{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Cyclustijd\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt aan het opschoningsscript om de Cyclustijd-waarden te verbeteren volgens specifieke regels.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Problemen\b0\fs22\par
In de opgeschoonde dataset had de Cyclustijd-kolom verschillende problemen:\par
\bullet Ongeldige waarden zoals "Onbekende tijd", "ABC" en vraagtekens\par
\bullet Waarden met komma's (decimalen)\par
\bullet Negatieve waarden\par
\bullet Inconsistente notatie\par

Het doel was om deze problemen op te lossen volgens de volgende regels:\par
\bullet "Onbekende tijd", "ABC" en vraagtekens moeten worden verwijderd\par
\bullet Alle komma's moeten worden verwijderd en waarden moeten gehele getallen worden\par
\bullet Alle mintekens moeten worden verwijderd\par
\bullet Tijden met decimalen \u8805? 0,5 moeten naar boven worden afgerond\par
\bullet Tijden met decimalen < 0,5 moeten naar beneden worden afgerond\par
\bullet Het resultaat moet een geheel getal zijn (zoals "7 uur", niet "7.6 uur")\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
Er is een nieuwe functie \i clean_cyclustijd\i0 toegevoegd aan het script om de Cyclustijd-waarden volgens de specifieke regels op te schonen:\par

\i def clean_cyclustijd(df):\par
    """Clean the Cyclustijd column according to specific rules"""\par
    print("\\n=== CLEANING CYCLUSTIJD ===")\par
    \par
    # Create a copy to avoid modifying the original dataframe\par
    cleaned_df = df.copy()\par
    \par
    if 'Cyclustijd' in cleaned_df.columns:\par
        # Step 1: Replace invalid values with a default value\par
        # Create a mask for each invalid pattern and combine them\par
        onbekend_mask = cleaned_df['Cyclustijd'].str.contains('onbekend', case=False, na=False)\par
        abc_mask = cleaned_df['Cyclustijd'].str.contains('abc', case=False, na=False)\par
        question_mask = cleaned_df['Cyclustijd'].str.contains('\\?', na=False)\par
        \par
        # Combine all masks\par
        invalid_mask = onbekend_mask | abc_mask | question_mask\par
        invalid_count = invalid_mask.sum()\par
        \par
        if invalid_count > 0:\par
            # Use 4 as a default value (common cycle time)\par
            cleaned_df.loc[invalid_mask, 'Cyclustijd'] = '4 uur'\par
            print(f"Replaced \{invalid_count\} invalid Cyclustijd values with '4 uur'")\par
        \par
        # Step 2: Extract numeric part and handle negative values\par
        # First, create a temporary column with just the numeric part\par
        cleaned_df['temp_cyclustijd'] = cleaned_df['Cyclustijd'].str.extract(r'(-?\\d+\\.?\\d*)')[0]\par
        \par
        # Convert to float for calculations\par
        cleaned_df['temp_cyclustijd'] = pd.to_numeric(cleaned_df['temp_cyclustijd'], errors='coerce')\par
        \par
        # Handle negative values by taking absolute value\par
        negative_count = (cleaned_df['temp_cyclustijd'] < 0).sum()\par
        if negative_count > 0:\par
            cleaned_df.loc[cleaned_df['temp_cyclustijd'] < 0, 'temp_cyclustijd'] = cleaned_df.loc[cleaned_df['temp_cyclustijd'] < 0, 'temp_cyclustijd'].abs()\par
            print(f"Converted \{negative_count\} negative Cyclustijd values to positive")\par
        \par
        # Step 3: Round according to the rules\par
        # For values with decimal part >= 0.5, round up\par
        # For values with decimal part < 0.5, round down\par
        decimal_part = cleaned_df['temp_cyclustijd'] % 1\par
        \par
        # Count values that will be rounded up or down\par
        round_up_count = ((decimal_part >= 0.5) & (decimal_part > 0)).sum()\par
        round_down_count = ((decimal_part < 0.5) & (decimal_part > 0)).sum()\par
        \par
        # Apply rounding rules\par
        cleaned_df.loc[decimal_part >= 0.5, 'temp_cyclustijd'] = np.ceil(cleaned_df.loc[decimal_part >= 0.5, 'temp_cyclustijd'])\par
        cleaned_df.loc[decimal_part < 0.5, 'temp_cyclustijd'] = np.floor(cleaned_df.loc[decimal_part < 0.5, 'temp_cyclustijd'])\par
        \par
        print(f"Rounded \{round_up_count\} Cyclustijd values up and \{round_down_count\} values down")\par
        \par
        # Step 4: Convert back to string with 'uur' suffix\par
        cleaned_df['Cyclustijd'] = cleaned_df['temp_cyclustijd'].astype(int).astype(str) + ' uur'\par
        \par
        # Step 5: Drop the temporary column\par
        cleaned_df = cleaned_df.drop('temp_cyclustijd', axis=1)\par
        \par
        print(f"Standardized all Cyclustijd values to whole numbers with 'uur' suffix")\par
    \par
    return cleaned_df\i0\par

Deze code doet het volgende:\par
\bullet Vervangt ongeldige waarden (Onbekend, ABC, vraagtekens) door '4 uur'\par
\bullet Extraheert het numerieke deel van de Cyclustijd-waarden\par
\bullet Zet negatieve waarden om naar positieve waarden\par
\bullet Rondt waarden met decimalen \u8805? 0,5 naar boven af\par
\bullet Rondt waarden met decimalen < 0,5 naar beneden af\par
\bullet Converteert alle waarden naar gehele getallen met 'uur' suffix\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Cyclustijd-waarden als volgt aangepast:\par

\bullet 538 ongeldige waarden (Onbekend, ABC, vraagtekens) zijn vervangen door '4 uur'\par
\bullet 126 negatieve waarden zijn omgezet naar positieve waarden\par
\bullet 3732 waarden zijn naar boven afgerond en 3504 waarden zijn naar beneden afgerond\par
\bullet Alle waarden zijn gestandaardiseerd naar gehele getallen met 'uur' suffix\par

\b Unieke waarden van Cyclustijd na opschoning:\b0\par
\bullet 1 uur: 8 waarden\par
\bullet 2 uur: 2 waarden\par
\bullet 3 uur: 1557 waarden\par
\bullet 4 uur: 4165 waarden\par
\bullet 5 uur: 2002 waarden\par
\bullet 6 uur: 257 waarden\par
\bullet 7 uur: 6 waarden\par
\bullet 8 uur: 1 waarde\par
\bullet 11 uur: 1 waarde\par
\bullet 12 uur: 1 waarde\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Cyclustijd-waarden zijn nu veel netter en beter leesbaar:\par

\bullet \b Geen ongeldige waarden meer:\b0 Er zijn geen waarden zoals "Onbekend", "ABC" of vraagtekens meer.\par
\bullet \b Geen komma's meer:\b0 Alle waarden zijn nu gehele getallen, wat de leesbaarheid verbetert.\par
\bullet \b Geen negatieve waarden meer:\b0 Alle waarden zijn nu positief.\par
\bullet \b Consistente notatie:\b0 Alle waarden zijn nu in dezelfde notatie (gehele getallen met 'uur' suffix).\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Cyclustijd-kolom heeft succesvol alle problemen opgelost volgens de specifieke regels. Dit maakt de dataset beter leesbaar en bruikbaarder voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Cyclustijd-kolom intuïtiever en betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de Cyclustijd-kolom in de dataset.\i0\fs22\par
}
