"""
Script om samenvattende gegevens uit de Kikker dataset te exporteren naar CSV voor Excel-visualisaties
"""

import pandas as pd
import numpy as np
import os

# Data inladen
print("Data inladen...")
try:
    df = pd.read_csv("Kikker_opgeschoond.csv")
    print("Opgeschoonde dataset geladen")
except:
    try:
        df = pd.read_csv("Kikker_cleaned.csv")
        print("Cleaned dataset geladen")
    except:
        df = pd.read_csv("Kikker.csv")
        print("Originele dataset geladen")

print(f"Dataset: {df.shape[0]} rijen, {df.shape[1]} kolommen")

# Zorg ervoor dat alle percentage-kolommen correct zijn geconverteerd
percentage_kolommen = ['Defectpercentage', 'Klantretourpercentage', 'Benuttingsgraad']
for kolom in percentage_kolommen:
    if kolom in df.columns:
        if df[kolom].dtype == 'object':
            # Verwijder % teken en converteer naar float
            df[kolom] = df[kolom].str.replace('%', '').astype(float) / 100
        # Zorg ervoor dat percentages tussen 0 en 1 liggen
        if df[kolom].max() > 1:
            # Als de waarden al als percentages zijn opgeslagen (bijv. 75 in plaats van 0.75)
            df[kolom] = df[kolom] / 100

# Extraheer numerieke waarden uit Cyclustijd
if 'Cyclustijd' in df.columns:
    df['Cyclustijd_num'] = df['Cyclustijd'].str.extract(r'(\d+\.?\d*)').astype(float)

# Extraheer numerieke waarden uit Energieverbruik
if 'Energieverbruik' in df.columns:
    df['Energieverbruik_num'] = df['Energieverbruik'].astype(str).str.extract(r'(\d+\.?\d*)').astype(float)

# Zorg ervoor dat Registratiedatum een datetime is
if 'Registratiedatum' in df.columns:
    df['Registratiedatum'] = pd.to_datetime(df['Registratiedatum'], errors='coerce')
    # Voeg kolommen toe voor dag, maand, jaar
    df['Dag'] = df['Registratiedatum'].dt.day
    df['Maand'] = df['Registratiedatum'].dt.month
    df['Jaar'] = df['Registratiedatum'].dt.year

# 1. Defectpercentage per machine
print("Berekenen defectpercentage per machine...")
if 'Defectpercentage' in df.columns and 'PackagingApparaat' in df.columns:
    defect_per_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean() * 100
    defect_per_machine = defect_per_machine.sort_values(ascending=False).reset_index()
    defect_per_machine.columns = ['PackagingApparaat', 'Defectpercentage']
    defect_per_machine.to_csv('excel_data/defect_per_machine.csv', index=False)
    print("Bestand opgeslagen: excel_data/defect_per_machine.csv")

# 2. Defectpercentage per dag
print("Berekenen defectpercentage per dag...")
if 'Defectpercentage' in df.columns and 'Registratiedatum' in df.columns:
    # Groepeer per datum en bereken gemiddeld defectpercentage
    defect_per_dag = df.groupby(df['Registratiedatum'].dt.date)['Defectpercentage'].mean() * 100
    defect_per_dag = defect_per_dag.reset_index()
    defect_per_dag.columns = ['Datum', 'Defectpercentage']
    defect_per_dag.to_csv('excel_data/defect_per_dag.csv', index=False)
    print("Bestand opgeslagen: excel_data/defect_per_dag.csv")

# 3. Cyclustijd per machine
print("Berekenen cyclustijd per machine...")
if 'Cyclustijd_num' in df.columns and 'PackagingApparaat' in df.columns:
    cyclus_per_machine = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean()
    cyclus_per_machine = cyclus_per_machine.reset_index()
    cyclus_per_machine.columns = ['PackagingApparaat', 'Cyclustijd']
    cyclus_per_machine.to_csv('excel_data/cyclustijd_per_machine.csv', index=False)
    print("Bestand opgeslagen: excel_data/cyclustijd_per_machine.csv")

# 4. Benuttingsgraad per machine
print("Berekenen benuttingsgraad per machine...")
if 'Benuttingsgraad' in df.columns and 'PackagingApparaat' in df.columns:
    benutting_per_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean() * 100
    benutting_per_machine = benutting_per_machine.reset_index()
    benutting_per_machine.columns = ['PackagingApparaat', 'Benuttingsgraad']
    benutting_per_machine.to_csv('excel_data/benutting_per_machine.csv', index=False)
    print("Bestand opgeslagen: excel_data/benutting_per_machine.csv")

# 5. Energieverbruik per machine
print("Berekenen energieverbruik per machine...")
if 'Energieverbruik_num' in df.columns and 'PackagingApparaat' in df.columns:
    energie_per_machine = df.groupby('PackagingApparaat')['Energieverbruik_num'].mean()
    energie_per_machine = energie_per_machine.reset_index()
    energie_per_machine.columns = ['PackagingApparaat', 'Energieverbruik']
    energie_per_machine.to_csv('excel_data/energie_per_machine.csv', index=False)
    print("Bestand opgeslagen: excel_data/energie_per_machine.csv")

# 6. Panel Test resultaten
print("Berekenen Panel Test resultaten...")
if 'Panel Test' in df.columns:
    panel_test = df['Panel Test'].value_counts()
    panel_test = panel_test.reset_index()
    panel_test.columns = ['Resultaat', 'Aantal']
    panel_test.to_csv('excel_data/panel_test.csv', index=False)
    print("Bestand opgeslagen: excel_data/panel_test.csv")

# 7. Defectpercentage per machine en koffieboon (voor Pareto)
print("Berekenen defectpercentage per machine en koffieboon...")
if 'Defectpercentage' in df.columns and 'PackagingApparaat' in df.columns and 'Koffieboon' in df.columns:
    # Maak een nieuwe kolom met de combinatie
    df['Machine_Boon_Combinatie'] = df['PackagingApparaat'] + ' - ' + df['Koffieboon']
    
    # Bereken gemiddeld defectpercentage per combinatie
    pareto_data = df.groupby('Machine_Boon_Combinatie')['Defectpercentage'].mean() * 100
    
    # Sorteer van hoog naar laag
    pareto_data = pareto_data.sort_values(ascending=False)
    pareto_data = pareto_data.reset_index()
    pareto_data.columns = ['Combinatie', 'Defectpercentage']
    pareto_data.to_csv('excel_data/pareto_data.csv', index=False)
    print("Bestand opgeslagen: excel_data/pareto_data.csv")

# 8. Alle machine gegevens in één bestand voor vergelijking
print("Combineren van alle machine gegevens...")
machine_data = pd.DataFrame()
machine_data['PackagingApparaat'] = defect_per_machine['PackagingApparaat']
machine_data['Defectpercentage'] = defect_per_machine['Defectpercentage']
machine_data['Cyclustijd'] = cyclus_per_machine['Cyclustijd']
machine_data['Benuttingsgraad'] = benutting_per_machine['Benuttingsgraad']
machine_data['Energieverbruik'] = energie_per_machine['Energieverbruik']
machine_data.to_csv('excel_data/machine_vergelijking.csv', index=False)
print("Bestand opgeslagen: excel_data/machine_vergelijking.csv")

print("Alle bestanden zijn opgeslagen in de map 'excel_data'")
