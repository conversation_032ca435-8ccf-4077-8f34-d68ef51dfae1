import pandas as pd
import random

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon het aantal nulwaarden in Klantretourpercentage
zero_count = (df['Klantretourpercentage'] == 0).sum()
print(f"Aantal nulwaarden in Klantretourpercentage: {zero_count}")

if zero_count > 0:
    # Bereken statistieken van niet-nulwaarden
    non_zero_values = df.loc[df['Klantretourpercentage'] > 0, 'Klantretourpercentage']
    p10 = non_zero_values.quantile(0.10)
    p25 = non_zero_values.quantile(0.25)

    # Vervang nulwaarden door willekeurige waarden tussen p10 en p25
    random.seed(42)  # Voor reproduceerbaarheid
    replacement_values = [random.uniform(p10, p25) for _ in range(zero_count)]

    # Vervang nulwaarden
    df.loc[df['Klantretourpercentage'] == 0, 'Klantretourpercentage'] = replacement_values

    print(f"Vervangen {zero_count} nulwaarden door willekeurige waarden tussen {p10:.4f} en {p25:.4f}")

    # Controleer of er nog nulwaarden zijn
    zero_count_after = (df['Klantretourpercentage'] == 0).sum()
    print(f"Aantal nulwaarden na vervanging: {zero_count_after}")

    # Sla de aangepaste dataset op
    df.to_csv('Kikker_cleaned_fixed.csv', index=False)
    print("Aangepaste dataset opgeslagen als 'Kikker_cleaned_fixed.csv'")
else:
    print("Geen nulwaarden gevonden in Klantretourpercentage")
