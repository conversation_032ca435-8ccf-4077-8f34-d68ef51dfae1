{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Duurzaamheid Score\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt aan het opschoningsscript om de lege waarden (blanks) in de Duurzaamheid Score-kolom te filteren en te vervangen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset waren er 135 lege waarden (NaN of blanks) in de Duurzaamheid Score-kolom. Deze lege waarden kunnen analyses verstoren en leiden tot onvolledige resultaten. Het doel was om deze lege waarden te vervangen door een geschikte waarde, zodat de kolom volledig en bruikbaar is voor analyses.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
In de functie \i fix_unrealistic_values\i0 is extra code toegevoegd voor de Duurzaamheid Score-kolom om lege waarden te identificeren en te vervangen:\par

\i # Also check for NaN values and replace them\par
nan_count = cleaned_df[col].isna().sum()\par
if nan_count > 0:\par
    # Replace NaN values with the median of non-NaN values\par
    median_value = cleaned_df[col].dropna().median()\par
    cleaned_df[col] = cleaned_df[col].fillna(median_value)\par
    print(f"Fixed \{nan_count\} blank values in '\{col\}' by replacing with median: \{median_value\}")\i0\par

Deze code doet het volgende:\par
\bullet Telt het aantal lege waarden (NaN) in de Duurzaamheid Score-kolom\par
\bullet Berekent de mediaan van de niet-lege waarden\par
\bullet Vervangt de lege waarden door deze mediaan\par
\bullet Rapporteert het aantal vervangen waarden en de gebruikte mediaan\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Duurzaamheid Score-waarden als volgt aangepast:\par

\bullet 135 lege waarden (blanks) in 'Duurzaamheid Score' zijn vervangen door de mediaan: 50.0\par

\b Statistieken van de aangepaste Duurzaamheid Score-kolom:\b0\par
\bullet Minimum: 0.0\par
\bullet Maximum: 100.0\par
\bullet Gemiddelde: 49.95\par
\bullet Mediaan: 50.0\par
\bullet 25e percentiel: 39.0\par
\bullet 75e percentiel: 60.0\par
\bullet Standaarddeviatie: 17.03\par

\b Verdeling van Duurzaamheid Score (top 10):\b0\par
\bullet 50.0: 1025 waarden (inclusief 135 vervangen waarden)\par
\bullet 39.0: 400 waarden\par
\bullet 60.0: 399 waarden\par
\bullet 55.0: 398 waarden\par
\bullet 45.0: 397 waarden\par
\bullet 40.0: 396 waarden\par
\bullet 58.0: 395 waarden\par
\bullet 42.0: 394 waarden\par
\bullet 56.0: 393 waarden\par
\bullet 61.0: 392 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Duurzaamheid Score-waarden zijn nu volledig en bruikbaar voor analyses:\par

\bullet \b Geen lege waarden meer:\b0 Er zijn geen lege waarden (blanks) meer in de kolom.\par
\bullet \b Consistente waarden:\b0 Alle waarden zijn nu tussen 0 en 100, wat een realistische schaal is voor duurzaamheidsscores.\par
\bullet \b Volledige dataset:\b0 De kolom bevat nu 8000 waarden (voorheen 7865), wat betekent dat alle rijen een geldige Duurzaamheid Score-waarde hebben.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Duurzaamheid Score-kolom heeft succesvol alle lege waarden vervangen door de mediaan van de niet-lege waarden. Dit maakt de dataset volledig en bruikbaar voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Duurzaamheid Score-kolom betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de Duurzaamheid Score-kolom in de dataset.\i0\fs22\par
}
