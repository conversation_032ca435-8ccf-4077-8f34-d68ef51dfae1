Option Explicit

' Script om grafieken toe te voegen aan bestaande Excel-bestanden

' Constanten voor grafiektypen
Const xlColumnClustered = 51 ' Gegroepeerde kolomgrafiek
Const xlPie = 5 ' Cirkeldiagram

Sub Main()
    ' Maak een Excel-applicatie
    Dim excel
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Verwerk de bestanden
    AddChartsToExcel "Kaizen_Analyse_Clean.xlsx", excel
    AddChartsToExcel "Lean_Analyse_Clean.xlsx", excel
    AddChartsToExcel "TOC_Analyse_Clean.xlsx", excel
    AddChartsToExcel "SixSigma_Analyse_Clean.xlsx", excel
    
    MsgBox "Grafieken zijn toegevoegd aan alle Excel-bestanden."
End Sub

' Functie om grafieken toe te voegen aan een Excel-bestand
Sub AddChartsToExcel(excelFile, excel)
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of het bestand bestaat
    If Not fso.FileExists(excelFile) Then
        MsgBox "Het bestand " & excelFile & " bestaat niet."
        Exit Sub
    End If
    
    ' Open het Excel-bestand
    Dim wb
    Set wb = excel.Workbooks.Open(excelFile)
    
    ' Bepaal het type analyse
    Dim analysisType
    If InStr(excelFile, "Kaizen") > 0 Then
        analysisType = "kaizen"
    ElseIf InStr(excelFile, "Lean") > 0 Then
        analysisType = "lean"
    ElseIf InStr(excelFile, "TOC") > 0 Then
        analysisType = "toc"
    ElseIf InStr(excelFile, "SixSigma") > 0 Then
        analysisType = "sixsigma"
    Else
        analysisType = ""
    End If
    
    ' Maak grafieken op basis van het type analyse
    If analysisType <> "" Then
        CreateChartsForAnalysis wb.Sheets(1), analysisType
    End If
    
    ' Sla het bestand op
    wb.Save
    
    ' Sluit het werkboek
    wb.Close
End Sub

' Functie om grafieken te maken voor een analyse
Sub CreateChartsForAnalysis(ws, analysisType)
    ' Zoek de laatste rij met data
    Dim lastRow
    lastRow = ws.UsedRange.Rows.Count
    
    ' Voeg een lege rij toe voor de grafieken
    lastRow = lastRow + 2
    ws.Cells(lastRow, 1).Value = "Visualisaties:"
    ws.Cells(lastRow, 1).Font.Bold = True
    lastRow = lastRow + 2
    
    ' Maak grafieken op basis van het analysetype
    Select Case analysisType
        Case "kaizen"
            ' Zoek data voor Panel Test Resultaten
            Dim panelTestRange
            Set panelTestRange = FindDataRange(ws, "Panel Test", "Voldoet")
            
            If Not panelTestRange Is Nothing Then
                ' Maak een cirkeldiagram
                CreatePieChart ws, panelTestRange, "Panel Test Resultaten", lastRow
                lastRow = lastRow + 20
            End If
            
            ' Zoek data voor Klanttevredenheid
            Dim klanttevredenheidRange
            Set klanttevredenheidRange = FindDataRange(ws, "Klanttevredenheid", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klanttevredenheidRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, klanttevredenheidRange, "Klanttevredenheid per Koffieboon Type", lastRow
                lastRow = lastRow + 20
            End If
            
            ' Zoek data voor Klantretourpercentage
            Dim klantretourRange
            Set klantretourRange = FindDataRange(ws, "Klantretourpercentage", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klantretourRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, klantretourRange, "Klantretourpercentage per Koffieboon Type", lastRow
                lastRow = lastRow + 20
            End If
            
        Case "lean"
            ' Zoek data voor Benuttingsgraad
            Dim benuttingRange
            Set benuttingRange = FindDataRange(ws, "Benuttingsgraad", "Packager")
            
            If Not benuttingRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, benuttingRange, "Benuttingsgraad per Verpakkingsmachine", lastRow
                lastRow = lastRow + 20
            End If
            
            ' Zoek data voor Voorraadniveaus
            Dim voorraadRange
            Set voorraadRange = FindDataRange(ws, "Voorraadniveaus", "Gemiddelde|Mediaan")
            
            If Not voorraadRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, voorraadRange, "Voorraadniveaus Statistieken", lastRow
                lastRow = lastRow + 20
            End If
            
        Case "sixsigma"
            ' Zoek data voor Top 10 batches met hoogste defectpercentage
            Dim batchRange
            Set batchRange = FindDataRange(ws, "batch", "Batch")
            
            If Not batchRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, batchRange, "Top 10 Batches met Hoogste Defectpercentage", lastRow
                lastRow = lastRow + 20
            End If
            
            ' Zoek data voor Process Capability
            Dim capabilityRange
            Set capabilityRange = FindDataRange(ws, "capability", "Cp|Cpk")
            
            If Not capabilityRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, capabilityRange, "Process Capability Analyse", lastRow
                lastRow = lastRow + 20
            End If
            
        Case "toc"
            ' Zoek data voor Procestijd Analyse
            Dim procestijdRange
            Set procestijdRange = FindDataRange(ws, "Procestijd", "Grinding|Filling|Packaging")
            
            If Not procestijdRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, procestijdRange, "Procestijd Analyse", lastRow
                lastRow = lastRow + 20
            End If
            
            ' Zoek data voor Energieverbruik per Verpakkingsmachine
            Dim energieRange
            Set energieRange = FindDataRange(ws, "Energieverbruik", "Packager|Onbekend")
            
            If Not energieRange Is Nothing Then
                ' Maak een staafdiagram
                CreateColumnChart ws, energieRange, "Energieverbruik per Verpakkingsmachine", lastRow
                lastRow = lastRow + 20
            End If
    End Select
End Sub

' Functie om een databereik te vinden op basis van zoektermen
Function FindDataRange(ws, sectionKeyword, dataKeywords)
    Dim row, lastRow, startRow, endRow, dataFound
    Dim keywordArray, keyword, i
    
    lastRow = ws.UsedRange.Rows.Count
    dataFound = False
    startRow = 0
    endRow = 0
    
    ' Split de dataKeywords op |
    keywordArray = Split(dataKeywords, "|")
    
    ' Zoek naar de sectie
    For row = 1 To lastRow
        ' Controleer of de cel de sectie-keyword bevat
        If Not IsEmpty(ws.Cells(row, 1).Value) Then
            If InStr(1, ws.Cells(row, 1).Value, sectionKeyword, vbTextCompare) > 0 Then
                ' Zoek naar de data binnen deze sectie
                startRow = row
                
                ' Zoek naar het einde van de sectie (lege regel of nieuwe sectie)
                For i = row + 1 To lastRow
                    If IsEmpty(ws.Cells(i, 1).Value) Then
                        endRow = i - 1
                        Exit For
                    End If
                    
                    ' Controleer of deze rij data bevat die we zoeken
                    For Each keyword In keywordArray
                        If InStr(1, ws.Cells(i, 1).Value, keyword, vbTextCompare) > 0 Then
                            dataFound = True
                            Exit For
                        End If
                    Next
                Next
                
                ' Als we het einde van het werkblad hebben bereikt
                If endRow = 0 And i > lastRow Then
                    endRow = lastRow
                End If
                
                ' Als we data hebben gevonden, stop met zoeken
                If dataFound Then
                    Exit For
                Else
                    ' Reset en zoek verder
                    startRow = 0
                    endRow = 0
                End If
            End If
        End If
    Next
    
    ' Als we data hebben gevonden, maak een bereik
    If dataFound And startRow > 0 And endRow > 0 Then
        ' Maak het bereik
        Set FindDataRange = ws.Range(ws.Cells(startRow, 1), ws.Cells(endRow, 2))
    Else
        Set FindDataRange = Nothing
    End If
End Function

' Functie om een cirkeldiagram te maken
Sub CreatePieChart(ws, dataRange, chartTitle, startRow)
    ' Maak een nieuw grafiekobject
    Dim chartObj
    Set chartObj = ws.Shapes.AddChart2(227, xlPie)
    
    ' Stel de databron in
    chartObj.Chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chartObj.Chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .HasLegend = True
        .Legend.Position = 7 ' xlLegendPositionBottom
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 400
    chartObj.Height = 300
    chartObj.Top = ws.Cells(startRow, 1).Top
    chartObj.Left = ws.Cells(startRow, 1).Left
End Sub

' Functie om een staafdiagram te maken
Sub CreateColumnChart(ws, dataRange, chartTitle, startRow)
    ' Maak een nieuw grafiekobject
    Dim chartObj
    Set chartObj = ws.Shapes.AddChart2(227, xlColumnClustered)
    
    ' Stel de databron in
    chartObj.Chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chartObj.Chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .HasLegend = False
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = ws.Cells(startRow, 1).Top
    chartObj.Left = ws.Cells(startRow, 1).Left
End Sub

' Start het script
Call Main()
