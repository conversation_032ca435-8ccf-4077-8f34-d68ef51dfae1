Option Explicit

' Sc<PERSON>t om één voor één CSV-bestanden te importeren in Excel

Sub Main()
    Dim excel, wb, ws, fso
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Set wb = excel.Workbooks.Add
    Set ws = wb.Sheets(1)
    ws.Name = "Kikker_cleaned"
    
    ' Importeer Kikker_cleaned.csv
    If fso.FileExists("Kikker_cleaned.csv") Then
        WScript.Echo "Importeren van <PERSON>_cleaned.csv..."
        ImportCSV "Kikker_cleaned.csv", ws
    Else
        WScript.Echo "Het bestand Kikker_cleaned.csv bestaat niet."
    End If
    
    ' Voeg een werkblad toe voor Lean_Analyse_Clean.csv
    If fso.FileExists("Lean_Analyse_Clean.csv") Then
        WScript.Echo "Importeren van Lean_Analyse_Clean.csv..."
        wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
        Set ws = wb.Sheets(wb.Sheets.Count)
        ws.Name = "Lean_Analyse_Clean"
        ImportCSV "Lean_Analyse_Clean.csv", ws
    End If
    
    ' Voeg een werkblad toe voor Kaizen_Analyse_Clean.csv
    If fso.FileExists("Kaizen_Analyse_Clean.csv") Then
        WScript.Echo "Importeren van Kaizen_Analyse_Clean.csv..."
        wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
        Set ws = wb.Sheets(wb.Sheets.Count)
        ws.Name = "Kaizen_Analyse_Clean"
        ImportCSV "Kaizen_Analyse_Clean.csv", ws
    End If
    
    ' Voeg een werkblad toe voor TOC_Analyse_Clean.csv
    If fso.FileExists("TOC_Analyse_Clean.csv") Then
        WScript.Echo "Importeren van TOC_Analyse_Clean.csv..."
        wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
        Set ws = wb.Sheets(wb.Sheets.Count)
        ws.Name = "TOC_Analyse_Clean"
        ImportCSV "TOC_Analyse_Clean.csv", ws
    End If
    
    ' Sla het Excel-bestand op
    wb.SaveAs "Kikker_Analyses_Gecombineerd.xlsx"
    
    WScript.Echo "Bestand opgeslagen als Kikker_Analyses_Gecombineerd.xlsx"
End Sub

' Functie om een CSV-bestand te importeren in een werkblad
Sub ImportCSV(csvFile, ws)
    Dim fso, file, line, row, col, values, separator
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(csvFile, 1)
    
    row = 1
    
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        If Trim(line) <> "" Then
            ' Bepaal het scheidingsteken
            If InStr(line, vbTab) > 0 Then
                separator = vbTab
            Else
                separator = ","
            End If
            
            ' Split de regel
            values = Split(line, separator)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                ws.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
    
    ' Pas de kolombreedtes aan
    ws.Columns.AutoFit
End Sub

' Start het script
Call Main()
