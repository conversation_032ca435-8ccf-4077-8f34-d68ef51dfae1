# Dashboard Screen - EuroCaps Order Management App

## Screen Layout (PowerApps Components - Interactive Dashboard)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management    [🔔3] [<PERSON> ▼] [⚙️]      |
+---------------------------------------------------------------+
| SIDEBAR MENU  | Dashboard - Welcome Operations Manager        |
|               |                                               |
| 🏠 Dashboard  | DASHBOARD FILTERS & CONTROLS                  |
| 👥 Customers  | ComboBox1: [📅 Last 30 Days ▼] Button1: [🔄] |
| 📦 Products   | Dropdown1: [All Customers ▼] Button2: [📊]   |
| 📋 Orders     |                                               |
| 📊 Reports    | INTERACTIVE KPI TILES (Clickable Buttons)    |
| 🔔 Messages   | ┌─────────────┬─────────────┬─────────────┐   |
| 👤 Users      | │Button3      │Button4      │Button5      │   |
| ⚙️ Settings   | │📊 TOTAL     │⚡ ACTIVE     │⏳ PENDING   │   |
| 🚪 Logout     | │ORDERS       │ORDERS       │ORDERS       │   |
|               | │Label1: 245  │Label2: 18   │Label3: 7    │   |
|               | │This Month   │In Progress  │Awaiting     │   |
|               | │Icon1: ↗️     │Icon2: ↗️     │Icon3: ↘️     │   |
|               | │+12.5%       │+8.2%        │-2.1%        │   |
|               | └─────────────┴─────────────┴─────────────┘   |
|               |                                               |
|               | ┌─────────────┬─────────────┬─────────────┐   |
|               | │Button6      │Button7      │Button8      │   |
|               | │✅ CONFIRMED │🚚 SHIPPED   │📦 DELIVERED │   |
|               | │ORDERS       │ORDERS       │TODAY        │   |
|               | │Label4: 15   │Label5: 42   │Label6: 8    │   |
|               | │Ready        │In Transit   │Completed    │   |
|               | │Icon4: ↗️     │Icon5: ↗️     │Icon6: ↗️     │   |
|               | │+5.3%        │+15.2%       │+3.1%        │   |
|               | └─────────────┴─────────────┴─────────────┘   |
|               |                                               |
|               | RECENT ORDERS - INTERACTIVE GALLERY          |
|               | Dropdown2: [All Status ▼] Button9: [View All]|
|               | Gallery1 (Vertical Gallery - Clickable Rows) |
|               | ┌──────────────────────────────────────────┐  |
|               | │Template1 (Clickable Order Row)           │  |
|               | │Label7: ORD-1089 │Label8: Coffee World    │  |
|               | │Label9: 15/01    │Label10: €164          │  |
|               | │Icon7: 🟠 New    │Button10: [👁️ View]    │  |
|               | ├──────────────────────────────────────────┤  |
|               | │Template2 (Clickable Order Row)           │  |
|               | │Label11: ORD-1088│Label12: Bean Lovers   │  |
|               | │Label13: 14/01   │Label14: €108          │  |
|               | │Icon8: 🔵 Proc   │Button11: [👁️ View]    │  |
|               | └──────────────────────────────────────────┘  |
|               |                                               |
|               | QUICK ACTIONS - NAVIGATION BUTTONS           |
|               | Container1 (Horizontal Layout)               |
|               | [Button12: 🛒 NEW ORDER] [Button13: 👥 CUST] |
|               | [Button14: 📦 PROD] [Button15: 📋 HISTORY]   |
|               |                                               |
+---------------------------------------------------------------+
```

## PowerApps Components Specification

### **1. Filter & Control Section:**

**ComboBox1 (Date Range Selector):**
```powerapps
ComboBox1
- Items: ["Today", "Last 7 Days", "Last 30 Days", "Last 90 Days", "This Month", "Last Month"]
- Default: "Last 30 Days"
- OnChange: Set(DateFilter, ComboBox1.Selected.Value); UpdateDashboard()
```

**Dropdown1 (Customer Filter):**
```powerapps
Dropdown1
- Items: ["All Customers"].Concat(Distinct(Orders, Customer))
- Default: "All Customers"
- OnChange: Set(CustomerFilter, Dropdown1.Selected.Value); UpdateDashboard()
```

**Button1 (Refresh):**
```powerapps
Button1
- Text: "🔄 Refresh"
- OnSelect: Refresh(Orders); Refresh(Customers); Refresh(Products); UpdateDashboard()
```

### **2. Interactive KPI Tiles (Clickable Buttons):**

**Button3 (Total Orders KPI):**
```powerapps
Button3
- Fill: RGBA(255, 255, 255, 1)
- BorderColor: RGBA(0, 120, 212, 1)
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "All"})
- Contains:
  - Label1: CountRows(FilteredOrders)
  - Icon1: Icon.TrendingUp (conditional based on trend)
```

**Button4 (Active Orders KPI):**
```powerapps
Button4
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "Active"})
- Contains:
  - Label2: CountRows(Filter(Orders, Status in ["New", "Processing"]))
  - Icon2: Icon.Lightning
```

**Button5 (Pending Orders KPI):**
```powerapps
Button5
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "Pending"})
- Contains:
  - Label3: CountRows(Filter(Orders, Status = "New"))
  - Icon3: Icon.Clock
```

**Button6-8 (Additional KPI Tiles):**
```powerapps
Button6 (Confirmed Orders)
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "Confirmed"})

Button7 (Shipped Orders)
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "Shipped"})

Button8 (Delivered Today)
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: "DeliveredToday"})
```

### **3. Recent Orders Interactive Gallery:**

**Dropdown2 (Order Status Filter):**
```powerapps
Dropdown2
- Items: ["All Status", "New", "Processing", "Shipped", "Delivered", "Cancelled"]
- Default: "All Status"
- OnChange: Set(StatusFilter, Dropdown2.Selected.Value)
```

**Gallery1 (Recent Orders):**
```powerapps
Gallery1 (Vertical Gallery)
- Items: FirstN(
    SortByColumns(
      Filter(Orders,
        (StatusFilter = "All Status" || Status = StatusFilter) &&
        (CustomerFilter = "All Customers" || Customer = CustomerFilter)
      ),
      "OrderDate", Descending
    ), 5)
- TemplatePadding: 5
- TemplateSize: 70
- OnSelect: Navigate(OrderDetailScreen, ScreenTransition.Fade, {OrderID: ThisItem.OrderID})

Template Contains:
- Label7: ThisItem.OrderID
- Label8: LookUp(Customers, CustomerID = ThisItem.CustomerID, CompanyName)
- Label9: Text(ThisItem.OrderDate, "dd/mm")
- Label10: "€" & Text(ThisItem.Total, "0.00")
- Icon7: Status indicator (conditional formatting)
- Button10: View button for quick access
```

### **4. Quick Actions Navigation:**

**Container1 (Action Buttons Layout):**
```powerapps
Container1
- LayoutDirection: LayoutDirection.Horizontal
- LayoutJustifyContent: LayoutJustifyContent.SpaceEvenly
- LayoutAlignItems: LayoutAlignItems.Center
```

**Navigation Buttons:**
```powerapps
Button12 (New Order)
- Text: "🛒 NEW ORDER"
- Fill: RGBA(0, 120, 212, 1)
- OnSelect: Navigate(NewOrderScreen, ScreenTransition.Fade)

Button13 (Customers)
- Text: "👥 CUSTOMERS"
- OnSelect: Navigate(CustomerListScreen, ScreenTransition.Fade)

Button14 (Products)
- Text: "📦 PRODUCTS"
- OnSelect: Navigate(ProductCatalogScreen, ScreenTransition.Fade)

Button15 (Order History)
- Text: "📋 HISTORY"
- OnSelect: Navigate(OrderHistoryScreen, ScreenTransition.Fade)
```

### **5. Key Formulas & Data Logic:**

**Dashboard Data Filtering:**
```powerapps
// Set on screen load
Set(FilteredOrders,
  Filter(Orders,
    // Date filter
    Switch(DateFilter,
      "Today", OrderDate = Today(),
      "Last 7 Days", OrderDate >= DateAdd(Today(), -7, Days),
      "Last 30 Days", OrderDate >= DateAdd(Today(), -30, Days),
      "Last 90 Days", OrderDate >= DateAdd(Today(), -90, Days),
      "This Month", Month(OrderDate) = Month(Today()) && Year(OrderDate) = Year(Today()),
      "Last Month", Month(OrderDate) = Month(DateAdd(Today(), -1, Months)) && Year(OrderDate) = Year(Today())
    ) &&
    // Customer filter
    (CustomerFilter = "All Customers" || Customer = CustomerFilter)
  )
)
```

**KPI Calculations:**
```powerapps
// Total Orders
CountRows(FilteredOrders)

// Active Orders
CountRows(Filter(FilteredOrders, Status in ["New", "Processing"]))

// Revenue This Period
Sum(FilteredOrders, Total)

// Trend Calculation (compare with previous period)
Set(PreviousPeriodOrders, Filter(Orders, [Previous Period Logic]))
Set(TrendPercentage,
  (CountRows(FilteredOrders) - CountRows(PreviousPeriodOrders)) /
  CountRows(PreviousPeriodOrders) * 100
)
```

### **6. Interactive Features:**

1. **Clickable KPI Tiles**: Each KPI button navigates to filtered views
2. **Dynamic Filtering**: ComboBox and Dropdown update all dashboard data
3. **Order Row Selection**: Click any order row to view details
4. **Real-time Updates**: Refresh button updates all data sources
5. **Context Navigation**: Pass filter context to target screens

### **7. Visual Feedback:**
- **Hover Effects**: Buttons change color on hover
- **Loading States**: Show spinner during data refresh
- **Status Colors**: Conditional formatting for order status
- **Trend Indicators**: Up/down arrows with percentage changes

## PowerApps Components Specification

### **PowerApps Controls Used:**

1. **Dropdown Controls:**
   - **Date Filter Dropdown**: `Dropdown1`
     - Items: `["Today", "Last 7 Days", "Last 30 Days", "Last 90 Days", "Custom Range"]`
     - Data Source: Static list
   - **Status Filter Dropdown**: `Dropdown2`
     - Items: `Distinct(Orders, Status)`
     - Data Source: Orders.xlsx (Status column)

2. **Button Controls:**
   - **Refresh Button**: `Button1` - OnSelect: `Refresh(Orders)`
   - **View All Button**: `Button2` - OnSelect: `Navigate(OrderHistoryScreen)`
   - **Quick Action Buttons**: `Button3-Button7` - Navigate to respective screens

3. **Label Controls for KPI Display:**
   - **Total Orders Label**: `Label1` - Text: `CountRows(Filter(Orders, OrderDate >= DateAdd(Today(), -30, Days)))`
   - **Active Orders Label**: `Label2` - Text: `CountRows(Filter(Orders, Status in ["New", "Processing"]))`
   - **Pending Orders Label**: `Label3` - Text: `CountRows(Filter(Orders, Status = "New"))`

4. **Gallery Control for Recent Orders:**
   - **Recent Orders Gallery**: `Gallery1` (Vertical Gallery)
     - Items: `FirstN(SortByColumns(Orders, "OrderDate", Descending), 5)`
     - Data Source: Orders.xlsx joined with Customers.xlsx
     - Template: Contains Labels for Order#, Customer, Date, Status

5. **Rectangle Controls for KPI Containers:**
   - **KPI Container 1**: `Rectangle1` - Contains Total Orders info
   - **KPI Container 2**: `Rectangle2` - Contains Active Orders info
   - **KPI Container 3**: `Rectangle3` - Contains Pending Orders info
   - **KPI Container 4**: `Rectangle4` - Contains Confirmed Orders info
   - **KPI Container 5**: `Rectangle5` - Contains Shipped Orders info
   - **KPI Container 6**: `Rectangle6` - Contains Delivered Orders info

6. **Icon Controls:**
   - **Menu Icon**: `Icon1` (Hamburger) - OnSelect: Toggle menu visibility
   - **Notification Icon**: `Icon2` (Bell) - OnSelect: `Navigate(NotificationsScreen)`
   - **Settings Icon**: `Icon3` (Settings) - OnSelect: `Navigate(SettingsScreen)`
   - **Trend Icons**: `Icon4-Icon9` (Arrow up/down) - Show trend direction

### **Data Connections:**
- **Orders Data Source**: Excel file connection to Orders.xlsx
- **Customers Data Source**: Excel file connection to Customers.xlsx
- **Users Data Source**: Excel file connection to Users.xlsx

### **Formulas Used:**
- **Total Orders This Month**: `CountRows(Filter(Orders, OrderDate >= DateAdd(Today(), -30, Days)))`
- **Active Orders**: `CountRows(Filter(Orders, Status in ["New", "Processing"]))`
- **Recent Orders**: `FirstN(SortByColumns(Orders, "OrderDate", Descending), 5)`
- **Customer Name Lookup**: `LookUp(Customers, CustomerID = ThisItem.CustomerID, CompanyName)`

### **Navigation:**
- **Screen Navigation**: Use `Navigate()` function for all menu items and buttons
- **Context Variables**: Set variables for selected filters using `Set()` function

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Cards: White (#ffffff)
- Status indicators:
  - New: Orange (#ff9800)
  - Processing: Blue (#4a6fa5)
  - Shipped: Purple (#9c27b0)
  - Delivered: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Card titles: Arial, 14pt, Bold, Dark gray
- Card values: Arial, 24pt, Bold, Status color
- Table headers: Arial, 12pt, Bold
- Table content: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Current page highlighted
   - Icons for each menu item

3. **Status Cards**
   - Four cards showing order counts by status
   - Each with distinct color coding
   - Large numbers for quick visibility
   - Clickable to filter order list by status

4. **Recent Orders Table**
   - Sortable columns
   - Status indicated by color
   - Clickable rows to view order details
   - Limited to 5 most recent orders

5. **Quick Actions**
   - Button group for common tasks
   - Prominent "New Order" button
   - Secondary action buttons

## Interactions

1. **Menu Navigation**
   - Click on menu items to navigate to different screens
   - Current screen highlighted in menu

2. **Status Cards**
   - Click on card to view filtered list of orders by that status

3. **Recent Orders**
   - Click on order row to navigate to Order Details screen
   - Hover effect on rows
   - Sort by clicking column headers

4. **Quick Actions**
   - "New Order" button navigates to New Order screen
   - "View Customers" navigates to Customer List screen
   - "View Products" navigates to Product Catalog screen

5. **User Menu**
   - Click on username to show dropdown
   - Options: Profile, Preferences, Logout

## Accessibility Considerations
- Clear visual hierarchy
- Consistent navigation patterns
- Color is not the only indicator of status (text labels included)
- Sufficient contrast for all text elements

## Notes for Implementation
- Dashboard should refresh automatically every few minutes
- Consider adding notifications area for system messages
- For prototype: Use mock data for all metrics and tables
