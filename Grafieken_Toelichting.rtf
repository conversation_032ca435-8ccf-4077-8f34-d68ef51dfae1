{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 Toelichting Grafieken Productieproces Americaps\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. Defectpercentage per Machine (defect_per_machine.png)\b0\fs22\par
Deze grafiek toont het gemiddelde defectpercentage voor elke verpakkingsmachine. De machines zijn gerangschikt van hoogste naar laagste defectpercentage.\par
\bullet \b Belangrijkste bevindingen:\b0\par
   - Packager 1 heeft het hoogste defectpercentage (2,05%)\par
   - Packager 4 heeft het laagste defectpercentage (2,00%)\par
   - Het verschil tussen de beste en slechtste machine is relatief klein (0,05%)\par
\bullet \b Implicaties:\b0\par
   - Hoewel de verschillen klein zijn, kan een verbetering van 0,05% bij grote productievolumes significant zijn\par
   - Focus verbeterinspanningen op Packager 1 en Packager 5\par

\pard\sa200\sl276\slmult1\b\fs24 2. Cyclustijd per Machine (cyclustijd_per_machine.png)\b0\fs22\par
Deze grafiek toont de gemiddelde cyclustijd (in uren) voor elke verpakkingsmachine. De machines zijn gerangschikt van langste naar kortste cyclustijd.\par
\bullet \b Belangrijkste bevindingen:\b0\par
   - Packager 4 heeft de langste cyclustijd (4,13 uur)\par
   - Packager 1 heeft ook een lange cyclustijd (4,13 uur)\par
   - Onbekende machines hebben de kortste cyclustijd (4,04 uur)\par
\bullet \b Implicaties:\b0\par
   - Packager 4 en Packager 1 zijn potentiële bottlenecks in het productieproces\par
   - Zelfs kleine verbeteringen in cyclustijd kunnen de totale productiecapaciteit verhogen\par

\pard\sa200\sl276\slmult1\b\fs24 3. Benuttingsgraad per Machine (benutting_per_machine.png)\b0\fs22\par
Deze grafiek toont de gemiddelde benuttingsgraad (in procenten) voor elke verpakkingsmachine. De machines zijn gerangschikt van laagste naar hoogste benuttingsgraad.\par
\bullet \b Belangrijkste bevindingen:\b0\par
   - Packager 1 heeft de laagste benuttingsgraad (74,59%)\par
   - Packager 3 heeft de hoogste benuttingsgraad (75,33%)\par
   - Alle machines hebben een benuttingsgraad tussen 74,5% en 75,5%\par
\bullet \b Implicaties:\b0\par
   - Er is ruimte voor verbetering in de benuttingsgraad van alle machines\par
   - Packager 1 verdient speciale aandacht (laagste benuttingsgraad én hoogste defectpercentage)\par

\pard\sa200\sl276\slmult1\b\fs24 4. Pareto-analyse Defecten (pareto_defecten.png)\b0\fs22\par
Deze grafiek toont een Pareto-analyse van de combinaties van machines en koffieboontypen met het hoogste defectpercentage. De top 10 combinaties zijn gerangschikt van hoogste naar laagste defectpercentage.\par
\bullet \b Belangrijkste bevindingen:\b0\par
   - Packager 5 met Excelsa heeft het hoogste defectpercentage (2,15%)\par
   - Packager 2 met Liberica heeft het op één na hoogste defectpercentage (2,11%)\par
   - Meerdere combinaties met Excelsa en Liberica bonen komen voor in de top 10\par
\bullet \b Implicaties:\b0\par
   - Focus op de verwerking van Excelsa en Liberica bonen kan de grootste kwaliteitsverbetering opleveren\par
   - Specifieke machine-boon combinaties vereisen gerichte verbeteringsacties\par

\pard\sa200\sl276\slmult1\b\fs24 5. Correlatieheatmap (correlatie_heatmap.png)\b0\fs22\par
Deze heatmap toont de correlaties tussen verschillende kwaliteitsaspecten van het productieproces. Sterke positieve correlaties zijn rood, sterke negatieve correlaties zijn blauw, en zwakke correlaties zijn wit.\par
\bullet \b Belangrijkste bevindingen:\b0\par
   - Er zijn geen sterke correlaties (>0,3 of <-0,3) gevonden tussen de kwaliteitsaspecten\par
   - De meeste kwaliteitsaspecten lijken onafhankelijk van elkaar te zijn\par
\bullet \b Implicaties:\b0\par
   - Verbeteringen in één aspect leiden niet automatisch tot verbeteringen in andere aspecten\par
   - Een holistische aanpak is nodig om alle kwaliteitsaspecten te verbeteren\par
   - Verder onderzoek naar onderliggende factoren die meerdere aspecten beïnvloeden is aan te bevelen\par

\pard\sa200\sl276\slmult1\b\fs24 Conclusies op basis van de grafieken\b0\fs22\par
\bullet De grafieken bevestigen dat Packager 1 en Packager 4 de meeste aandacht verdienen voor verbetering\par
\bullet Excelsa en Liberica koffiebonen leiden tot meer defecten dan andere boontypen\par
\bullet De benuttingsgraad van alle machines kan worden verbeterd\par
\bullet Er is geen duidelijke correlatie tussen verschillende kwaliteitsaspecten, wat wijst op de noodzaak van een geïntegreerde aanpak\par

\pard\sa200\sl276\slmult1\i\fs20 Deze grafieken zijn gegenereerd op basis van de opgeschoonde Kikker.csv dataset en bieden visuele ondersteuning voor de bevindingen en aanbevelingen in het analyserapport.\i0\fs22\par
}
