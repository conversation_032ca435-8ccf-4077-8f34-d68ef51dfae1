import pandas as pd

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon statistieken van FillingID
print('FillingID statistieken:')
if df['FillingID'].dtype == 'object':
    # Als het een string-kolom is, probeer te converteren naar numeriek
    try:
        numeric_values = pd.to_numeric(df['FillingID'], errors='coerce')
        print(numeric_values.describe())
    except:
        print("Kan niet converteren naar numeriek")
else:
    print(df['FillingID'].describe())

# Toon de verdeling van FillingID (frequentietabel van de hoogste waarden)
print('\nTop 20 hoogste FillingID waarden:')
if df['FillingID'].dtype == 'object':
    # Als het een string-kolom is, sorteer op lengte en dan alfabetisch
    top_values = df['FillingID'].value_counts().sort_index(key=lambda x: [len(str(i)) for i in x]).tail(20)
    print(top_values)
else:
    # Als het een numerieke kolom is, sorteer op waarde
    print(df['FillingID'].value_counts().sort_index().tail(20))

# Toon de unieke waarden van FillingID
print('\nAantal unieke waarden in FillingID:')
print(len(df['FillingID'].unique()))

# Controleer op extreme waarden
print('\nControleer op extreme waarden in FillingID:')
if df['FillingID'].dtype == 'object':
    # Als het een string-kolom is, tel waarden met meer dan 4 karakters
    long_values = df['FillingID'].str.len() > 4
    print(f"Aantal waarden met meer dan 4 karakters: {long_values.sum()}")
    
    # Tel waarden die alleen uit cijfers bestaan en groter zijn dan 1000
    numeric_mask = df['FillingID'].str.isdigit()
    if numeric_mask.sum() > 0:
        large_values = pd.to_numeric(df.loc[numeric_mask, 'FillingID']) > 1000
        print(f"Aantal numerieke waarden groter dan 1000: {large_values.sum()}")
else:
    # Als het een numerieke kolom is, tel waarden groter dan 1000
    large_values = df['FillingID'] > 1000
    print(f"Aantal waarden groter dan 1000: {large_values.sum()}")
