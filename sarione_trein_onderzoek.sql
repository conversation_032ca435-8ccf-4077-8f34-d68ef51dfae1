-- Stap 1: <PERSON><PERSON> de schoonmaker Sarione en zijn ID
SELECT * FROM schoonmaker 
WHERE naam = 'Sarione';

-- Stap 2: <PERSON><PERSON>jk de schoonmaak planning voor Sarione op 24 april 2024
SELECT * FROM schoonmaak_planning
WHERE schoonmaker_id = 16
AND datum = '2024-04-24';

-- Stap 3: Vind de specifieke trein waar Sarione op 24 april 2024 werkte
SELECT DISTINCT
    schoonmaak_planning.trein_id AS trein_id,
    schoonmaker.naam,
    schoonmaak_planning.datum,
    schoonmaak_planning.tijd_start,
    schoonmaak_planning.tijd_einde
FROM
    schoonmaak_planning
JOIN
    schoonmaker ON schoonmaak_planning.schoonmaker_id = schoonmaker.schoonmaker_id
WHERE
    schoonmaker.naam = 'Sarione'
    AND schoonmaak_planning.datum = '2024-04-24';

-- Stap 4: Bekijk de dienstregeling van de trein waar Sarione op werkte
SELECT * FROM dienstregeling
WHERE trein_id = 6679
AND datum = '2024-04-24';

-- Stap 5: Be<PERSON>jk alle check-ins en check-outs op de stations waar trein 6679 stopte op 24 april
SELECT ov.* 
FROM ov_info ov
JOIN dienstregeling d ON ov.station_id = d.station_id_vertrek OR ov.station_id = d.station_id_aankomst
WHERE d.trein_id = 6679
AND ov.datum = '2024-04-24'
ORDER BY ov.tijd;

-- Stap 6: Vind passagiers die hebben ingecheckt maar niet uitgecheckt op 24 april 2024
-- (potentiële verdachten)
SELECT o1.ov_chipkaart_id, o1.datum, o1.station_id
FROM ov_info o1
WHERE o1.datum = '2024-04-24'
AND o1.datum_activiteit = 'inchecken'
AND NOT EXISTS (
    SELECT 1 FROM ov_info o2
    WHERE o2.ov_chipkaart_id = o1.ov_chipkaart_id
    AND o2.datum = '2024-04-24'
    AND o2.datum_activiteit = 'uitchecken'
);

-- Stap 7: Combineer alle informatie voor een compleet overzicht
SELECT 
    s.naam AS schoonmaker_naam,
    sp.trein_id,
    sp.datum,
    sp.tijd_start,
    sp.tijd_einde,
    d.station_id_vertrek,
    d.station_id_aankomst,
    d.tijd_vertrek,
    d.tijd_aankomst,
    ov.ov_chipkaart_id,
    ov.datum_activiteit,
    ov.station_id
FROM schoonmaker s
JOIN schoonmaak_planning sp ON s.schoonmaker_id = sp.schoonmaker_id
JOIN dienstregeling d ON sp.trein_id = d.trein_id AND d.datum = sp.datum
LEFT JOIN ov_info ov ON ov.datum = d.datum 
    AND (ov.station_id = d.station_id_vertrek OR ov.station_id = d.station_id_aankomst)
WHERE s.naam = 'Sarione'
AND sp.datum = '2024-04-24'
ORDER BY d.tijd_vertrek, ov.tijd;
