Option Explicit

' Eenvoudig script om CSV-bestanden te combineren in één Excel-bestand

' Hoofdfunctie
Sub Main()
    Dim fso, excel, wb, ws
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Set wb = excel.Workbooks.Add
    Set ws = wb.Sheets(1)
    ws.Name = "Kikker_cleaned"
    
    ' Importeer <PERSON>_cleaned.csv
    If fso.FileExists("Kikker_cleaned.csv") Then
        ImportCSV "Kikker_cleaned.csv", ws
    End If
    
    ' Voeg werkbladen toe voor de analyses
    Dim analyseFiles, i
    analyseFiles = Array("Lean_Analyse_Clean.csv", "Kaizen_Analyse_Clean.csv", "TOC_Analyse_Clean.csv")
    
    For i = 0 To UBound(analyseFiles)
        If fso.FileExists(analyseFiles(i)) Then
            ' Voeg een nieuw werkblad toe
            wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
            Set ws = wb.Sheets(wb.Sheets.Count)
            ws.Name = Replace(analyseFiles(i), ".csv", "")
            
            ' Importeer de CSV-data
            ImportCSV analyseFiles(i), ws
        End If
    Next
    
    ' Sla het Excel-bestand op
    wb.SaveAs "Kikker_Analyses_Gecombineerd.xlsx", 51
    
    WScript.Echo "Bestand opgeslagen als Kikker_Analyses_Gecombineerd.xlsx"
End Sub

' Functie om een CSV-bestand te importeren in een werkblad
Sub ImportCSV(csvFile, ws)
    Dim fso, file, line, row, col, values
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(csvFile, 1)
    
    row = 1
    
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        If Trim(line) <> "" Then
            ' Bepaal het scheidingsteken
            Dim separator
            If InStr(line, vbTab) > 0 Then
                separator = vbTab
            Else
                separator = ","
            End If
            
            ' Split de regel
            values = Split(line, separator)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                ws.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
    
    ' Pas de kolombreedtes aan
    ws.Columns.AutoFit
End Sub

' Start het script
Call Main()
