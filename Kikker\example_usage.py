#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example usage of the CSV cleaner for Americaps

This script demonstrates how to use the csv_cleaner.py module to clean CSV data.
"""

import os
import pandas as pd
from csv_cleaner import (
    load_csv,
    clean_csv_data,
    save_cleaned_data,
    generate_cleaning_report
)

def create_sample_csv():
    """Create a sample CSV file with data quality issues for demonstration."""
    # Create a directory for sample data if it doesn't exist
    if not os.path.exists('data'):
        os.makedirs('data')

    # Create sample data with various issues
    data = {
        'ProductID': [1, 2, 3, 4, 5, None, 7, 8, 9, 10],
        'ProductName': ['Coffee Capsule A', 'Coffee Capsule B', '  Coffee Capsule C  ',
                       'Coffee Capsule D', 'Coffee Capsule E', 'Coffee Capsule F',
                       'Coffee Capsule G', 'Coffee Capsule H', 'Coffee Capsule I', 'Coffee Capsule I'],  # Duplicate
        'Price': [2.5, 3.0, 2.75, None, 3.25, 2.9, 3.1, 2.8, 100.0, 3.2],  # Outlier
        'InStock': ['Yes', 'No', 'Yes', 'Yes', None, 'No', 'Yes', 'No', 'Yes', 'No'],
        'ManufactureDate': ['2023-01-15', '2023-02-20', 'invalid date', '2023-04-10',
                           '2023-05-05', '2023-06-15', '2023-07-20', '2023-08-10', '2023-09-05', '2023-10-15'],
        'DefectRate': ['1.5%', '2.0%', '1.8%', '2.2%', None, '1.9%', '2.1%', '1.7%', '1.6%', '2.3%'],
        'CustomerRating': [4.5, 4.2, 4.8, 4.0, 4.6, None, 4.3, 4.7, 4.9, 4.1]
    }

    # Create DataFrame
    df = pd.DataFrame(data)

    # Save to CSV
    file_path = 'data/sample_data.csv'
    df.to_csv(file_path, index=False)

    print(f"Sample CSV file created at {file_path}")
    return file_path


def main():
    """Main function to demonstrate CSV cleaning."""
    print("=" * 80)
    print("AMERICAPS CSV CLEANER - EXAMPLE USAGE")
    print("=" * 80)

    # Create sample data
    print("\nCreating sample data...")
    sample_file = create_sample_csv()

    # Load the CSV file
    print("\nLoading CSV file...")
    df_original = load_csv(sample_file)

    # Display original data
    print("\nOriginal data:")
    print(df_original)

    # Set cleaning options
    options = {
        'numeric_fill': 'median',
        'text_fill': 'mode',
        'trim_text': True,
        'lowercase_text': False,
        'remove_duplicates': True,
        'handle_outliers': True,
        'outlier_method': 'iqr',
        'outlier_threshold': 1.5
    }

    # Clean the data
    print("\nCleaning data...")
    df_cleaned, summary = clean_csv_data(df_original, options)

    # Display cleaned data
    print("\nCleaned data:")
    print(df_cleaned)

    # Save cleaned data
    output_file = 'data/sample_data_cleaned.csv'
    save_cleaned_data(df_cleaned, output_file, delimiter=',')

    # Generate cleaning report
    report_file = 'data/cleaning_report.txt'
    report = generate_cleaning_report(df_original, df_cleaned, summary, report_file)

    # Display summary
    print("\nCleaning summary:")
    for item in summary:
        print(f"- {item}")

    print("\nExample completed successfully!")
    print(f"Cleaned data saved to: {output_file}")
    print(f"Cleaning report saved to: {report_file}")


if __name__ == "__main__":
    main()
