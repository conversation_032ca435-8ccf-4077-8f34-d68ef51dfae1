<!DOCTYPE html>
<html>
<head>
    <title>Use Case Scenarios - EuroCaps Order Management App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
        }
        th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        td {
            padding: 10px;
            border: 1px solid #ddd;
            vertical-align: top;
            line-height: 1.4;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .use-case-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .actor {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Use Case Scenarios - EuroCaps Order Management App</h1>
    
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Use Case Name</th>
                <th style="width: 15%;">Actor</th>
                <th style="width: 15%;">Assumptions</th>
                <th style="width: 25%;">Description</th>
                <th style="width: 15%;">Exceptions</th>
                <th style="width: 15%;">Result</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="use-case-name">Login</td>
                <td class="actor">User</td>
                <td>PowerApps is installed and system is operational</td>
                <td>1) User opens the EuroCaps Order Management app<br>2) User enters username and password<br>3) System validates credentials. If not valid, exception [user not found] occurs<br>4) Upon successful login, dashboard is displayed</td>
                <td>[User not found] Error message is shown and user remains on login screen<br>[App unavailable] Message that app cannot be loaded</td>
                <td>User gains access to the app dashboard</td>
            </tr>
            <tr>
                <td class="use-case-name">View Dashboard</td>
                <td class="actor">Operations Manager</td>
                <td>User is logged into the app</td>
                <td>1) User opens the dashboard screen<br>2) System displays KPIs and order overviews<br>3) User views recent orders and statistics<br>4) User can navigate to other modules via navigation menu</td>
                <td>[No data available] Dashboard shows message that no data is available<br>[Network issue] Dashboard cannot be loaded</td>
                <td>Dashboard is displayed with current business information and navigation options</td>
            </tr>
            <tr>
                <td class="use-case-name">View Customers</td>
                <td class="actor">Operations Manager</td>
                <td>User has access to customer module</td>
                <td>1) User navigates to Customer List screen<br>2) System displays list of all customers<br>3) User can search customers using search function<br>4) User selects customer to view details</td>
                <td>[No customers found] Message that no customers exist in database<br>[Empty search result] No customers found with search term</td>
                <td>Customer list is displayed and user can select a customer</td>
            </tr>
            <tr>
                <td class="use-case-name">View Products</td>
                <td class="actor">Purchasing Manager</td>
                <td>Product catalog is populated with products</td>
                <td>1) User navigates to Product Catalog screen<br>2) System displays all available products<br>3) User can search and filter products<br>4) User views product details and prices</td>
                <td>[No products available] Message that product catalog is empty<br>[Product not found] Search yields no products</td>
                <td>Product catalog is displayed with all available products</td>
            </tr>
            <tr>
                <td class="use-case-name">Create New Order</td>
                <td class="actor">Purchasing Manager</td>
                <td>Customers and products are available in the system</td>
                <td>1) User navigates to New Order screen<br>2) User selects customer from customer list<br>3) User adds products to order<br>4) System checks availability. If not available, exception [product unavailable] occurs<br>5) User saves the order</td>
                <td>[Product unavailable] Message that product is out of stock<br>[No customer selected] Error message that customer must be chosen</td>
                <td>New order is created and saved in the system</td>
            </tr>
            <tr>
                <td class="use-case-name">View Order Details</td>
                <td class="actor">Logistics Manager</td>
                <td>Orders exist in the system</td>
                <td>1) User navigates to Order Detail screen<br>2) User selects an order from the list<br>3) System displays all order details including customer, products and status<br>4) User can modify order if status allows</td>
                <td>[Order not found] Message that selected order does not exist<br>[Order not editable] Order status does not allow modifications</td>
                <td>Order details are fully displayed with option to modify</td>
            </tr>
            <tr>
                <td class="use-case-name">Manage Order Items</td>
                <td class="actor">Production Employee</td>
                <td>Order is selected and contains items</td>
                <td>1) User opens Order Item screen<br>2) System displays all items in the selected order<br>3) User can modify quantities of existing items<br>4) User can add new items or remove existing ones</td>
                <td>[Minimum quantity] Quantity cannot go below minimum<br>[Item unavailable] Selected product is no longer available</td>
                <td>Order items are updated according to user preferences</td>
            </tr>
            <tr>
                <td class="use-case-name">View Order History</td>
                <td class="actor">Operations Manager</td>
                <td>Historical orders are stored in database</td>
                <td>1) User navigates to Order History screen<br>2) System displays all historical orders<br>3) User can filter orders by date, customer or status<br>4) User selects order to view details</td>
                <td>[No history available] Message that no historical orders exist<br>[Filter yields no results] No orders match filter criteria</td>
                <td>Order history is displayed with filtering options</td>
            </tr>
            <tr>
                <td class="use-case-name">Confirm Order</td>
                <td class="actor">Logistics Manager</td>
                <td>Order is complete and ready for confirmation</td>
                <td>1) User navigates to Order Confirmation screen<br>2) System displays order summary for confirmation<br>3) User reviews all details and confirms order<br>4) System changes order status to 'confirmed' and sends confirmation</td>
                <td>[Order incomplete] Message that order does not contain all required information<br>[Confirmation failed] Technical error when sending confirmation</td>
                <td>Order is confirmed and confirmation email is sent</td>
            </tr>
            <tr>
                <td class="use-case-name">Manage App Settings</td>
                <td class="actor">Operations Manager</td>
                <td>User has admin rights for settings</td>
                <td>1) User navigates to Settings screen<br>2) System displays all available settings<br>3) User modifies desired settings such as profile, preferences<br>4) User can logout via settings menu</td>
                <td>[No admin rights] User cannot modify certain settings<br>[Settings not saved] Error when saving changes</td>
                <td>App settings are updated and saved</td>
            </tr>
        </tbody>
    </table>
</body>
</html>
