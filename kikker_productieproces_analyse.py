"""
Kikker Dataset - Productieproces Analyse
----------------------------------------
Dit script analyseert het productieproces in de Kikker dataset volgens verschillende methodologieën:
1. Six Sigma: Defectanalyse
2. Lean: Doorlooptijden en verspillingen
3. TOC: Bottleneck identificatie
4. Kaizen: Verbetermogelijkheden
5. TQM: Organisatiebrede kwaliteitsaanpak
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# Stap 1: Data inladen
print("Stap 1: Data inladen...")
# Probeer eerst de opgeschoonde data te laden, anders de originele
try:
    df = pd.read_csv("Kikker_opgeschoond.csv")
    print("Opgeschoonde dataset geladen")
except:
    try:
        df = pd.read_csv("Kikker_cleaned.csv")
        print("Cleaned dataset geladen")
    except:
        df = pd.read_csv("Kikker.csv")
        print("Originele dataset geladen")

print(f"Dataset: {df.shape[0]} rijen, {df.shape[1]} kolommen")

# Zorg ervoor dat alle percentage-kolommen correct zijn geconverteerd
percentage_kolommen = ['Defectpercentage', 'Klantretourpercentage', 'Benuttingsgraad']
for kolom in percentage_kolommen:
    if kolom in df.columns:
        if df[kolom].dtype == 'object':
            # Verwijder % teken en converteer naar float
            df[kolom] = df[kolom].str.replace('%', '').astype(float) / 100
        # Zorg ervoor dat percentages tussen 0 en 1 liggen
        if df[kolom].max() > 1:
            # Als de waarden al als percentages zijn opgeslagen (bijv. 75 in plaats van 0.75)
            df[kolom] = df[kolom] / 100

# Stap 2: Six Sigma - Defectanalyse
print("\nStap 2: Six Sigma - Defectanalyse...")

# Defectpercentage berekenen
if 'Defectpercentage' in df.columns:
    # Bereken gemiddeld defectpercentage
    gem_defect = df['Defectpercentage'].mean() * 100
    print(f"Gemiddeld defectpercentage: {gem_defect:.2f}%")
    
    # Defectpercentage per machine
    if 'PackagingApparaat' in df.columns:
        defect_per_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean() * 100
        print("\nDefectpercentage per verpakkingsmachine:")
        for machine, percentage in defect_per_machine.sort_values(ascending=False).items():
            print(f"  - {machine}: {percentage:.2f}%")
    
    # Klantretourpercentage analyseren
    if 'Klantretourpercentage' in df.columns:
        gem_retour = df['Klantretourpercentage'].mean() * 100
        print(f"\nGemiddeld klantretourpercentage: {gem_retour:.2f}%")
        
        # Correlatie tussen defecten en retours
        corr = df['Defectpercentage'].corr(df['Klantretourpercentage'])
        print(f"Correlatie tussen defecten en klantretours: {corr:.2f}")
    
    # Panel Test resultaten analyseren
    if 'Panel Test' in df.columns:
        panel_counts = df['Panel Test'].value_counts()
        print("\nPanel Test resultaten:")
        for resultaat, aantal in panel_counts.items():
            percentage = (aantal / len(df)) * 100
            print(f"  - {resultaat}: {aantal} ({percentage:.1f}%)")

# Stap 3: Lean - Doorlooptijden en verspillingen
print("\nStap 3: Lean - Doorlooptijden en verspillingen...")

# Cyclustijd analyse
if 'Cyclustijd' in df.columns:
    # Extraheer numerieke waarden uit Cyclustijd
    df['Cyclustijd_num'] = df['Cyclustijd'].str.extract(r'(\d+\.?\d*)').astype(float)
    
    # Bereken gemiddelde cyclustijd
    gem_cyclus = df['Cyclustijd_num'].mean()
    print(f"Gemiddelde cyclustijd: {gem_cyclus:.2f} uur")
    
    # Cyclustijd per machine
    if 'PackagingApparaat' in df.columns:
        cyclus_per_machine = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean()
        print("\nGemiddelde cyclustijd per verpakkingsmachine:")
        for machine, tijd in cyclus_per_machine.sort_values(ascending=False).items():
            print(f"  - {machine}: {tijd:.2f} uur")

# ProcessTime analyse
if 'ProcessTime' in df.columns:
    # Extraheer numerieke waarden uit ProcessTime
    df['ProcessTime_num'] = df['ProcessTime'].str.extract(r'(\d+\.?\d*)').astype(float)
    
    # Bereken gemiddelde procestijd
    gem_proces = df['ProcessTime_num'].mean()
    print(f"\nGemiddelde procestijd: {gem_proces:.2f} uur")

# Benuttingsgraad analyse
if 'Benuttingsgraad' in df.columns:
    # Bereken gemiddelde benuttingsgraad
    gem_benutting = df['Benuttingsgraad'].mean() * 100
    print(f"Gemiddelde benuttingsgraad: {gem_benutting:.2f}%")
    
    # Benuttingsgraad per machine
    if 'PackagingApparaat' in df.columns:
        benutting_per_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean() * 100
        print("\nBenuttingsgraad per verpakkingsmachine:")
        for machine, benutting in benutting_per_machine.sort_values().items():
            print(f"  - {machine}: {benutting:.2f}%")

# Stap 4: TOC - Bottleneck identificatie
print("\nStap 4: TOC - Bottleneck identificatie...")

# Identificeer bottlenecks op basis van cyclustijd en benuttingsgraad
if 'Cyclustijd_num' in df.columns and 'Benuttingsgraad' in df.columns and 'PackagingApparaat' in df.columns:
    # Combineer cyclustijd en benuttingsgraad per machine
    bottlenecks = pd.DataFrame({
        'Cyclustijd': df.groupby('PackagingApparaat')['Cyclustijd_num'].mean(),
        'Benuttingsgraad': df.groupby('PackagingApparaat')['Benuttingsgraad'].mean() * 100,
        'Defectpercentage': df.groupby('PackagingApparaat')['Defectpercentage'].mean() * 100 if 'Defectpercentage' in df.columns else 0
    })
    
    # Sorteer op cyclustijd (aflopend) om bottlenecks te identificeren
    print("Potentiële bottlenecks (machines met langste cyclustijd):")
    for machine, row in bottlenecks.sort_values('Cyclustijd', ascending=False).head(3).iterrows():
        print(f"  - {machine}: Cyclustijd {row['Cyclustijd']:.2f} uur, Benuttingsgraad {row['Benuttingsgraad']:.2f}%, Defectpercentage {row['Defectpercentage']:.2f}%")
    
    # Sorteer op benuttingsgraad (oplopend) om onderbenutte machines te identificeren
    print("\nOnderbenutte machines (laagste benuttingsgraad):")
    for machine, row in bottlenecks.sort_values('Benuttingsgraad').head(3).iterrows():
        print(f"  - {machine}: Benuttingsgraad {row['Benuttingsgraad']:.2f}%, Cyclustijd {row['Cyclustijd']:.2f} uur, Defectpercentage {row['Defectpercentage']:.2f}%")

# Stap 5: Kaizen - Verbetermogelijkheden
print("\nStap 5: Kaizen - Verbetermogelijkheden...")

# Identificeer verbetermogelijkheden op basis van defecten en cyclustijd
verbeteringen = []

# Verbetermogelijkheid 1: Machines met hoog defectpercentage
if 'Defectpercentage' in df.columns and 'PackagingApparaat' in df.columns:
    worst_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean().sort_values(ascending=False).index[0]
    worst_defect = df.groupby('PackagingApparaat')['Defectpercentage'].mean().sort_values(ascending=False).iloc[0] * 100
    verbeteringen.append(f"Verbeter kwaliteitscontrole bij {worst_machine} (defectpercentage: {worst_defect:.2f}%)")

# Verbetermogelijkheid 2: Machines met lange cyclustijd
if 'Cyclustijd_num' in df.columns and 'PackagingApparaat' in df.columns:
    slowest_machine = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean().sort_values(ascending=False).index[0]
    slowest_time = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean().sort_values(ascending=False).iloc[0]
    verbeteringen.append(f"Optimaliseer processen bij {slowest_machine} (cyclustijd: {slowest_time:.2f} uur)")

# Verbetermogelijkheid 3: Onderbenutte machines
if 'Benuttingsgraad' in df.columns and 'PackagingApparaat' in df.columns:
    underused_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean().sort_values().index[0]
    underused_pct = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean().sort_values().iloc[0] * 100
    verbeteringen.append(f"Verhoog benuttingsgraad van {underused_machine} (huidige benutting: {underused_pct:.2f}%)")

# Verbetermogelijkheid 4: Hoog energieverbruik
if 'Energieverbruik' in df.columns and 'PackagingApparaat' in df.columns:
    # Extraheer numerieke waarden uit Energieverbruik
    df['Energieverbruik_num'] = df['Energieverbruik'].astype(str).str.extract(r'(\d+\.?\d*)').astype(float)
    
    # Identificeer machine met hoogste energieverbruik
    high_energy_machine = df.groupby('PackagingApparaat')['Energieverbruik_num'].mean().sort_values(ascending=False).index[0]
    high_energy = df.groupby('PackagingApparaat')['Energieverbruik_num'].mean().sort_values(ascending=False).iloc[0]
    verbeteringen.append(f"Reduceer energieverbruik van {high_energy_machine} (gemiddeld verbruik: {high_energy:.1f} kWh)")

# Verbetermogelijkheid 5: Duurzaamheid verbeteren
if 'Duurzaamheid Score' in df.columns and 'PackagingApparaat' in df.columns:
    low_sust_machine = df.groupby('PackagingApparaat')['Duurzaamheid Score'].mean().sort_values().index[0]
    low_sust = df.groupby('PackagingApparaat')['Duurzaamheid Score'].mean().sort_values().iloc[0]
    verbeteringen.append(f"Verbeter duurzaamheid van {low_sust_machine} (huidige score: {low_sust:.1f})")

# Toon verbetermogelijkheden
print("Kaizen verbetermogelijkheden:")
for i, verbetering in enumerate(verbeteringen, 1):
    print(f"  {i}. {verbetering}")

# Stap 6: TQM - Organisatiebrede kwaliteitsaanpak
print("\nStap 6: TQM - Organisatiebrede kwaliteitsaanpak...")

# Analyseer relaties tussen verschillende kwaliteitsaspecten
print("Correlaties tussen kwaliteitsaspecten:")

# Zorg ervoor dat alle kolommen numeriek zijn voor correlatie-analyse
for kolom in df.columns:
    if df[kolom].dtype == 'object':
        try:
            # Probeer te converteren naar numeriek
            df[kolom] = pd.to_numeric(df[kolom].str.replace('[^0-9.-]', '', regex=True), errors='coerce')
        except:
            pass  # Als het niet lukt, sla over

# Selecteer relevante kolommen voor kwaliteitsanalyse
kwaliteits_kolommen = []
if 'Defectpercentage' in df.columns and df['Defectpercentage'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Defectpercentage')
if 'Klanttevredenheid' in df.columns and df['Klanttevredenheid'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Klanttevredenheid')
if 'Klantretourpercentage' in df.columns and df['Klantretourpercentage'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Klantretourpercentage')
if 'Cyclustijd_num' in df.columns and df['Cyclustijd_num'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Cyclustijd_num')
if 'Benuttingsgraad' in df.columns and df['Benuttingsgraad'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Benuttingsgraad')
if 'Cost' in df.columns and df['Cost'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Cost')
if 'Leveranciersbeoordeling' in df.columns and df['Leveranciersbeoordeling'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Leveranciersbeoordeling')
if 'Duurzaamheid Score' in df.columns and df['Duurzaamheid Score'].dtype in ['float64', 'int64']:
    kwaliteits_kolommen.append('Duurzaamheid Score')

# Bereken correlaties tussen kwaliteitsaspecten
if kwaliteits_kolommen:
    try:
        correlaties = df[kwaliteits_kolommen].corr()
        
        # Toon sterke correlaties (positief of negatief)
        sterke_correlaties = []
        for i in range(len(correlaties.columns)):
            for j in range(i):
                if abs(correlaties.iloc[i, j]) > 0.3:  # Drempelwaarde voor sterke correlatie
                    sterke_correlaties.append((
                        correlaties.columns[i], 
                        correlaties.columns[j], 
                        correlaties.iloc[i, j]
                    ))
        
        # Sorteer op absolute correlatiewaarde
        sterke_correlaties.sort(key=lambda x: abs(x[2]), reverse=True)
        
        # Toon sterke correlaties
        if sterke_correlaties:
            print("Sterke correlaties tussen kwaliteitsaspecten:")
            for var1, var2, corr in sterke_correlaties[:5]:  # Toon top 5
                print(f"  - {var1} en {var2}: {corr:.2f}")
        else:
            print("Geen sterke correlaties gevonden tussen kwaliteitsaspecten")
    except Exception as e:
        print(f"Fout bij berekenen correlaties: {e}")
        print("Controleer of alle kolommen numeriek zijn.")
else:
    print("Geen geschikte numerieke kolommen gevonden voor correlatie-analyse.")

# Stap 7: Samenvatting en aanbevelingen
print("\nStap 7: Samenvatting en aanbevelingen...")

# Verzamel belangrijkste bevindingen
bevindingen = []

# Six Sigma bevindingen
if 'Defectpercentage' in df.columns:
    bevindingen.append(f"Gemiddeld defectpercentage: {gem_defect:.2f}%")
    if 'PackagingApparaat' in df.columns:
        worst_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean().sort_values(ascending=False).index[0]
        worst_defect = df.groupby('PackagingApparaat')['Defectpercentage'].mean().sort_values(ascending=False).iloc[0] * 100
        bevindingen.append(f"Machine met hoogste defectpercentage: {worst_machine} ({worst_defect:.2f}%)")

# Lean bevindingen
if 'Cyclustijd_num' in df.columns:
    bevindingen.append(f"Gemiddelde cyclustijd: {gem_cyclus:.2f} uur")
if 'Benuttingsgraad' in df.columns:
    bevindingen.append(f"Gemiddelde benuttingsgraad: {gem_benutting:.2f}%")

# TOC bevindingen
if 'Cyclustijd_num' in df.columns and 'PackagingApparaat' in df.columns:
    slowest_machine = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean().sort_values(ascending=False).index[0]
    slowest_time = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean().sort_values(ascending=False).iloc[0]
    bevindingen.append(f"Bottleneck: {slowest_machine} (cyclustijd: {slowest_time:.2f} uur)")

# Toon samenvatting
print("Belangrijkste bevindingen:")
for i, bevinding in enumerate(bevindingen, 1):
    print(f"  {i}. {bevinding}")

# Toon aanbevelingen
print("\nAanbevelingen:")
for i, verbetering in enumerate(verbeteringen, 1):
    print(f"  {i}. {verbetering}")

# Stap 8: Visualisaties maken
print("\nStap 8: Visualisaties maken...")

# Maak visualisatiemap
if not os.path.exists('visualisaties'):
    os.makedirs('visualisaties')

# Visualisatie 1: Defectpercentage per machine (Pareto)
if 'Defectpercentage' in df.columns and 'PackagingApparaat' in df.columns:
    plt.figure(figsize=(12, 6))
    
    # Bereken defectpercentage per machine
    defect_per_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean() * 100
    
    # Sorteer van hoog naar laag (Pareto principe)
    defect_per_machine = defect_per_machine.sort_values(ascending=False)
    
    # Bereken cumulatief percentage
    totaal_defect = defect_per_machine.sum()
    cum_percentage = np.cumsum(defect_per_machine) / totaal_defect * 100
    
    # Maak een bar plot
    ax1 = plt.subplot(111)
    bars = ax1.bar(defect_per_machine.index, defect_per_machine, color='salmon')
    ax1.set_ylabel('Defectpercentage (%)', color='salmon')
    ax1.set_xlabel('Verpakkingsmachine')
    ax1.tick_params(axis='y', labelcolor='salmon')
    
    # Voeg cumulatieve lijn toe
    ax2 = ax1.twinx()
    ax2.plot(defect_per_machine.index, cum_percentage, 'bo-', linewidth=2)
    ax2.set_ylabel('Cumulatief percentage (%)', color='blue')
    ax2.tick_params(axis='y', labelcolor='blue')
    ax2.set_ylim([0, 105])
    
    # Voeg 80% lijn toe (Pareto principe)
    ax2.axhline(y=80, color='red', linestyle='--', alpha=0.7)
    ax2.text(0, 81, '80% van defecten', color='red')
    
    # Voeg waarden toe boven de balken
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}%', ha='center', va='bottom')
    
    plt.title('Pareto Analyse: Defectpercentage per Verpakkingsmachine')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('visualisaties/pareto_defecten_per_machine.png')
    print("Visualisatie opgeslagen: visualisaties/pareto_defecten_per_machine.png")

# Visualisatie 2: Cyclustijd per machine
if 'Cyclustijd_num' in df.columns and 'PackagingApparaat' in df.columns:
    plt.figure(figsize=(10, 6))
    cyclus_per_machine = df.groupby('PackagingApparaat')['Cyclustijd_num'].mean()
    
    # Sorteer van langste naar kortste cyclustijd
    cyclus_per_machine = cyclus_per_machine.sort_values(ascending=False)
    
    # Maak een bar plot
    bars = plt.bar(cyclus_per_machine.index, cyclus_per_machine, color='skyblue')
    
    # Voeg waarden toe boven de balken
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{height:.2f}', ha='center', va='bottom')
    
    plt.title('Gemiddelde Cyclustijd per Verpakkingsmachine')
    plt.xlabel('Verpakkingsmachine')
    plt.ylabel('Cyclustijd (uur)')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('visualisaties/cyclustijd_per_machine.png')
    print("Visualisatie opgeslagen: visualisaties/cyclustijd_per_machine.png")

# Visualisatie 3: Benuttingsgraad per machine
if 'Benuttingsgraad' in df.columns and 'PackagingApparaat' in df.columns:
    plt.figure(figsize=(10, 6))
    benutting_per_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean() * 100
    
    # Sorteer van laagste naar hoogste benuttingsgraad
    benutting_per_machine = benutting_per_machine.sort_values()
    
    # Maak een bar plot
    bars = plt.bar(benutting_per_machine.index, benutting_per_machine, color='lightgreen')
    
    # Voeg waarden toe boven de balken
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{height:.2f}%', ha='center', va='bottom')
    
    plt.title('Benuttingsgraad per Verpakkingsmachine')
    plt.xlabel('Verpakkingsmachine')
    plt.ylabel('Benuttingsgraad (%)')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('visualisaties/benutting_per_machine.png')
    print("Visualisatie opgeslagen: visualisaties/benutting_per_machine.png")

# Visualisatie 4: Energieverbruik per machine
if 'Energieverbruik_num' in df.columns and 'PackagingApparaat' in df.columns:
    plt.figure(figsize=(10, 6))
    energie_per_machine = df.groupby('PackagingApparaat')['Energieverbruik_num'].mean()
    
    # Sorteer van hoogste naar laagste energieverbruik
    energie_per_machine = energie_per_machine.sort_values(ascending=False)
    
    # Maak een bar plot
    bars = plt.bar(energie_per_machine.index, energie_per_machine, color='orange')
    
    # Voeg waarden toe boven de balken
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + energie_per_machine.max()*0.02,
                f'{height:.1f}', ha='center', va='bottom')
    
    plt.title('Gemiddeld Energieverbruik per Verpakkingsmachine')
    plt.xlabel('Verpakkingsmachine')
    plt.ylabel('Energieverbruik (kWh)')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('visualisaties/energie_per_machine.png')
    print("Visualisatie opgeslagen: visualisaties/energie_per_machine.png")

# Visualisatie 5: Duurzaamheid per machine
if 'Duurzaamheid Score' in df.columns and 'PackagingApparaat' in df.columns:
    plt.figure(figsize=(10, 6))
    duurzaamheid_per_machine = df.groupby('PackagingApparaat')['Duurzaamheid Score'].mean()
    
    # Sorteer van laagste naar hoogste duurzaamheid
    duurzaamheid_per_machine = duurzaamheid_per_machine.sort_values()
    
    # Maak een bar plot
    bars = plt.bar(duurzaamheid_per_machine.index, duurzaamheid_per_machine, color='green')
    
    # Voeg waarden toe boven de balken
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}', ha='center', va='bottom')
    
    plt.title('Gemiddelde Duurzaamheidsscore per Verpakkingsmachine')
    plt.xlabel('Verpakkingsmachine')
    plt.ylabel('Duurzaamheidsscore')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('visualisaties/duurzaamheid_per_machine.png')
    print("Visualisatie opgeslagen: visualisaties/duurzaamheid_per_machine.png")

# Visualisatie 6: Correlatieheatmap
if kwaliteits_kolommen:
    try:
        plt.figure(figsize=(10, 8))
        # Maak een eenvoudige heatmap
        plt.imshow(correlaties, cmap='coolwarm', aspect='auto')
        plt.colorbar(label='Correlatie')
        
        # Voeg labels toe
        plt.xticks(range(len(correlaties.columns)), correlaties.columns, rotation=45, ha='right')
        plt.yticks(range(len(correlaties.columns)), correlaties.columns)
        
        # Voeg waarden toe in de cellen
        for i in range(len(correlaties.columns)):
            for j in range(len(correlaties.columns)):
                plt.text(j, i, f"{correlaties.iloc[i, j]:.2f}", 
                         ha="center", va="center", color="black")
        
        plt.title('Correlatie tussen Kwaliteitsaspecten')
        plt.tight_layout()
        plt.savefig('visualisaties/correlatie_heatmap.png')
        print("Visualisatie opgeslagen: visualisaties/correlatie_heatmap.png")
    except Exception as e:
        print(f"Fout bij maken correlatieheatmap: {e}")

print("\nAnalyse van het productieproces voltooid!")
print("Bekijk de visualisaties in de map 'visualisaties' voor grafische weergave van de resultaten.")
