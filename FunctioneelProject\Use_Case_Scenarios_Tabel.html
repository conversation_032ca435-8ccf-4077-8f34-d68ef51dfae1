<!DOCTYPE html>
<html>
<head>
    <title>Use <PERSON>'s - EuroCaps Systeem</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
        }
        th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        td {
            padding: 10px;
            border: 1px solid #ddd;
            vertical-align: top;
            line-height: 1.4;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .use-case-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .actor {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Use Case Scenario's - EuroCaps Order Management App</h1>
    
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Naam Use <PERSON></th>
                <th style="width: 15%;">Actor</th>
                <th style="width: 15%;">Aannames</th>
                <th style="width: 25%;">Beschrijving</th>
                <th style="width: 15%;">Uitzonderingen</th>
                <th style="width: 15%;">Resultaat</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="use-case-name">Inloggen</td>
                <td class="actor">Gebruiker</td>
                <td>Systeem is in werking</td>
                <td>1) Gebruiker geeft de naam en wachtwoord in<br>2) Het systeem controleert of naam en wachtwoord juist zijn. Zo niet, dan treedt de uitzondering [gebruiker niet bekend] op. Indien de combinatie goed is, wordt het startscherm getoond</td>
                <td>[Gebruiker niet bekend] Er wordt een foutmelding gegeven en het systeem blijft op het aanlogscherm</td>
                <td>Het startscherm wordt getoond</td>
            </tr>
            <tr>
                <td class="use-case-name">Dashboard bekijken</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Gebruiker is ingelogd in de app</td>
                <td>1) Gebruiker opent het dashboard screen<br>2) Het systeem toont KPI's en overzichten<br>3) Gebruiker bekijkt recente orders en statistieken<br>4) Gebruiker kan navigeren naar andere modules via menu</td>
                <td>[Geen data beschikbaar] Dashboard toont melding dat er geen data is<br>[Netwerk probleem] Dashboard kan niet worden geladen</td>
                <td>Dashboard wordt weergegeven met actuele bedrijfsinformatie</td>
            </tr>
            <tr>
                <td class="use-case-name">Productieproces starten</td>
                <td class="actor">Productiemedewerker</td>
                <td>Machines zijn operationeel en grondstoffen zijn beschikbaar</td>
                <td>1) Productiemedewerker selecteert een productieorder<br>2) Het systeem controleert beschikbaarheid van grondstoffen. Zo niet, dan treedt uitzondering [onvoldoende grondstoffen] op<br>3) Medewerker start het productieproces<br>4) Het systeem registreert de start van het productieproces</td>
                <td>[Onvoldoende grondstoffen] Foutmelding wordt getoond en productie kan niet starten<br>[Machine defect] Systeem toont waarschuwing en blokkeert productiestart</td>
                <td>Productieproces wordt gestart en geregistreerd in het systeem</td>
            </tr>
            <tr>
                <td class="use-case-name">Verzending plannen</td>
                <td class="actor">Manager Logistiek</td>
                <td>Orders zijn gereed voor verzending</td>
                <td>1) Manager bekijkt openstaande orders<br>2) Manager selecteert orders op basis van prioriteit<br>3) Het systeem controleert beschikbaarheid van transport. Zo niet, dan treedt uitzondering [geen transport beschikbaar] op<br>4) Manager maakt verzendschema aan<br>5) Het systeem slaat het verzendschema op</td>
                <td>[Geen transport beschikbaar] Systeem toont melding en verzending wordt uitgesteld<br>[Geen orders beschikbaar] Melding wordt getoond dat er geen orders klaar staan</td>
                <td>Verzendschema wordt aangemaakt en opgeslagen</td>
            </tr>
            <tr>
                <td class="use-case-name">Kwaliteitstest uitvoeren</td>
                <td class="actor">Productiemedewerker</td>
                <td>Test apparatuur is gekalibreerd en producten zijn gereed</td>
                <td>1) Productiemedewerker neemt monsters van geproduceerde capsules<br>2) Medewerker voert kwaliteitstests uit volgens procedures<br>3) Het systeem controleert of testresultaten binnen normen vallen. Zo niet, dan treedt uitzondering [kwaliteit onvoldoende] op<br>4) Medewerker registreert testresultaten in het systeem</td>
                <td>[Kwaliteit onvoldoende] Afwijkingsrapport wordt automatisch gegenereerd en management wordt geïnformeerd<br>[Apparatuur defect] Test kan niet worden uitgevoerd, foutmelding wordt getoond</td>
                <td>Testresultaten worden geregistreerd in het systeem</td>
            </tr>
            <tr>
                <td class="use-case-name">Inkooporder plaatsen</td>
                <td class="actor">Manager Inkoop</td>
                <td>Leveranciers zijn beschikbaar en budget is goedgekeurd</td>
                <td>1) Manager selecteert leverancier uit database<br>2) Manager specificeert producten en hoeveelheden<br>3) Het systeem controleert beschikbaar budget. Zo niet, dan treedt uitzondering [onvoldoende budget] op<br>4) Manager plaatst inkooporder<br>5) Het systeem verzendt order naar leverancier en registreert de order</td>
                <td>[Onvoldoende budget] Foutmelding wordt getoond en order kan niet worden geplaatst<br>[Leverancier niet beschikbaar] Systeem toont melding en vraagt om andere leverancier</td>
                <td>Inkooporder wordt geplaatst en bevestigd</td>
            </tr>
            <tr>
                <td class="use-case-name">Rapportage genereren</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Relevante data is beschikbaar in het systeem</td>
                <td>1) Manager selecteert gewenst rapporttype (productie, voorraad, inkoop, logistiek)<br>2) Manager specificeert periode en parameters<br>3) Het systeem controleert of voldoende data beschikbaar is. Zo niet, dan treedt uitzondering [onvoldoende data] op<br>4) Het systeem genereert rapport met statistieken en trends<br>5) Rapport wordt weergegeven en kan worden geëxporteerd</td>
                <td>[Onvoldoende data] Melding wordt getoond dat er te weinig data is voor de gevraagde periode<br>[Ongeldige parameters] Foutmelding en verzoek om correcte parameters</td>
                <td>Gedetailleerd rapport wordt gegenereerd en weergegeven</td>
            </tr>
            <tr>
                <td class="use-case-name">Gebruiker aanmaken</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Manager heeft beheerrechten</td>
                <td>1) Manager voert nieuwe gebruikersgegevens in (naam, email, functie)<br>2) Het systeem controleert of gebruikersnaam al bestaat. Zo ja, dan treedt uitzondering [gebruiker bestaat al] op<br>3) Manager wijst rol en rechten toe op basis van functie<br>4) Het systeem creëert gebruikersaccount<br>5) Inloggegevens worden naar gebruiker verzonden</td>
                <td>[Gebruiker bestaat al] Foutmelding wordt getoond en verzoek om andere gebruikersnaam<br>[Ongeldige gegevens] Validatiefout en verzoek om correcte gegevens</td>
                <td>Nieuw gebruikersaccount wordt aangemaakt en geactiveerd</td>
            </tr>
            <tr>
                <td class="use-case-name">Storing melden</td>
                <td class="actor">Productiemedewerker</td>
                <td>Machine vertoont problemen</td>
                <td>1) Productiemedewerker identificeert storing aan machine<br>2) Medewerker documenteert symptomen en locatie<br>3) Het systeem controleert of machine kritiek is. Zo ja, dan treedt uitzondering [kritieke storing] op<br>4) Medewerker meldt storing via het systeem<br>5) Het systeem registreert storingsmelding en informeert onderhoudsteam</td>
                <td>[Kritieke storing] Automatische noodstop wordt geactiveerd en management wordt direct geïnformeerd<br>[Systeem niet beschikbaar] Storing moet handmatig worden gemeld</td>
                <td>Storingsmelding wordt geregistreerd en doorgestuurd naar onderhoud</td>
            </tr>
            <tr>
                <td class="use-case-name">Batch traceren</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Batchnummers zijn geregistreerd in het systeem</td>
                <td>1) Manager voert batchnummer in<br>2) Het systeem zoekt naar productiehistorie van de batch. Indien niet gevonden, dan treedt uitzondering [batch niet gevonden] op<br>3) Het systeem toont volledige traceerbaarheid van grondstoffen tot eindproduct<br>4) Manager bekijkt productiedata, kwaliteitsresultaten en leveranciersinformatie</td>
                <td>[Batch niet gevonden] Foutmelding wordt getoond dat batchnummer niet bestaat<br>[Onvolledige data] Waarschuwing dat niet alle traceerbaarheidsgegevens beschikbaar zijn</td>
                <td>Volledige batchhistorie wordt weergegeven met alle relevante gegevens</td>
            </tr>
        </tbody>
    </table>
</body>
</html>
