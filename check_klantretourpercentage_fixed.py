import pandas as pd

# Laad de aangepaste dataset
df = pd.read_csv('Ki<PERSON>_cleaned_fixed.csv')

# Toon statistieken van Klantretourpercentage
print('Klantretourpercentage statistieken:')
print(df['Klantretourpercentage'].describe())

# Toon de verdeling van Klantretourpercentage (frequentietabel)
print('\nFrequentietabel van Klantretourpercentage:')
print(df['Klantretourpercentage'].value_counts().sort_index())

# Toon de unieke waarden van Klantretourpercentage
print('\nAantal unieke waarden van Klantretourpercentage:')
print(len(df['Klantretourpercentage'].unique()))

# Controleer op nulwaarden
print('\nAantal nulwaarden in Klantretourpercentage:')
print((df['Klantretourpercentage'] == 0).sum())
