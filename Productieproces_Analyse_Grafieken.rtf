{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 Productieproces Analyse - Grafieken\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de grafieken die zijn gemaakt voor de analyse van het productieproces in de Kikker dataset. De grafieken zijn opgeslagen als PNG-bestanden in de map 'visualisaties'.\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. Pareto Analyse: Defectpercentage per Verpakkingsmachine\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/pareto_defecten_per_machine.png\par
\b Beschrijving:\b0 Deze grafiek toont een Pareto analyse van het defectpercentage per verpakkingsmachine. De balken tonen het defectpercentage per machine (gesorteerd van hoog naar laag), terwijl de blauwe lijn het cumulatieve percentage toont. De rode stippellijn markeert de 80% grens volgens het Pareto-principe.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft het hoogste defectpercentage (2,05%)\par
\bullet Packager 4 heeft het laagste defectpercentage (2,00%)\par
\bullet De cumulatieve lijn toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de twee slechtst presterende machines\par

\pard\sa200\sl276\slmult1\b\fs24 2. Cyclustijd per Verpakkingsmachine\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/cyclustijd_per_machine.png\par
\b Beschrijving:\b0 Deze grafiek toont de gemiddelde cyclustijd (in uren) voor elke verpakkingsmachine, gesorteerd van langste naar kortste cyclustijd.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 4 heeft de langste cyclustijd (4,13 uur)\par
\bullet Packager 1 heeft ook een lange cyclustijd (4,13 uur)\par
\bullet Onbekende machines hebben de kortste cyclustijd (4,04 uur)\par

\pard\sa200\sl276\slmult1\b\fs24 3. Benuttingsgraad per Verpakkingsmachine\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/benutting_per_machine.png\par
\b Beschrijving:\b0 Deze grafiek toont de gemiddelde benuttingsgraad (in procenten) voor elke verpakkingsmachine, gesorteerd van laagste naar hoogste benuttingsgraad.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft de laagste benuttingsgraad (74,59%)\par
\bullet Packager 3 heeft de hoogste benuttingsgraad (75,33%)\par
\bullet Alle machines hebben een benuttingsgraad tussen 74,5% en 75,5%\par

\pard\sa200\sl276\slmult1\b\fs24 4. Energieverbruik per Verpakkingsmachine\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/energie_per_machine.png\par
\b Beschrijving:\b0 Deze grafiek toont het gemiddelde energieverbruik (in kWh) voor elke verpakkingsmachine, gesorteerd van hoogste naar laagste energieverbruik.\par
\b Belangrijkste bevindingen:\b0\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik (24.835,5 kWh)\par
\bullet Packager 4 heeft het laagste energieverbruik onder de bekende machines\par
\bullet Er zijn grote verschillen in energieverbruik tussen de machines\par

\pard\sa200\sl276\slmult1\b\fs24 5. Duurzaamheidsscore per Verpakkingsmachine\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/duurzaamheid_per_machine.png\par
\b Beschrijving:\b0 Deze grafiek toont de gemiddelde duurzaamheidsscore voor elke verpakkingsmachine, gesorteerd van laagste naar hoogste duurzaamheidsscore.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 3 heeft de laagste duurzaamheidsscore (61,1)\par
\bullet Packager 5 heeft de hoogste duurzaamheidsscore\par
\bullet Er zijn significante verschillen in duurzaamheid tussen de machines\par

\pard\sa200\sl276\slmult1\b\fs24 6. Correlatieheatmap van Kwaliteitsaspecten\b0\fs22\par
\b Bestandsnaam:\b0 visualisaties/correlatie_heatmap.png\par
\b Beschrijving:\b0 Deze heatmap toont de correlaties tussen verschillende kwaliteitsaspecten van het productieproces. Sterke positieve correlaties zijn rood, sterke negatieve correlaties zijn blauw, en zwakke correlaties zijn wit.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Er zijn geen sterke correlaties (>0,3 of <-0,3) gevonden tussen de kwaliteitsaspecten\par
\bullet De meeste kwaliteitsaspecten lijken onafhankelijk van elkaar te zijn\par
\bullet Er is geen duidelijk verband tussen defectpercentage en klanttevredenheid\par

\pard\sa200\sl276\slmult1\b\fs24 Conclusies op basis van de grafieken\b0\fs22\par
\bullet \b Six Sigma (Defectanalyse):\b0 Packager 1 heeft het hoogste defectpercentage (2,05%) en zou de focus moeten zijn voor kwaliteitsverbetering.\par
\bullet \b Lean (Doorlooptijden):\b0 Packager 4 en Packager 1 hebben de langste cyclustijden (4,13 uur) en zijn potentiële bottlenecks.\par
\bullet \b TOC (Bottlenecks):\b0 Packager 1 combineert de laagste benuttingsgraad met de hoogste defecten en langste cyclustijd, wat wijst op een kritisch verbeterpunt.\par
\bullet \b Kaizen (Verbetermogelijkheden):\b0 Het extreem hoge energieverbruik van 'Onbekend' machines vereist onmiddellijke aandacht.\par
\bullet \b TQM (Organisatiebrede aanpak):\b0 Er is geen duidelijke correlatie tussen verschillende kwaliteitsaspecten, wat wijst op de noodzaak van een geïntegreerde aanpak.\par

\pard\sa200\sl276\slmult1\i\fs20 Opmerking: De grafieken zijn opgeslagen als PNG-bestanden in de map 'visualisaties'. Open deze bestanden om de visualisaties te bekijken.\i0\fs22\par
}
