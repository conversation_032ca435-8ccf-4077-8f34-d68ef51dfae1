"""
Script om een Excel-bestand te maken met visualisaties op basis van de gegenereerde CSV-bestanden
"""

import pandas as pd
import numpy as np
import os
import openpyxl
from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.chart.label import DataLabelList
from openpyxl.chart.series import DataPoint
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

# Functie om een werkblad te maken met een DataFrame
def create_worksheet(wb, title, df):
    ws = wb.create_sheet(title=title)
    
    # Voeg de data toe
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # Opmaak voor de header
    for cell in ws[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
    
    # Pas de kolombreedte aan
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    return ws

# Functie om een staafdiagram te maken
def create_bar_chart(ws, title, categories_col, values_col, start_row=1, end_row=None):
    chart = BarChart()
    chart.title = title
    chart.style = 10
    chart.x_axis.title = ws.cell(row=1, column=categories_col).value
    chart.y_axis.title = ws.cell(row=1, column=values_col).value
    
    if end_row is None:
        end_row = ws.max_row
    
    data = Reference(ws, min_col=values_col, min_row=start_row, max_row=end_row, max_col=values_col)
    cats = Reference(ws, min_col=categories_col, min_row=start_row+1, max_row=end_row, max_col=categories_col)
    
    chart.add_data(data, titles_from_data=True)
    chart.set_categories(cats)
    
    # Voeg datalabels toe
    chart.dataLabels = DataLabelList()
    chart.dataLabels.showVal = True
    
    return chart

# Functie om een lijngrafiek te maken
def create_line_chart(ws, title, categories_col, values_col, start_row=1, end_row=None):
    chart = LineChart()
    chart.title = title
    chart.style = 12
    chart.x_axis.title = ws.cell(row=1, column=categories_col).value
    chart.y_axis.title = ws.cell(row=1, column=values_col).value
    
    if end_row is None:
        end_row = ws.max_row
    
    data = Reference(ws, min_col=values_col, min_row=start_row, max_row=end_row, max_col=values_col)
    cats = Reference(ws, min_col=categories_col, min_row=start_row+1, max_row=end_row, max_col=categories_col)
    
    chart.add_data(data, titles_from_data=True)
    chart.set_categories(cats)
    
    # Voeg datalabels toe
    chart.dataLabels = DataLabelList()
    chart.dataLabels.showVal = True
    
    return chart

# Functie om een Pareto-diagram te maken
def create_pareto_chart(ws, title, categories_col, values_col, start_row=1, end_row=None):
    # Maak een staafdiagram voor de defectpercentages
    chart = BarChart()
    chart.title = title
    chart.style = 10
    chart.x_axis.title = ws.cell(row=1, column=categories_col).value
    chart.y_axis.title = ws.cell(row=1, column=values_col).value
    
    if end_row is None:
        end_row = ws.max_row
    
    data = Reference(ws, min_col=values_col, min_row=start_row, max_row=end_row, max_col=values_col)
    cats = Reference(ws, min_col=categories_col, min_row=start_row+1, max_row=end_row, max_col=categories_col)
    
    chart.add_data(data, titles_from_data=True)
    chart.set_categories(cats)
    
    # Voeg datalabels toe
    chart.dataLabels = DataLabelList()
    chart.dataLabels.showVal = True
    
    # Bereken cumulatieve percentages en voeg toe aan werkblad
    total = sum([cell.value for cell in ws[values_col][start_row:end_row+1]])
    cum_pct = 0
    ws.cell(row=1, column=values_col+1).value = "Cumulatief %"
    
    for i in range(start_row+1, end_row+1):
        value = ws.cell(row=i, column=values_col).value
        cum_pct += (value / total) * 100
        ws.cell(row=i, column=values_col+1).value = cum_pct
    
    # Maak een lijngrafiek voor de cumulatieve percentages
    line = LineChart()
    line.title = "Cumulatief Percentage"
    line.style = 12
    line.y_axis.axId = 200
    line.y_axis.title = "Cumulatief %"
    
    data = Reference(ws, min_col=values_col+1, min_row=start_row, max_row=end_row, max_col=values_col+1)
    line.add_data(data, titles_from_data=True)
    
    # Combineer de grafieken
    chart += line
    
    return chart

# Functie om een taartdiagram te maken
def create_pie_chart(ws, title, categories_col, values_col, start_row=1, end_row=None):
    chart = PieChart()
    chart.title = title
    chart.style = 10
    
    if end_row is None:
        end_row = ws.max_row
    
    data = Reference(ws, min_col=values_col, min_row=start_row, max_row=end_row, max_col=values_col)
    cats = Reference(ws, min_col=categories_col, min_row=start_row+1, max_row=end_row, max_col=categories_col)
    
    chart.add_data(data, titles_from_data=True)
    chart.set_categories(cats)
    
    # Voeg datalabels toe
    chart.dataLabels = DataLabelList()
    chart.dataLabels.showVal = True
    chart.dataLabels.showPercent = True
    chart.dataLabels.showCatName = True
    
    return chart

# Maak een nieuw Excel-bestand
wb = Workbook()

# Verwijder het standaard werkblad
wb.remove(wb.active)

# 1. Defectpercentage per machine
print("Visualisatie 1: Defectpercentage per machine...")
df_defect = pd.read_csv('excel_data/defect_per_machine.csv')
ws_defect = create_worksheet(wb, "Defect per Machine", df_defect)
chart_defect = create_bar_chart(ws_defect, "Defectpercentage per Verpakkingsmachine", 1, 2)
ws_defect.add_chart(chart_defect, "D2")

# 2. Defectpercentage per dag
print("Visualisatie 2: Defectpercentage per dag...")
df_defect_dag = pd.read_csv('excel_data/defect_per_dag.csv')
ws_defect_dag = create_worksheet(wb, "Defect per Dag", df_defect_dag)
chart_defect_dag = create_line_chart(ws_defect_dag, "Defectpercentage per Dag", 1, 2)
ws_defect_dag.add_chart(chart_defect_dag, "D2")

# 3. Cyclustijd per machine
print("Visualisatie 3: Cyclustijd per machine...")
df_cyclus = pd.read_csv('excel_data/cyclustijd_per_machine.csv')
ws_cyclus = create_worksheet(wb, "Cyclustijd per Machine", df_cyclus)
chart_cyclus = create_bar_chart(ws_cyclus, "Cyclustijd per Verpakkingsmachine", 1, 2)
ws_cyclus.add_chart(chart_cyclus, "D2")

# 4. Benuttingsgraad per machine
print("Visualisatie 4: Benuttingsgraad per machine...")
df_benutting = pd.read_csv('excel_data/benutting_per_machine.csv')
ws_benutting = create_worksheet(wb, "Benutting per Machine", df_benutting)
chart_benutting = create_bar_chart(ws_benutting, "Benuttingsgraad per Verpakkingsmachine", 1, 2)
ws_benutting.add_chart(chart_benutting, "D2")

# 5. Energieverbruik per machine
print("Visualisatie 5: Energieverbruik per machine...")
df_energie = pd.read_csv('excel_data/energie_per_machine.csv')
ws_energie = create_worksheet(wb, "Energie per Machine", df_energie)
chart_energie = create_bar_chart(ws_energie, "Energieverbruik per Verpakkingsmachine", 1, 2)
ws_energie.add_chart(chart_energie, "D2")

# 6. Panel Test resultaten
print("Visualisatie 6: Panel Test resultaten...")
df_panel = pd.read_csv('excel_data/panel_test.csv')
ws_panel = create_worksheet(wb, "Panel Test", df_panel)
chart_panel = create_pie_chart(ws_panel, "Panel Test Resultaten", 1, 2)
ws_panel.add_chart(chart_panel, "D2")

# 7. Pareto-diagram
print("Visualisatie 7: Pareto-diagram...")
df_pareto = pd.read_csv('excel_data/pareto_data.csv')
# Neem alleen de top 10 voor het Pareto-diagram
df_pareto = df_pareto.head(10)
ws_pareto = create_worksheet(wb, "Pareto Analyse", df_pareto)
chart_pareto = create_pareto_chart(ws_pareto, "Pareto Analyse: Defectpercentage", 1, 2)
ws_pareto.add_chart(chart_pareto, "D2")

# 8. Machine vergelijking
print("Visualisatie 8: Machine vergelijking...")
df_vergelijking = pd.read_csv('excel_data/machine_vergelijking.csv')
ws_vergelijking = create_worksheet(wb, "Machine Vergelijking", df_vergelijking)

# Maak een staafdiagram voor elke metriek
chart_defect_vgl = create_bar_chart(ws_vergelijking, "Defectpercentage per Machine", 1, 2)
ws_vergelijking.add_chart(chart_defect_vgl, "G2")

chart_cyclus_vgl = create_bar_chart(ws_vergelijking, "Cyclustijd per Machine", 1, 3)
ws_vergelijking.add_chart(chart_cyclus_vgl, "G18")

chart_benutting_vgl = create_bar_chart(ws_vergelijking, "Benuttingsgraad per Machine", 1, 4)
ws_vergelijking.add_chart(chart_benutting_vgl, "G34")

chart_energie_vgl = create_bar_chart(ws_vergelijking, "Energieverbruik per Machine", 1, 5)
ws_vergelijking.add_chart(chart_energie_vgl, "G50")

# 9. Maak een dashboard
print("Visualisatie 9: Dashboard...")
ws_dashboard = wb.create_sheet(title="Dashboard")
ws_dashboard.sheet_properties.tabColor = "1072BA"

# Voeg titel toe
ws_dashboard['A1'] = "PRODUCTIEPROCES ANALYSE DASHBOARD"
ws_dashboard['A1'].font = Font(size=16, bold=True)
ws_dashboard.merge_cells('A1:J1')
ws_dashboard['A1'].alignment = Alignment(horizontal="center")

# Voeg subtitels toe
ws_dashboard['A3'] = "Kwaliteit"
ws_dashboard['A3'].font = Font(size=14, bold=True)
ws_dashboard['A15'] = "Efficiëntie"
ws_dashboard['A15'].font = Font(size=14, bold=True)
ws_dashboard['A27'] = "Duurzaamheid"
ws_dashboard['A27'].font = Font(size=14, bold=True)

# Voeg grafieken toe aan het dashboard
chart_defect_dash = create_bar_chart(ws_defect, "Defectpercentage per Machine", 1, 2)
ws_dashboard.add_chart(chart_defect_dash, "A4")

chart_panel_dash = create_pie_chart(ws_panel, "Panel Test Resultaten", 1, 2)
ws_dashboard.add_chart(chart_panel_dash, "G4")

chart_cyclus_dash = create_bar_chart(ws_cyclus, "Cyclustijd per Machine", 1, 2)
ws_dashboard.add_chart(chart_cyclus_dash, "A16")

chart_benutting_dash = create_bar_chart(ws_benutting, "Benuttingsgraad per Machine", 1, 2)
ws_dashboard.add_chart(chart_benutting_dash, "G16")

chart_energie_dash = create_bar_chart(ws_energie, "Energieverbruik per Machine", 1, 2)
ws_dashboard.add_chart(chart_energie_dash, "A28")

chart_pareto_dash = create_pareto_chart(ws_pareto, "Pareto Analyse: Defectpercentage", 1, 2)
ws_dashboard.add_chart(chart_pareto_dash, "G28")

# Sla het Excel-bestand op
print("Excel-bestand opslaan...")
wb.save("Productieproces_Visualisaties.xlsx")
print("Excel-bestand opgeslagen: Productieproces_Visualisaties.xlsx")
