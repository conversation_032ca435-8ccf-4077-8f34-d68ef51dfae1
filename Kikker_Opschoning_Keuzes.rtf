{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 Data Opschoning Kikker.csv - Gemaakte Keuzes\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. Ontbrekende Waarden\b0\fs22\par
\bullet \b Numerieke kolommen:\b0 Opgevuld met mediaan\par
   - Cost: 332 missende waarden (mediaan: 503,33)\par
   - Leveranciersbeoordeling: 403 missende waarden (mediaan: 4,00)\par
   - Klanttevredenheid: 140 missende waarden (mediaan: 5,00)\par
\bullet \b Categorische kolommen:\b0 Opgevuld met modus (meest voorkomende waarde)\par
   - PackagingApparaat: 115 missende waarden (modus: 'Packager 5')\par
\bullet \b Datumkolommen:\b0 Opgevuld met mediaan datum of standaard tijdstempel\par
   - Registratiedatum: 447 missende waarden (mediaan: 2021-12-19)\par
   - Tijdstempelkolommen: Standaard tijdstempel (2022-01-01 12:00:00)\par

\pard\sa200\sl276\slmult1\b\fs24 2. Onrealistische Waarden\b0\fs22\par
\bullet \b Negatieve waarden:\b0 Omgezet naar absolute waarden\par
   - Leveranciersbeoordeling: 6 negatieve waarden gecorrigeerd\par
   - Fair-Trade Score: 1 negatieve waarde gecorrigeerd\par
   - Klanttevredenheid: 135 negatieve waarden gecorrigeerd\par
\bullet \b Onmogelijke datums:\b0 Vervangen of gecorrigeerd\par
   - FillingDatumTijdEind: 4019 onmogelijke datums gecorrigeerd\par
   - PackagingDatumTijdEind: 4097 onmogelijke datums gecorrigeerd\par
   - Andere tijdstempelkolommen: Vergelijkbare correcties\par
\bullet \b Extreme waarden:\b0 Vervangen door mediaan\par
   - Cyclustijd: 124 extreme waarden (>24 uur) vervangen door mediaan (4,08 uur)\par

\pard\sa200\sl276\slmult1\b\fs24 3. Typfouten en Inconsistenties\b0\fs22\par
\bullet \b PackagingApparaat:\b0 Standaardisatie van namen\par
   - 285 onbekende waarden ('###', 'Onbekend apparaat') gestandaardiseerd naar 'Onbekend'\par
   - Consistente hoofdlettergebruik toegepast\par
\bullet \b Eenheden:\b0 Standaardisatie van notatie\par
   - Cyclustijd: 189 waarden zonder eenheid aangevuld met 'uur'\par
   - 'hour' vervangen door 'uur' voor consistentie\par

\pard\sa200\sl276\slmult1\b\fs24 4. Duplicaten\b0\fs22\par
\bullet \b Exacte duplicaten:\b0 Geen exacte duplicaten gevonden\par
\bullet \b Dubbele batchnummers:\b0 2680 dubbele batchnummers gevonden\par
   - Originele batchnummers behouden in kolom 'Original_Batchnr'\par
   - Unieke identifiers toegevoegd aan batchnummers (format: [batchnr]_[volgnummer])\par

\pard\sa200\sl276\slmult1\b\fs24 5. Resultaat\b0\fs22\par
\bullet Originele dataset: 8000 rijen, 37 kolommen\par
\bullet Opgeschoonde dataset: 8000 rijen, 38 kolommen\par
\bullet Alle missende waarden opgevuld\par
\bullet Alle onrealistische waarden gecorrigeerd\par
\bullet Alle inconsistenties gestandaardiseerd\par
\bullet Alle duplicaten behandeld\par
\bullet Opgeschoonde data opgeslagen in 'Kikker_opgeschoond.csv'\par

\pard\sa200\sl276\slmult1\i\fs20 Opmerking: Bij het opschonen van de data zijn keuzes gemaakt die de integriteit van de dataset behouden terwijl problematische waarden worden gecorrigeerd. Voor numerieke kolommen is de mediaan gebruikt in plaats van het gemiddelde omdat dit minder gevoelig is voor uitschieters. Voor categorische data is de meest voorkomende waarde (modus) gebruikt. Dubbele batchnummers zijn niet verwijderd maar voorzien van unieke identifiers om geen data te verliezen.\i0\fs22\par
}
