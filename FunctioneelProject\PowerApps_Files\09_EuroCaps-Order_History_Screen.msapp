{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Order History", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "OrderHistoryScreen", "Controls": [{"Name": "DateRangeDropdown", "ControlType": "Dropdown", "Items": "[\"Last 30 Days\", \"Last 90 Days\", \"This Month\", \"Last Month\"]"}, {"Name": "StatusFilterDropdown", "ControlType": "Dropdown", "Items": "[\"All Status\", \"New\", \"Processing\", \"Shipped\", \"Delivered\"]"}, {"Name": "OrderHistoryGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "SortByColumns(Filter(Orders, FilterConditions), \"OrderDate\", Descending)", "OnSelect": "Navigate(OrderDetailScreen, ScreenTransition.Fade, {SelectedOrder: ThisItem})"}, {"Name": "ExportButton", "ControlType": "<PERSON><PERSON>", "Text": "📤 EXPORT", "Fill": "RGBA(16, 124, 16, 1)"}]}], "DataSources": [{"Name": "Orders", "Type": "Excel"}, {"Name": "Customers", "Type": "Excel"}]}