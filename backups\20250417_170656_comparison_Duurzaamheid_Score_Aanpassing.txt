Comparison Report: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Score_Aanpassing
Timestamp: 2025-04-17 17:06:56
Original file: Kikker.csv
Modified file: Ki<PERSON>_cleaned.csv

=== BASIC COMPARISON ===
Original shape: (8000, 37)
Modified shape: (8000, 38)

New columns: Original_Batchnr

=== COLUMN CHANGES ===
Column 'FillingDatumTijdEind' has changes:
  - 4278 values changed
  - Sample of changes (first 5):
    Row 0: '2023-08-15 20:55:47.293446' -> '2023-08-15 20:12:47.293446'
    Row 1: '2020-08-06 16:50:09.006882' -> '2020-08-06 16:12:09.006882'
    Row 2: '2020-07-15 14:40:28.427630' -> '2020-07-15 14:12:28.427630'
    Row 4: '2022-03-25 15:42:04.750057' -> '2022-03-25 15:12:04.750057'
    Row 5: '2020-05-23 00:35:12.441245' -> '2020-05-23 00:12:12.441245'

Column 'Energieverbruik' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '999999 kWh' -> '266.0'
    Row 1: '441.0 kWh' -> '441.0'
    Row 2: '250.0 kWh' -> '250.0'
    Row 3: '267.0 kWh' -> '267.0'
    Row 4: '276.0 kWh' -> '276.0'

Column 'GrindingDatumTijdStart' has changes:
  - 4343 values changed
  - Sample of changes (first 5):
    Row 0: '2023-08-15 19:52:39' -> '2023-08-15 19:12:39'
    Row 2: '2020-07-15 13:30:32' -> '2020-07-15 13:12:32'
    Row 3: '2020-10-28 11:38:06' -> '2020-10-28 11:12:06'
    Row 4: '2022-03-25 14:33:40' -> '2022-03-25 14:12:40'
    Row 5: '2020-05-22 23:58:20' -> '2020-05-22 23:12:20'

Column 'Cyclustijd' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '2.57 uur' -> '3 uur'
    Row 1: '4.26 uur' -> '4 uur'
    Row 2: 'nan' -> '4 uur'
    Row 3: '4.80 uur' -> '5 uur'
    Row 4: '3.66 uur' -> '4 uur'

Column 'PackagingApparaat' has changes:
  - 400 values changed
  - Sample of changes (first 5):
    Row 61: '###' -> 'Onbekend'
    Row 68: 'nan' -> 'Packager 5'
    Row 140: 'Onbekend apparaat' -> 'Onbekend'
    Row 152: 'nan' -> 'Packager 5'
    Row 167: '###' -> 'Onbekend'

Column 'Registratiedatum' has changes:
  - 447 values changed
  - Sample of changes (first 5):
    Row 9: 'nan' -> '2021-12-19'
    Row 35: 'nan' -> '2021-12-19'
    Row 61: 'nan' -> '2021-12-19'
    Row 86: 'nan' -> '2021-12-19'
    Row 113: 'nan' -> '2021-12-19'

Column 'Batchnr' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '9972' -> '9972_0'
    Row 1: '7344' -> '7344_0'
    Row 2: '4833' -> '4833_0'
    Row 3: '6155' -> '6155_0'
    Row 4: '5426' -> '5426_0'

Column 'PackagingDatumTijdEind' has changes:
  - 4365 values changed
  - Sample of changes (first 5):
    Row 0: '2023-08-15 22:31:31.409449' -> '2023-08-15 22:12:31.409449'
    Row 1: '2020-08-06 18:31:46.236289' -> '2020-08-06 18:12:46.236289'
    Row 2: '2020-07-15 16:36:12.719674' -> '2020-07-15 16:12:12.719674'
    Row 3: '2020-10-28 13:51:01.296349' -> '2020-10-28 13:12:01.296349'
    Row 5: '2020-05-23 01:30:35.261616' -> '2020-05-23 01:12:35.261616'

Column 'Leveranciersbeoordeling' has changes:
  - 417 values changed
  - Original statistics:
    Min: -16.0, Max: 49.0, Mean: 4.5310, Median: 4.0
  - Modified statistics:
    Min: 1.0, Max: 10.0, Mean: 4.4965, Median: 4.0
  - Sample of changes (first 5):
    Row 26: nan -> 4.0
    Row 35: nan -> 4.0
    Row 40: nan -> 4.0
    Row 50: nan -> 4.0
    Row 64: nan -> 4.0

Column 'CO2-Footprint' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '1.00 kg CO2/kg' -> '1.002'
    Row 1: '8.32 kg CO2/kg' -> '8.322'
    Row 2: '3.01 kg CO2/kg' -> '3.012'
    Row 3: '5.21 kg CO2/kg' -> '5.212'
    Row 4: '10.00 kg CO2/kg' -> '10.002'

Column 'Laatste Audit' has changes:
  - 382 values changed
  - Sample of changes (first 5):
    Row 45: 'nan' -> '2022-01-01 12:00:00'
    Row 81: 'nan' -> '2022-01-01 12:00:00'
    Row 109: 'nan' -> '2022-01-01 12:00:00'
    Row 149: 'nan' -> '2022-01-01 12:00:00'
    Row 154: 'nan' -> '2022-01-01 12:00:00'

Column 'Voorraadniveaus' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '300 units' -> '300.0'
    Row 1: '200 units' -> '200.0'
    Row 2: '300 units' -> '300.0'
    Row 3: '100 units' -> '100.0'
    Row 4: '500 units' -> '500.0'

Column 'Cost' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '428.69 euros' -> '428.69'
    Row 1: '512.77 euros' -> '512.77'
    Row 2: '590.85 euros' -> '590.85'
    Row 3: '540.10 euros' -> '540.1'
    Row 4: '482.82 euros' -> '482.82'

Column 'Defectpercentage' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '1.17%' -> '100.0'
    Row 1: '2.22%' -> '100.0'
    Row 2: '2.78%' -> '100.0'
    Row 3: '1.59%' -> '100.0'
    Row 4: '2.19%' -> '100.0'

Column 'PackagingDatumTijdStart' has changes:
  - 4352 values changed
  - Sample of changes (first 5):
    Row 0: '2023-08-15 21:54:12.024316' -> '2023-08-15 21:12:12.024316'
    Row 4: '2022-03-25 16:51:39.145502' -> '2022-03-25 16:12:39.145502'
    Row 7: '2023-03-06 22:38:41.770829' -> '2023-03-06 22:12:41.770829'
    Row 8: '31-02-2025 25:61:61' -> '2022-01-01 12:00:00.000000'
    Row 9: '2023-04-07 15:34:55.987942' -> '2023-04-07 15:12:55.987942'

Column 'Klantretourpercentage' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '1.33%' -> '1.3'
    Row 1: '1.03%' -> '1.0'
    Row 2: '1.06%' -> '1.1'
    Row 3: '0.88%' -> '0.9'
    Row 4: '1.74%' -> '1.7'

Column 'FillingDatumTijdStart' has changes:
  - 4309 values changed
  - Sample of changes (first 5):
    Row 0: '2023-08-15 20:35:50.887546' -> '2023-08-15 20:12:50.887546'
    Row 1: '2020-08-06 16:39:03.861956' -> '2020-08-06 16:12:03.861956'
    Row 2: '31-02-2025 25:61:61' -> '2022-01-01 12:00:00.000000'
    Row 8: '2021-06-18 21:54:42.992100' -> '2021-06-18 21:12:42.992100'
    Row 9: '2023-04-07 13:57:56.491980' -> '2023-04-07 13:12:56.491980'

Column 'Klanttevredenheid' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '3' -> '3.0'
    Row 1: 'onbekend' -> '5.0'
    Row 2: '8' -> '8.0'
    Row 3: '2' -> '2.0'
    Row 4: 'onbekend' -> '5.0'

Column 'Benuttingsgraad' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '75.71%' -> '76'
    Row 1: '72.36%' -> '72'
    Row 2: '79.13%' -> '79'
    Row 3: '77.17%' -> '77'
    Row 4: '67.34%' -> '67'

Column 'GrindingID' has changes:
  - 138 values changed
  - Sample of changes (first 5):
    Row 109: '9999' -> 'Onbekend'
    Row 199: '9999' -> 'Onbekend'
    Row 381: '9999' -> 'Onbekend'
    Row 445: '9999' -> 'Onbekend'
    Row 637: '9999' -> 'Onbekend'

Column 'Gewichtscontrole' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '0.91 kg' -> '1'
    Row 1: '1.33 kg' -> '1'
    Row 2: '1.13 kg' -> '1'
    Row 3: '1.02 kg' -> '1'
    Row 4: '1.13 kg' -> '1'

Column 'FillingID' has changes:
  - 7185 values changed
  - Original statistics:
    Min: 1, Max: 9999, Mean: 4955.2328, Median: 4924.5
  - Modified statistics:
    Min: 1, Max: 1000, Mean: 500.4000, Median: 499.0
  - Sample of changes (first 5):
    Row 1: 8486 -> 910
    Row 2: 6640 -> 79
    Row 3: 3362 -> 410
    Row 4: 9439 -> 41
    Row 6: 4974 -> 603

Column 'Duurzaamheid Score' has changes:
  - 8000 values changed
  - Sample of changes (first 5):
    Row 0: '37' -> '37.0'
    Row 1: '28' -> '28.0'
    Row 2: '64' -> '64.0'
    Row 3: '45' -> '45.0'
    Row 4: '46' -> '46.0'

Column 'GrindingDatumTijdEind' has changes:
  - 4323 values changed
  - Sample of changes (first 5):
    Row 1: '2020-08-06 15:56:08.236780' -> '2020-08-06 15:12:08.236780'
    Row 2: '2020-07-15 13:48:30.289711' -> '2020-07-15 13:12:30.289711'
    Row 3: '2020-10-28 11:46:28.607100' -> '2020-10-28 11:12:28.607100'
    Row 4: '2022-03-25 14:52:50.786653' -> '2022-03-25 14:12:50.786653'
    Row 6: '2023-01-12 14:45:23.152003' -> '2023-01-12 14:12:23.152003'

Column 'Fair-Trade Score' has changes:
  - 4 values changed
  - Original statistics:
    Min: -58, Max: 399, Mean: 49.3927, Median: 49.0
  - Modified statistics:
    Min: 0, Max: 144, Mean: 49.3339, Median: 49.0
  - Sample of changes (first 5):
    Row 2957: 289 -> 144
    Row 3737: -58 -> 58
    Row 6626: 399 -> 144
    Row 6727: 331 -> 144

