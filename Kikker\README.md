# Kwaliteitsmanagement Analyse voor Americaps

Dit project bevat scripts voor het opschonen en analyseren van de Kikker.csv dataset voor kwaliteitsmanagement doeleinden.

## Bestanden

- `clean_kikker_data.py`: <PERSON>ript om de Kikker.csv dataset op te schonen
- `analyze_quality_management.py`: Script om de opgeschoonde data te analyseren voor vier kwaliteitsmanagementmethoden
- `Ki<PERSON>_cleaned.csv`: Opgeschoonde dataset

## Opschoonproces

Het opschoonscript voert de volgende acties uit:

1. **Laden van de dataset**: Laadt de Kikker.csv dataset
2. **Opschonen van percentages**: Converteert percentagestrings naar numerieke waarden
   - Klantretourpercentage
   - Defectpercentage
   - Benuttingsgraad
3. **Opschonen van numerieke waarden**: Extraheert numerieke waarden en verwijdert onrealistische waarden
   - Gewichtscontrole
   - Energieverbruik (verwijdert waarden >10000 kWh)
   - Cost (verwijdert waarden >10000 euros)
   - Cyclustijd
   - ProcessTime
   - Voorraadniveaus (verwijdert waarden >10000 units)
4. **Opschonen van categorische waarden**: Standaardiseert categorische waarden
   - Panel Test
   - PackagingApparaat
   - Klanttevredenheid (verwijdert waarden >5 voor een 5-puntsschaal)
5. **Opschonen van datums**: Converteert datumstrings naar datetime objecten

## Kwaliteitsmanagement Analyses

Het analyse script voert analyses uit voor vier kwaliteitsmanagementmethoden:

### 1. Six Sigma
- **Onderzoek**: Analyse van defectpercentage per batch
- **Bevindingen**:
  - Top batches met hoogste defectpercentage geïdentificeerd
  - Proces capability (Cp) voor gewichtscontrole is 0.16 (onder de gewenste 1.33)
- **Advies**: Implementeer Statistical Process Control (SPC) voor gewichtscontrole

### 2. Lean
- **Onderzoek**: Analyse van verspilling in procestijd en voorraadniveaus
- **Bevindingen**:
  - Gemiddelde cyclustijd: 4.10 uur
  - Gemiddelde benuttingsgraad: 74.93%
  - Gemiddelde voorraadniveaus: 311 eenheden
- **Advies**: Implementeer Value Stream Mapping om verspilling te identificeren en te elimineren

### 3. Theory of Constraints (TOC)
- **Onderzoek**: Identificatie van knelpunten in het proces
- **Bevindingen**:
  - Bottleneck proces: Packaging (0.50 uur)
  - Andere processen: Grinding (0.34 uur), Filling (0.25 uur)
  - Energieverbruik per machine geanalyseerd
- **Advies**: Optimaliseer het verpakkingsproces (bottleneck) en implementeer Drum-Buffer-Rope planning

### 4. Kaizen
- **Onderzoek**: Evaluatie van klanttevredenheid en klantretourpercentage
- **Bevindingen**:
  - Gemiddelde klanttevredenheid: 3.32/5
  - Gemiddeld klantretourpercentage: 1.00%
  - Klanttevredenheid per koffieboontype geanalyseerd
- **Advies**: Organiseer kleine, frequente verbeterinitiatieven gericht op klanttevredenheid

## Gebruik

1. Voer het opschoonscript uit om de data op te schonen:
   ```
   python clean_kikker_data.py
   ```

2. Voer het analyse script uit om de opgeschoonde data te analyseren:
   ```
   python analyze_quality_management.py
   ```

## Resultaten

De analyses bieden inzicht in verbetermogelijkheden voor het productieproces van Americaps, met specifieke aanbevelingen voor elke kwaliteitsmanagementmethode.

## Vereisten

- Python 3.6+
- pandas
- numpy
