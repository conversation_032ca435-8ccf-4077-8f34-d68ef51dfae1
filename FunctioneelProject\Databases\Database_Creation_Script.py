import pandas as pd
import os

# Create databases directory if it doesn't exist
os.makedirs('FunctioneelProject/Databases', exist_ok=True)

# 1. Users Database
users_data = [
    {'UserID': 1, 'Username': 'john.doe', 'Email': '<EMAIL>', 'FirstName': '<PERSON>', 'LastName': 'Doe', 'Role': 'Operations Manager', 'Status': 'Active', 'LastLogin': '2025-01-16 09:15', 'Department': 'Operations'},
    {'UserID': 2, 'Username': 'sarah.johnson', 'Email': '<EMAIL>', 'FirstName': 'Sarah', 'LastName': '<PERSON>', 'Role': 'Purchasing Manager', 'Status': 'Active', 'LastLogin': '2025-01-16 08:30', 'Department': 'Purchasing'},
    {'UserID': 3, 'Username': 'mike.chen', 'Email': '<EMAIL>', 'FirstName': '<PERSON>', 'LastName': 'Chen', 'Role': 'Production Employee', 'Status': 'Active', 'LastLogin': '2025-01-16 07:45', 'Department': 'Production'},
    {'UserID': 4, 'Username': 'lisa.taylor', 'Email': '<EMAIL>', 'FirstName': 'Lisa', 'LastName': 'Taylor', 'Role': 'Logistics Manager', 'Status': 'Pending', 'LastLogin': None, 'Department': 'Logistics'},
    {'UserID': 5, 'Username': 'tom.wilson', 'Email': '<EMAIL>', 'FirstName': 'Tom', 'LastName': 'Wilson', 'Role': 'Operations Manager', 'Status': 'Inactive', 'LastLogin': '2025-01-15 16:20', 'Department': 'Operations'}
]

# 2. Customers Database
customers_data = [
    {'CustomerID': 1, 'CompanyName': 'Coffee World', 'ContactPerson': 'David Lee', 'Email': '<EMAIL>', 'Phone': '+31-20-1234567', 'Address': 'Koffiestraat 123, Amsterdam', 'Status': 'Active', 'CustomerType': 'Premium', 'RegistrationDate': '2023-03-15'},
    {'CustomerID': 2, 'CompanyName': 'Bean Lovers', 'ContactPerson': 'John Smith', 'Email': '<EMAIL>', 'Phone': '+31-20-2345678', 'Address': 'Beanstraat 456, Rotterdam', 'Status': 'Active', 'CustomerType': 'Standard', 'RegistrationDate': '2023-05-20'},
    {'CustomerID': 3, 'CompanyName': 'Café Express', 'ContactPerson': 'Maria Garcia', 'Email': '<EMAIL>', 'Phone': '+31-20-3456789', 'Address': 'Expresslaan 789, Utrecht', 'Status': 'Active', 'CustomerType': 'Premium', 'RegistrationDate': '2023-07-10'},
    {'CustomerID': 4, 'CompanyName': 'Morning Brew', 'ContactPerson': 'Sarah Johnson', 'Email': '<EMAIL>', 'Phone': '+31-20-4567890', 'Address': 'Morningweg 321, Den Haag', 'Status': 'Active', 'CustomerType': 'Standard', 'RegistrationDate': '2023-09-05'},
    {'CustomerID': 5, 'CompanyName': 'The Daily Cup', 'ContactPerson': 'Robert Brown', 'Email': '<EMAIL>', 'Phone': '+31-20-5678901', 'Address': 'Cupstraat 654, Eindhoven', 'Status': 'Active', 'CustomerType': 'Standard', 'RegistrationDate': '2023-11-12'}
]

# 3. Products Database
products_data = [
    {'ProductID': 1, 'ProductName': 'Espresso Classic', 'Category': 'Espresso', 'Price': 12.50, 'Stock': 50, 'SKU': 'ESP-001', 'Description': 'Rich and intense espresso with perfect crema', 'Size': '10 capsules', 'Intensity': 8, 'Origin': 'Italy'},
    {'ProductID': 2, 'ProductName': 'Lungo Intense', 'Category': 'Lungo', 'Price': 14.00, 'Stock': 30, 'SKU': 'LUN-001', 'Description': 'Full-bodied lungo with aromatic intensity', 'Size': '10 capsules', 'Intensity': 7, 'Origin': 'Brazil'},
    {'ProductID': 3, 'ProductName': 'Ristretto Strong', 'Category': 'Ristretto', 'Price': 13.75, 'Stock': 25, 'SKU': 'RIS-001', 'Description': 'Concentrated coffee with bold flavor', 'Size': '10 capsules', 'Intensity': 9, 'Origin': 'Colombia'},
    {'ProductID': 4, 'ProductName': 'Vanilla Flavored', 'Category': 'Flavored', 'Price': 15.50, 'Stock': 40, 'SKU': 'VAN-001', 'Description': 'Smooth vanilla-flavored coffee capsules', 'Size': '10 capsules', 'Intensity': 5, 'Origin': 'Guatemala'},
    {'ProductID': 5, 'ProductName': 'Decaf Espresso', 'Category': 'Decaf', 'Price': 11.50, 'Stock': 35, 'SKU': 'DEC-001', 'Description': 'Decaffeinated espresso without compromise', 'Size': '10 capsules', 'Intensity': 6, 'Origin': 'Peru'}
]

# 4. Orders Database
orders_data = [
    {'OrderID': 1089, 'CustomerID': 1, 'OrderDate': '2025-01-15', 'DeliveryDate': '2025-01-22', 'Status': 'New', 'Priority': 'Normal', 'Subtotal': 135.50, 'VAT': 28.46, 'Total': 163.96, 'Notes': 'Please deliver before noon', 'CreatedBy': 1},
    {'OrderID': 1088, 'CustomerID': 2, 'OrderDate': '2025-01-14', 'DeliveryDate': '2025-01-21', 'Status': 'Processing', 'Priority': 'High', 'Subtotal': 89.25, 'VAT': 18.74, 'Total': 107.99, 'Notes': 'Urgent delivery required', 'CreatedBy': 2},
    {'OrderID': 1087, 'CustomerID': 3, 'OrderDate': '2025-01-14', 'DeliveryDate': '2025-01-21', 'Status': 'Shipped', 'Priority': 'Normal', 'Subtotal': 156.00, 'VAT': 32.76, 'Total': 188.76, 'Notes': '', 'CreatedBy': 1},
    {'OrderID': 1086, 'CustomerID': 4, 'OrderDate': '2025-01-13', 'DeliveryDate': '2025-01-20', 'Status': 'Delivered', 'Priority': 'Normal', 'Subtotal': 95.75, 'VAT': 20.11, 'Total': 115.86, 'Notes': '', 'CreatedBy': 3},
    {'OrderID': 1085, 'CustomerID': 1, 'OrderDate': '2025-01-12', 'DeliveryDate': '2025-01-19', 'Status': 'Delivered', 'Priority': 'Low', 'Subtotal': 210.00, 'VAT': 44.10, 'Total': 254.10, 'Notes': 'Large order', 'CreatedBy': 2}
]

# 5. Order Items Database
order_items_data = [
    {'OrderItemID': 1, 'OrderID': 1089, 'ProductID': 1, 'Quantity': 5, 'UnitPrice': 12.50, 'Total': 62.50},
    {'OrderItemID': 2, 'OrderID': 1089, 'ProductID': 2, 'Quantity': 3, 'UnitPrice': 14.00, 'Total': 42.00},
    {'OrderItemID': 3, 'OrderID': 1089, 'ProductID': 4, 'Quantity': 2, 'UnitPrice': 15.50, 'Total': 31.00},
    {'OrderItemID': 4, 'OrderID': 1088, 'ProductID': 1, 'Quantity': 3, 'UnitPrice': 12.50, 'Total': 37.50},
    {'OrderItemID': 5, 'OrderID': 1088, 'ProductID': 3, 'Quantity': 2, 'UnitPrice': 13.75, 'Total': 27.50},
    {'OrderItemID': 6, 'OrderID': 1088, 'ProductID': 5, 'Quantity': 2, 'UnitPrice': 11.50, 'Total': 23.00}
]

# Save all databases to Excel files
try:
    with pd.ExcelWriter('FunctioneelProject/Databases/EuroCaps_Users.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(users_data).to_excel(writer, sheet_name='Users', index=False)

    with pd.ExcelWriter('FunctioneelProject/Databases/EuroCaps_Customers.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(customers_data).to_excel(writer, sheet_name='Customers', index=False)

    with pd.ExcelWriter('FunctioneelProject/Databases/EuroCaps_Products.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(products_data).to_excel(writer, sheet_name='Products', index=False)

    with pd.ExcelWriter('FunctioneelProject/Databases/EuroCaps_Orders.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(orders_data).to_excel(writer, sheet_name='Orders', index=False)

    with pd.ExcelWriter('FunctioneelProject/Databases/EuroCaps_OrderItems.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(order_items_data).to_excel(writer, sheet_name='OrderItems', index=False)

    print('✅ All Excel databases created successfully!')
    print('📁 Location: FunctioneelProject/Databases/')
    print('📊 Files created:')
    print('  - EuroCaps_Users.xlsx')
    print('  - EuroCaps_Customers.xlsx') 
    print('  - EuroCaps_Products.xlsx')
    print('  - EuroCaps_Orders.xlsx')
    print('  - EuroCaps_OrderItems.xlsx')
    
except Exception as e:
    print(f'❌ Error creating databases: {e}')
