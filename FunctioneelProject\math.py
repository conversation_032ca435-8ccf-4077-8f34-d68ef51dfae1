# from math import floor
# #Definieer variabelen
# vijftig = 0
# twintig = 0
# tien = 0
# vijf = 0
# bedrag_o = 0
# #Vraag het bedrag op en stop het in een variabele
# bedrag = int(input('Wat is het bedrag?'))
# #Bedrag_o is het originele bedrag, met de variabele bedrag wordt gerekend
# bedrag_o = bedrag
# while bedrag != 0:

#     if (bedrag / 50) >= 1:

#         vijftig = floor(bedrag / 50)

#         bedrag = bedrag - (vijftig * 50)
#     elif (bedrag / 20) >= 1:

#         twintig = floor(bedrag / 20)

#         bedrag = bedrag - (twintig * 20)
#     elif (bedrag / 10) >= 1:

#         tien = floor(bedrag / 10)

#         bedrag = bedrag - (tien * 10)
#     elif (bedrag / 5) >= 1:

#         vijf = floor(bedrag / 5)

#         bedrag = bedrag - (vijf * 5)
#     else:
#         break
        
# print ('het bedrag ' + str(bedrag_o) + ' is onder te verdelen in: ' + str(vijftig) + 'briefje(s) van 50, ' + str(twintig) + ' briefje(s) van 20, ' + str(tien) + ' briefje(s) van 10 en ' + str(vijf) + ' briefje(s) van 5.')
        
namen = ["Alice", "Bob"]
namen.append("Charlie")

print(namen)
