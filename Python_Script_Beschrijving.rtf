{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Beschrijving Python-script voor Data-opschoning\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft het Python-script (kikker_opschonen.py) dat is gebruikt voor het opschonen van de <PERSON>kker dataset.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Overzicht van het Script\b0\fs22\par
Het script 'kikker_opschonen.py' is ontwikkeld om de Kikker dataset systematisch op te schonen volgens de volgende stappen:\par
\bullet Data inladen\par
\bullet Ontbrekende waarden behandelen\par
\bullet Onrealistische waarden corrigeren\par
\bullet Typfouten en inconsistenties standaardiseren\par
\bullet Duplicaten verwijderen of behandelen\par
\bullet Opgeschoonde data opslaan\par

Het script maakt gebruik van de pandas en numpy bibliotheken voor data manipulatie en analyse.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Belangrijkste Functies en Componenten\b0\fs22\par

\b 2.1 Data Inladen\b0\par
\i # Stap 1: Data inladen\par
print("Stap 1: Data inladen...")\par
df = pd.read_csv("Kikker.csv")\par
print(f"Dataset geladen: \{df.shape[0]\} rijen, \{df.shape[1]\} kolommen")\i0\par
\b Toelichting:\b0 Deze code laadt de Kikker.csv dataset in een pandas DataFrame. Het script probeert eerst de dataset te laden met automatische encoding detectie, en als dat mislukt, probeert het verschillende encodings (utf-8, latin1, ISO-8859-1, cp1252).\par

\b 2.2 Ontbrekende Waarden Behandelen\b0\par
\i # Stap 2: Ontbrekende waarden behandelen\par
print("\\nStap 2: Ontbrekende waarden behandelen...")\par
# Keuze: Numerieke kolommen - mediaan gebruiken\par
numerieke_kolommen = ['Cost', 'Voorraadniveaus', 'Benuttingsgraad', ...]\par
for kolom in numerieke_kolommen:\par
    if kolom in df.columns:\par
        # Converteer naar numeriek als het nog niet numeriek is\par
        if df[kolom].dtype == 'object':\par
            df[kolom] = pd.to_numeric(df[kolom].str.replace('[^0-9.-]', '', regex=True), errors='coerce')\par
        \par
        # Vul missende waarden in met mediaan\par
        missend = df[kolom].isnull().sum()\par
        if missend > 0:\par
            mediaan = df[kolom].median()\par
            df[kolom].fillna(mediaan, inplace=True)\par
            print(f"  - \{kolom\}: \{missend\} missende waarden opgevuld met mediaan (\{mediaan:.2f\})")\i0\par
\b Toelichting:\b0 Deze code behandelt ontbrekende waarden in numerieke kolommen door ze op te vullen met de mediaan. Voor categorische kolommen wordt de modus (meest voorkomende waarde) gebruikt, en voor datumkolommen wordt de mediaan datum of een standaard tijdstempel gebruikt.\par

\b Keuze en motivatie:\b0 De mediaan is gekozen voor numerieke kolommen omdat deze minder gevoelig is voor uitschieters dan het gemiddelde. Voor categorische data is de modus logischer omdat het de meest voorkomende waarde vertegenwoordigt.\par

\b 2.3 Onrealistische Waarden Corrigeren\b0\par
\i # Stap 3: Onrealistische waarden corrigeren\par
print("\\nStap 3: Onrealistische waarden corrigeren...")\par
\par
# Keuze: Negatieve waarden omzetten naar absolute waarden\par
for kolom in numerieke_kolommen:\par
    if kolom in df.columns:\par
        negatief = (df[kolom] < 0).sum()\par
        if negatief > 0:\par
            df.loc[df[kolom] < 0, kolom] = df.loc[df[kolom] < 0, kolom].abs()\par
            print(f"  - \{kolom\}: \{negatief\} negatieve waarden omgezet naar positief")\par
\par
# Keuze: Onmogelijke datums corrigeren\par
for kolom in tijdstempel_kolommen:\par
    if kolom in df.columns:\par
        # Vervang onmogelijke datums\par
        onmogelijk = df[df[kolom].str.contains('31-02|30-02|31-04|31-06|31-09|31-11|25:|[3-9][0-9]:|:[6-9][0-9]', na=False)].shape[0]\par
        if onmogelijk > 0:\par
            df[kolom] = df[kolom].replace(r'31-02-2025 25:61:61', np.nan, regex=True)\par
            df[kolom] = df[kolom].replace(r'31-02|30-02|31-04|31-06|31-09|31-11', '01-01', regex=True)\par
            df[kolom] = df[kolom].replace(r'25:|[3-9][0-9]:', '12:', regex=True)\par
            df[kolom] = df[kolom].replace(r':[6-9][0-9]', ':00', regex=True)\par
            print(f"  - \{kolom\}: \{onmogelijk\} onmogelijke datums gecorrigeerd")\i0\par
\b Toelichting:\b0 Deze code corrigeert onrealistische waarden zoals negatieve getallen (door ze om te zetten naar absolute waarden) en onmogelijke datums (door ze te vervangen met geldige datums). Ook worden extreme cyclustijden (>24 uur) vervangen door de mediaan.\par

\b Keuze en motivatie:\b0 Negatieve waarden zijn omgezet naar absolute waarden omdat negatieve scores, percentages of tijden niet realistisch zijn in deze context. Onmogelijke datums zijn gecorrigeerd naar geldige datums in plaats van verwijderd, om geen data te verliezen.\par

\b 2.4 Typfouten en Inconsistenties Standaardiseren\b0\par
\i # Stap 4: Typfouten en inconsistenties standaardiseren\par
print("\\nStap 4: Typfouten en inconsistenties standaardiseren...")\par
\par
# Keuze: PackagingApparaat standaardiseren\par
if 'PackagingApparaat' in df.columns:\par
    # Vervang onbekende waarden\par
    onbekend = df[df['PackagingApparaat'].isin(['###', 'Onbekend apparaat'])].shape[0]\par
    df['PackagingApparaat'] = df['PackagingApparaat'].replace(['###', 'Onbekend apparaat'], 'Onbekend')\par
    \par
    # Standaardiseer naamgeving\par
    standaard_count = 0\par
    for i in range(1, 6):\par
        oude_waarde = f"packager \{i\}"\par
        nieuwe_waarde = f"Packager \{i\}"\par
        count = (df['PackagingApparaat'] == oude_waarde).sum()\par
        if count > 0:\par
            df['PackagingApparaat'] = df['PackagingApparaat'].replace(oude_waarde, nieuwe_waarde)\par
            standaard_count += count\i0\par
\b Toelichting:\b0 Deze code standaardiseert inconsistenties in categorische kolommen, zoals verschillende schrijfwijzen voor dezelfde machine ('packager 1' vs 'Packager 1') en verschillende notaties voor eenheden.\par

\b Keuze en motivatie:\b0 Standaardisatie is belangrijk voor accurate analyses, omdat verschillende schrijfwijzen voor dezelfde entiteit anders als verschillende entiteiten worden beschouwd. Door alles te standaardiseren, kunnen we betrouwbaardere groepering en aggregatie uitvoeren.\par

\b 2.5 Duplicaten Verwijderen\b0\par
\i # Stap 5: Duplicaten verwijderen\par
print("\\nStap 5: Duplicaten verwijderen...")\par
\par
# Keuze: Exacte duplicaten verwijderen\par
duplicaten = df.duplicated().sum()\par
if duplicaten > 0:\par
    df.drop_duplicates(inplace=True)\par
    print(f"  - \{duplicaten\} exacte duplicaten verwijderd")\par
else:\par
    print("  - Geen exacte duplicaten gevonden")\par
\par
# Keuze: Duplicaten in batchnummers behandelen\par
if 'Batchnr' in df.columns:\par
    dubbele_batches = df['Batchnr'].duplicated().sum()\par
    if dubbele_batches > 0:\par
        print(f"  - \{dubbele_batches\} dubbele batchnummers gevonden")\par
        print("  - Originele batchnummers behouden maar unieke identifiers toegevoegd")\par
        df['Original_Batchnr'] = df['Batchnr']\par
        df['Batchnr'] = df['Batchnr'].astype(str) + '_' + df.groupby('Batchnr').cumcount().astype(str)\i0\par
\b Toelichting:\b0 Deze code controleert op exacte duplicaten (identieke rijen) en dubbele batchnummers. Exacte duplicaten worden verwijderd, terwijl dubbele batchnummers worden voorzien van unieke identifiers om ze te onderscheiden.\par

\b Keuze en motivatie:\b0 Exacte duplicaten voegen geen waarde toe en kunnen analyses verstoren. Voor dubbele batchnummers is gekozen om ze te behouden maar uniek te maken, omdat ze verschillende informatie bevatten die waardevol kan zijn voor de analyse.\par

\b 2.6 Opgeschoonde Data Opslaan\b0\par
\i # Stap 6: Opgeschoonde data opslaan\par
print("\\nStap 6: Opgeschoonde data opslaan...")\par
df.to_csv("Kikker_opgeschoond.csv", index=False)\par
print(f"Opgeschoonde dataset opgeslagen: \{df.shape[0]\} rijen, \{df.shape[1]\} kolommen")\i0\par
\b Toelichting:\b0 Deze code slaat de opgeschoonde dataset op als een nieuw CSV-bestand zonder de index kolom.\par

\pard\sa200\sl276\slmult1\b\fs26 3. Uitvoering en Resultaten\b0\fs22\par
Het script is uitgevoerd op de originele Kikker.csv dataset en heeft de volgende resultaten opgeleverd:\par

\bullet \b Ontbrekende waarden:\b0 3.097 missende waarden opgevuld\par
\bullet \b Onrealistische waarden:\b0 12.856 onrealistische waarden gecorrigeerd\par
\bullet \b Inconsistenties:\b0 975 inconsistenties gestandaardiseerd\par
\bullet \b Duplicaten:\b0 0 exacte duplicaten verwijderd, 2.680 dubbele batchnummers voorzien van unieke identifiers\par
\bullet \b Resultaat:\b0 Een opgeschoonde dataset (Kikker_opgeschoond.csv) met 8.000 rijen en 38 kolommen\par

\pard\sa200\sl276\slmult1\b\fs26 4. Belangrijke Keuzes en Motivatie\b0\fs22\par
Bij het ontwikkelen van het opschoningsscript zijn de volgende belangrijke keuzes gemaakt:\par

\bullet \b Behoud van alle rijen:\b0 Er is gekozen om alle rijen te behouden en missende waarden op te vullen in plaats van rijen te verwijderen, om geen waardevolle informatie te verliezen.\par

\bullet \b Mediaan vs. gemiddelde:\b0 Voor numerieke kolommen is de mediaan gebruikt in plaats van het gemiddelde, omdat de mediaan robuuster is tegen uitschieters.\par

\bullet \b Correctie in plaats van verwijdering:\b0 Onrealistische waarden zijn gecorrigeerd in plaats van verwijderd, om de integriteit van de dataset te behouden.\par

\bullet \b Standaardisatie van categorische waarden:\b0 Inconsistente categorische waarden zijn gestandaardiseerd om betrouwbare groepering en aggregatie mogelijk te maken.\par

\bullet \b Behandeling van dubbele batchnummers:\b0 Dubbele batchnummers zijn voorzien van unieke identifiers in plaats van verwijderd, omdat ze verschillende informatie bevatten die waardevol kan zijn.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
Het Python-script voor data-opschoning heeft effectief alle datakwaliteitsproblemen in de Kikker dataset aangepakt, resulterend in een schone, consistente en betrouwbare dataset die klaar is voor analyse. De gemaakte keuzes zijn gebaseerd op het behoud van zoveel mogelijk informatie, terwijl de betrouwbaarheid en bruikbaarheid van de data wordt verbeterd.\par

\pard\sa200\sl276\slmult1\i\fs20 Het volledige script (kikker_opschonen.py) is beschikbaar in de bijbehorende bestanden.\i0\fs22\par
}
