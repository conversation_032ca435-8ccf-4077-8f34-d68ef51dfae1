{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Overzicht van Deliverables\par
Analyse Productieproces Americaps Koffiecapsules\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document geeft een overzicht van alle deliverables die zijn opgeleverd voor de analyse van het productieproces van Americaps koffiecapsules.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Python-script voor Data-opschoning\b0\fs22\par
\b Bestandsnaam:\b0 kikker_opschonen.py\par
\b Beschrijving:\b0 Python-script dat de Kikker dataset systematisch opschoont volgens de volgende stappen:\par
\bullet Data inladen\par
\bullet Ontbrekende waarden behandelen\par
\bullet Onrealistische waarden corrigeren\par
\bullet Typfouten en inconsistenties standaardiseren\par
\bullet Duplicaten verwijderen of behandelen\par
\bullet Opgeschoonde data opslaan\par

\b Documentatie:\b0 Het script bevat uitgebreide commentaarregels die elke stap en keuze toelichten. Daarnaast is er een apart document (Python_Script_Beschrijving.rtf) dat het script in detail beschrijft.\par

\b Belangrijkste keuzes:\b0\par
\bullet Behoud van alle rijen om geen waardevolle informatie te verliezen\par
\bullet Gebruik van mediaan voor numerieke kolommen vanwege robuustheid tegen uitschieters\par
\bullet Correctie van onrealistische waarden in plaats van verwijdering\par
\bullet Standaardisatie van categorische waarden voor betrouwbare analyses\par
\bullet Unieke identifiers voor dubbele batchnummers om informatie te behouden\par

\pard\sa200\sl276\slmult1\b\fs26 2. Opgeschoonde Dataset\b0\fs22\par
\b Bestandsnaam:\b0 Kikker_opgeschoond.csv\par
\b Beschrijving:\b0 De opgeschoonde versie van de Kikker dataset, zonder missende waarden, onrealistische gegevens of inconsistenties.\par

\b Kenmerken:\b0\par
\bullet Aantal rijen: 8.000 (alle originele rijen behouden)\par
\bullet Aantal kolommen: 38 (inclusief nieuwe kolom 'Original_Batchnr')\par
\bullet Geen missende waarden\par
\bullet Geen onrealistische waarden\par
\bullet Gestandaardiseerde categorische waarden\par
\bullet Unieke batchnummers\par

\b Documentatie:\b0 Een apart document (Opgeschoonde_Dataset_Beschrijving.rtf) beschrijft de opgeschoonde dataset in detail, inclusief alle veranderingen ten opzichte van de originele dataset.\par

\pard\sa200\sl276\slmult1\b\fs26 3. Management Samenvatting\b0\fs22\par
\b Bestandsnaam:\b0 Management_Samenvatting.rtf\par
\b Beschrijving:\b0 Een beknopt document dat de kern van de analyse en resultaten beschrijft, gericht op de belangrijkste bevindingen en aanbevelingen.\par

\b Inhoud:\b0\par
\bullet Datakwaliteit en Opschoning: Overzicht van de datakwaliteitsproblemen en de aanpak voor opschoning\par
\bullet Belangrijkste Bevindingen: Kernbevindingen op het gebied van kwaliteit, efficiëntie en duurzaamheid\par
\bullet Aanbevelingen volgens Kwaliteitsmanagementmethoden: Concrete aanbevelingen op basis van Six Sigma, Lean, TOC, Kaizen en TQM\par
\bullet Verwachte Impact op Bedrijfsresultaten: Kwantificering van de verwachte verbeteringen in kwaliteit, efficiëntie en kosten\par
\bullet Implementatieplan: Stappenplan voor de implementatie van de aanbevelingen op korte, middellange en lange termijn\par

\pard\sa200\sl276\slmult1\b\fs26 4. Excel-bestand met Visualisaties\b0\fs22\par
\b Bestandsnaam:\b0 Productieproces_Visualisaties.xlsx\par
\b Beschrijving:\b0 Excel-bestand met verschillende visualisaties van de opgeschoonde Kikker dataset, inclusief een dashboard met de belangrijkste grafieken.\par

\b Werkbladen en Visualisaties:\b0\par
\bullet Dashboard: Overzicht van de belangrijkste grafieken, gegroepeerd in categorieën (kwaliteit, efficiëntie, duurzaamheid)\par
\bullet Defect per Machine: Staafdiagram van het defectpercentage per verpakkingsmachine\par
\bullet Defect per Dag: Lijngrafiek van het defectpercentage per productiedag\par
\bullet Cyclustijd per Machine: Staafdiagram van de cyclustijd per verpakkingsmachine\par
\bullet Benutting per Machine: Staafdiagram van de benuttingsgraad per verpakkingsmachine\par
\bullet Energie per Machine: Staafdiagram van het energieverbruik per verpakkingsmachine\par
\bullet Panel Test: Taartdiagram van de Panel Test resultaten\par
\bullet Pareto Analyse: Pareto-diagram van defectpercentages per machine-koffieboon combinatie\par
\bullet Machine Vergelijking: Vergelijking van machines op verschillende metrieken\par

\b Documentatie:\b0 Elke visualisatie in het Excel-bestand is voorzien van een duidelijke titel, as-labels en legenda. Daarnaast is er een apart document (Excel_Visualisaties_Beschrijving.rtf) dat alle visualisaties in detail beschrijft.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Video-presentatie\b0\fs22\par
\b Bestandsnaam:\b0 [Video-bestand]\par
\b Beschrijving:\b0 Een 5-minuten video-presentatie die de belangrijkste aspecten van de analyse toelicht, inclusief de data-opschoning, de belangrijkste bevindingen, en de aanbevelingen.\par

\b Inhoud:\b0\par
\bullet Introductie (30 seconden): Overzicht van de analyse en de aanpak\par
\bullet Data-opschoning (1 minuut 30 seconden): Uitleg van de datakwaliteitsproblemen en de aanpak voor opschoning\par
\bullet Data-analyse en Visualisaties (1 minuut 30 seconden): Toelichting bij de belangrijkste visualisaties en bevindingen\par
\bullet Aanbevelingen volgens Kwaliteitsmanagementmethoden (1 minuut 30 seconden): Uitleg van de aanbevelingen op basis van Six Sigma, Lean, TOC, Kaizen en TQM\par
\bullet Conclusie (30 seconden): Samenvatting van de verwachte impact en afronding\par

\b Script:\b0 Een apart document (Video_Presentatie_Script.rtf) bevat het volledige script voor de video-presentatie.\par

\pard\sa200\sl276\slmult1\b\fs26 6. Aanvullende Documentatie\b0\fs22\par
\b Bestandsnamen:\b0\par
\bullet Python_Script_Beschrijving.rtf: Gedetailleerde beschrijving van het Python-script voor data-opschoning\par
\bullet Opgeschoonde_Dataset_Beschrijving.rtf: Gedetailleerde beschrijving van de opgeschoonde dataset\par
\bullet Excel_Visualisaties_Beschrijving.rtf: Gedetailleerde beschrijving van de Excel-visualisaties\par
\bullet Video_Presentatie_Script.rtf: Script voor de video-presentatie\par
\bullet Deliverables_Overzicht.rtf: Dit document, dat een overzicht geeft van alle deliverables\par

\b Beschrijving:\b0 Deze aanvullende documenten bieden gedetailleerde informatie over de verschillende deliverables, zodat de lezer een volledig begrip kan krijgen van de analyse en de resultaten.\par

\pard\sa200\sl276\slmult1\b\fs26 7. Conclusie\b0\fs22\par
De geleverde deliverables vormen samen een complete analyse van het productieproces van Americaps koffiecapsules, van data-opschoning tot concrete aanbevelingen voor verbetering. De analyse is gebaseerd op de opgeschoonde Kikker dataset en maakt gebruik van verschillende kwaliteitsmanagementmethoden (Six Sigma, Lean, TOC, Kaizen, TQM) om verbetermogelijkheden te identificeren en te kwantificeren.\par

De belangrijkste conclusies zijn:\par
\bullet Packager 1 heeft het hoogste defectpercentage en de laagste benuttingsgraad, en verdient daarom de hoogste prioriteit voor verbeteringsinitiatieven\par
\bullet Packager 4 heeft de langste cyclustijd en is een potentiële bottleneck in het productieproces\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik dat nader onderzoek vereist\par
\bullet Door de aanbevelingen te implementeren, kan het bedrijf het defectpercentage met 25% verlagen, de productiecapaciteit met 10% verhogen, en de totale kwaliteitskosten met 15% reduceren\par

\pard\sa200\sl276\slmult1\i\fs20 Dit document geeft een overzicht van alle deliverables die zijn opgeleverd voor de analyse van het productieproces van Americaps koffiecapsules. Raadpleeg de individuele deliverables voor meer gedetailleerde informatie.\i0\fs22\par
}
