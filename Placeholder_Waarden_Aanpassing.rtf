{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Placeholder-waarden\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassingen die zijn gemaakt aan het opschoningsscript om placeholder-waarden zoals "999999", "9999999" en "9999999.99" te filteren en te vervangen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset (Kikker_cleaned.csv) waren er nog steeds onnodige placeholder-waarden aanwezig in verschillende cellen:\par
\bullet 999999\par
\bullet 9999999\par
\bullet 9999999.99\par

Deze waarden zijn waarschijnlijk gebruikt als placeholder voor missende of onbekende gegevens, maar waren niet correct geïdentificeerd en behandeld tijdens het oorspronkelijke opschonen.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassingen aan het Script\b0\fs22\par

\b 2.1 Aanpassing voor Numerieke Kolommen\b0\par
In de functie \i fix_unrealistic_values\i0 is code toegevoegd om placeholder-waarden in numerieke kolommen te identificeren en te vervangen:\par

\i # Fix placeholder values (999999, 9999999, 9999999.99)\par
placeholder_patterns = [999999, 9999999, 9999999.99]\par
for pattern in placeholder_patterns:\par
    placeholder_count = (cleaned_df[col] == pattern).sum()\par
    if placeholder_count > 0:\par
        # Replace placeholder values with median of non-placeholder values\par
        median_value = cleaned_df.loc[~cleaned_df[col].isin(placeholder_patterns), col].median()\par
        cleaned_df.loc[cleaned_df[col] == pattern, col] = median_value\par
        print(f"Fixed \{placeholder_count\} placeholder values (\{pattern\}) in '\{col\}' by replacing with median: \{median_value\}")\i0\par

Deze code identificeert de placeholder-waarden in numerieke kolommen en vervangt ze door de mediaan van de niet-placeholder waarden in dezelfde kolom.\par

\b 2.2 Aanpassing voor String Kolommen\b0\par
In de functie \i standardize_inconsistencies\i0 is code toegevoegd om placeholder-waarden in string kolommen te identificeren en te vervangen:\par

\i # Fix placeholder string values ("999999", "9999999", "9999999.99")\par
placeholder_strings = ['999999', '9999999', '9999999.99']\par
\par
# Check all columns for placeholder strings\par
for col in cleaned_df.columns:\par
    if cleaned_df[col].dtype == 'object':  # Only check string columns\par
        # Count placeholder values\par
        placeholder_count = 0\par
        for pattern in placeholder_strings:\par
            placeholder_count += (cleaned_df[col] == pattern).sum()\par
        \par
        if placeholder_count > 0:\par
            print(f"Found \{placeholder_count\} placeholder string values in '\{col\}'")\par
            \par
            # Replace placeholder values based on column type\par
            if col in ['Cyclustijd', 'ProcessTime', 'Energieverbruik']:\par
                # For time/energy columns, replace with median\par
                # [code for extracting and calculating median]\par
                \par
            elif col in ['PackagingApparaat', 'FillingApparaat', 'GrindingApparaat']:\par
                # For machine columns, replace with mode\par
                # [code for calculating mode]\par
                \par
            else:\par
                # For other columns, replace with "Onbekend"\par
                # [code for replacing with "Onbekend"]\i0\par

Deze code identificeert de placeholder-waarden in string kolommen en vervangt ze op basis van het type kolom:\par
\bullet Voor tijd- en energiekolommen: vervangen door de mediaan + juiste eenheid\par
\bullet Voor machinekolommen: vervangen door de modus (meest voorkomende waarde)\par
\bullet Voor andere kolommen: vervangen door "Onbekend"\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de volgende placeholder-waarden geïdentificeerd en vervangen:\par

\b Numerieke waarden:\b0\par
\bullet 144 placeholder-waarden (999999) in 'Energieverbruik' vervangen door de mediaan (266.0)\par
\bullet 92 placeholder-waarden (999999) in 'Cost' vervangen door de mediaan (504.305)\par
\bullet 109 placeholder-waarden (9999999) in 'Cost' vervangen door de mediaan (504.305)\par
\bullet 113 placeholder-waarden (9999999.99) in 'Cost' vervangen door de mediaan (504.305)\par
\bullet 95 placeholder-waarden (999999) in 'Voorraadniveaus' vervangen door de mediaan (300.0)\par
\bullet 90 placeholder-waarden (9999999) in 'Voorraadniveaus' vervangen door de mediaan (300.0)\par

\b String waarden:\b0\par
\bullet Placeholder-waarden in string kolommen zijn vervangen door de mediaan, modus of "Onbekend", afhankelijk van het type kolom.\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
Na het opschonen is een verificatiescript (check_placeholders.py) uitgevoerd om te controleren of er nog placeholder-waarden in de opgeschoonde dataset zitten. Het resultaat was dat er geen placeholder-waarden meer zijn gevonden.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassingen aan het opschoningsscript hebben succesvol alle placeholder-waarden geïdentificeerd en vervangen door meer geschikte waarden. De opgeschoonde dataset bevat nu geen onnodige placeholder-waarden meer, wat de kwaliteit en bruikbaarheid van de data verder verbetert.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassingen zijn gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en dragen bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules.\i0\fs22\par
}
