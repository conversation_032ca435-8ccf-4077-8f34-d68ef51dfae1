{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Beschrijving Excel-visualisaties\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de visualisaties die zijn gemaakt in het Excel-bestand "Productieproces_Visualisaties.xlsx" op basis van de opgeschoonde Kikker dataset.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Overzicht van het Excel-bestand\b0\fs22\par
Het Excel-bestand "Productieproces_Visualisaties.xlsx" bevat 9 werkbladen met verschillende visualisaties van de opgeschoonde Kikker dataset:\par
\bullet Dashboard: Overzicht van de belangrijkste grafieken\par
\bullet Defect per Machine: Defectpercentage per verpakkingsmachine\par
\bullet Defect per Dag: Defectpercentage per productiedag\par
\bullet Cyclustijd per Machine: Cyclustijd per verpakkingsmachine\par
\bullet Benutting per Machine: Benuttingsgraad per verpakkingsmachine\par
\bullet Energie per Machine: Energieverbruik per verpakkingsmachine\par
\bullet Panel Test: Panel Test resultaten\par
\bullet Pareto Analyse: Pareto-diagram van defectpercentages\par
\bullet Machine Vergelijking: Vergelijking van machines op verschillende metrieken\par

\pard\sa200\sl276\slmult1\b\fs26 2. Beschrijving van de Visualisaties\b0\fs22\par

\b 2.1 Dashboard\b0\par
Het Dashboard werkblad bevat een overzicht van de belangrijkste visualisaties, gegroepeerd in drie categorieën:\par
\bullet \b Kwaliteit:\b0 Defectpercentage per machine en Panel Test resultaten\par
\bullet \b Efficiëntie:\b0 Cyclustijd per machine en Benuttingsgraad per machine\par
\bullet \b Duurzaamheid:\b0 Energieverbruik per machine en Pareto-analyse van defecten\par

\b Doel:\b0 Dit dashboard biedt een snel overzicht van de belangrijkste prestatie-indicatoren van het productieproces, zodat managers in één oogopslag de status kunnen zien.\par

\b 2.2 Defect per Machine\b0\par
\b Visualisatie:\b0 Staafdiagram van het defectpercentage per verpakkingsmachine\par
\b Gegevens:\b0 Gemiddeld defectpercentage per machine, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft het hoogste defectpercentage (2,05%)\par
\bullet Packager 4 heeft het laagste defectpercentage (2,00%)\par
\bullet Het verschil tussen de beste en slechtste machine is relatief klein (0,05%)\par

\b Doel:\b0 Deze visualisatie helpt bij het identificeren van machines met de hoogste defectpercentages, zodat verbeterinspanningen gericht kunnen worden op de meest problematische machines.\par

\b 2.3 Defect per Dag\b0\par
\b Visualisatie:\b0 Lijngrafiek van het defectpercentage per dag\par
\b Gegevens:\b0 Gemiddeld defectpercentage per productiedag, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet Er zijn duidelijke pieken en dalen in het defectpercentage over tijd\par
\bullet Sommige dagen hebben defectpercentages die significant hoger zijn dan het gemiddelde\par
\bullet Er is geen duidelijke trend zichtbaar (stijgend of dalend) over de geanalyseerde periode\par

\b Doel:\b0 Deze visualisatie helpt bij het identificeren van dagen met abnormaal hoge defectpercentages, wat kan wijzen op specifieke problemen op die dagen (zoals materiaalissues, personeelswisselingen, of onderhoudsproblemen).\par

\b 2.4 Cyclustijd per Machine\b0\par
\b Visualisatie:\b0 Staafdiagram van de cyclustijd per verpakkingsmachine\par
\b Gegevens:\b0 Gemiddelde cyclustijd (in uren) per machine, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 4 heeft de langste cyclustijd (4,13 uur)\par
\bullet Packager 1 heeft ook een lange cyclustijd (4,13 uur)\par
\bullet Onbekende machines hebben de kortste cyclustijd (4,04 uur)\par

\b Doel:\b0 Deze visualisatie helpt bij het identificeren van machines met de langste cyclustijden, wat wijst op potentiële bottlenecks in het productieproces die geoptimaliseerd kunnen worden.\par

\b 2.5 Benutting per Machine\b0\par
\b Visualisatie:\b0 Staafdiagram van de benuttingsgraad per verpakkingsmachine\par
\b Gegevens:\b0 Gemiddelde benuttingsgraad (in procenten) per machine, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft de laagste benuttingsgraad (74,59%)\par
\bullet Packager 3 heeft de hoogste benuttingsgraad (75,33%)\par
\bullet Alle machines hebben een benuttingsgraad tussen 74,5% en 75,5%\par

\b Doel:\b0 Deze visualisatie helpt bij het identificeren van onderbenutte machines, wat wijst op inefficiënties in het productieproces die verbeterd kunnen worden.\par

\b 2.6 Energie per Machine\b0\par
\b Visualisatie:\b0 Staafdiagram van het energieverbruik per verpakkingsmachine\par
\b Gegevens:\b0 Gemiddeld energieverbruik (in kWh) per machine, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik (24.835,5 kWh)\par
\bullet Packager 4 heeft het laagste energieverbruik onder de bekende machines\par
\bullet Er zijn grote verschillen in energieverbruik tussen de machines\par

\b Doel:\b0 Deze visualisatie helpt bij het identificeren van machines met hoog energieverbruik, wat wijst op potentiële kostenbesparingen en duurzaamheidsverbeteringen.\par

\b 2.7 Panel Test\b0\par
\b Visualisatie:\b0 Taartdiagram van de Panel Test resultaten\par
\b Gegevens:\b0 Verdeling van de resultaten van de Panel Test, berekend uit de opgeschoonde dataset\par
\b Belangrijkste bevindingen:\b0\par
\bullet De meerderheid van de producten voldoet aan de kwaliteitseisen\par
\bullet Een significant deel voldoet gedeeltelijk of voldoet niet\par

\b Doel:\b0 Deze visualisatie geeft inzicht in de kwaliteitsbeoordeling van de producten, wat helpt bij het bepalen van de algehele productkwaliteit.\par

\b 2.8 Pareto Analyse\b0\par
\b Visualisatie:\b0 Pareto-diagram van defectpercentages per machine-koffieboon combinatie\par
\b Gegevens:\b0 Defectpercentages per machine-koffieboon combinatie, gesorteerd van hoog naar laag, met een cumulatieve lijn\par
\b Belangrijkste bevindingen:\b0\par
\bullet De top 3 combinaties zijn verantwoordelijk voor een significant deel van alle defecten\par
\bullet De cumulatieve lijn toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de top 5 combinaties\par

\b Doel:\b0 Deze visualisatie past het Pareto-principe (80/20-regel) toe om te laten zien welke combinaties van machines en koffiebonen de meeste defecten veroorzaken, zodat verbeterinspanningen gericht kunnen worden op de meest problematische combinaties.\par

\b 2.9 Machine Vergelijking\b0\par
\b Visualisatie:\b0 Meerdere staafdiagrammen die verschillende metrieken per machine vergelijken\par
\b Gegevens:\b0 Defectpercentage, cyclustijd, benuttingsgraad en energieverbruik per machine\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 combineert het hoogste defectpercentage met een lange cyclustijd en de laagste benuttingsgraad\par
\bullet Packager 4 heeft een lange cyclustijd maar het laagste defectpercentage\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik\par

\b Doel:\b0 Deze visualisatie biedt een geïntegreerd overzicht van de prestaties van elke machine op verschillende metrieken, wat helpt bij het identificeren van machines die op meerdere gebieden slecht presteren en dus prioriteit moeten krijgen bij verbeteringsinitiatieven.\par

\pard\sa200\sl276\slmult1\b\fs26 3. Ontwikkeling van de Visualisaties\b0\fs22\par
De visualisaties zijn ontwikkeld volgens de volgende stappen:\par

\bullet \b Gegevensvoorbereiding:\b0 De opgeschoonde dataset is geaggregeerd per machine, dag, of andere relevante dimensies om de benodigde gegevens voor de visualisaties te verkrijgen.\par

\bullet \b Visualisatiekeuze:\b0 Voor elke analyse is een passende visualisatie gekozen:\par
   - Staafdiagrammen voor vergelijkingen tussen machines\par
   - Lijngrafieken voor trends over tijd\par
   - Taartdiagrammen voor verdelingen\par
   - Pareto-diagrammen voor cumulatieve analyses\par

\bullet \b Opmaak en leesbaarheid:\b0 Alle visualisaties zijn voorzien van duidelijke titels, as-labels en legenda's, en waar relevant zijn datawaarden direct in de grafiek weergegeven.\par

\bullet \b Dashboard-integratie:\b0 De belangrijkste visualisaties zijn geïntegreerd in een dashboard voor een snel overzicht van de belangrijkste prestatie-indicatoren.\par

\pard\sa200\sl276\slmult1\b\fs26 4. Conclusies op basis van de Visualisaties\b0\fs22\par
Op basis van de visualisaties kunnen de volgende conclusies worden getrokken:\par

\bullet \b Kwaliteit:\b0 Packager 1 heeft het hoogste defectpercentage en zou de focus moeten zijn voor kwaliteitsverbetering volgens Six Sigma-methodologie.\par

\bullet \b Efficiëntie:\b0 Packager 4 en Packager 1 hebben de langste cyclustijden en zijn potentiële bottlenecks in het productieproces volgens Lean en TOC-methodologieën.\par

\bullet \b Benutting:\b0 Packager 1 heeft de laagste benuttingsgraad en verdient aandacht voor efficiëntieverbetering volgens Lean-methodologie.\par

\bullet \b Energieverbruik:\b0 De categorie 'Onbekend' heeft een extreem hoog energieverbruik dat nader onderzoek vereist volgens Kaizen-methodologie.\par

\bullet \b Pareto-analyse:\b0 Door te focussen op de top combinaties van machines en koffiebonen kan een significant deel van de defecten worden aangepakt volgens Six Sigma-methodologie.\par

\bullet \b Geïntegreerde aanpak:\b0 Packager 1 verdient de hoogste prioriteit voor verbeteringsinitiatieven, aangezien deze machine slecht presteert op meerdere metrieken (defectpercentage, cyclustijd, benuttingsgraad) volgens TQM-methodologie.\par

\pard\sa200\sl276\slmult1\i\fs20 Het Excel-bestand "Productieproces_Visualisaties.xlsx" bevat alle bovengenoemde visualisaties en biedt een uitgebreid inzicht in het productieproces op basis van de opgeschoonde Kikker dataset.\i0\fs22\par
}
