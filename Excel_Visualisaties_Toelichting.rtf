{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 Excel Visualisaties - Productieproces Analyse\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de visualisaties die zijn gemaakt in het Excel-bestand "Productieproces_Visualisaties.xlsx". Het bestand bevat verschillende werkbladen met grafieken die inzicht geven in het productieproces op basis van de Kikker dataset.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Dashboard\b0\fs22\par
Het Dashboard werkblad bevat een overzicht van de belangrijkste visualisaties, gegroepeerd in drie categorieën:\par
\bullet \b Kwaliteit:\b0 Defectpercentage per machine en Panel Test resultaten\par
\bullet \b Efficiëntie:\b0 Cyclustijd per machine en Benuttingsgraad per machine\par
\bullet \b Duurzaamheid:\b0 Energieverbruik per machine en Pareto-analyse van defecten\par
Dit dashboard biedt een snel overzicht van de belangrijkste prestatie-indicatoren van het productieproces.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Defect per Machine\b0\fs22\par
\b Visualisatie:\b0 Staafdiagram van het defectpercentage per verpakkingsmachine\par
\b Beschrijving:\b0 Deze grafiek toont het gemiddelde defectpercentage voor elke verpakkingsmachine, gesorteerd van hoogste naar laagste defectpercentage.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft het hoogste defectpercentage (2,05%)\par
\bullet Packager 4 heeft het laagste defectpercentage (2,00%)\par
\bullet Het verschil tussen de beste en slechtste machine is relatief klein (0,05%)\par
\b Implicaties:\b0 Focus verbeterinspanningen op Packager 1 en Packager 5 voor maximale impact op kwaliteit.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Defect per Dag\b0\fs22\par
\b Visualisatie:\b0 Lijngrafiek van het defectpercentage per dag\par
\b Beschrijving:\b0 Deze grafiek toont het gemiddelde defectpercentage voor elke productiedag, wat inzicht geeft in de consistentie van het productieproces over tijd.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Er zijn duidelijke pieken en dalen in het defectpercentage over tijd\par
\bullet Sommige dagen hebben defectpercentages die significant hoger zijn dan het gemiddelde\par
\b Implicaties:\b0 Onderzoek de oorzaken van de pieken in defectpercentage en implementeer maatregelen om de consistentie te verbeteren.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Cyclustijd per Machine\b0\fs22\par
\b Visualisatie:\b0 Staafdiagram van de cyclustijd per verpakkingsmachine\par
\b Beschrijving:\b0 Deze grafiek toont de gemiddelde cyclustijd (in uren) voor elke verpakkingsmachine, gesorteerd van langste naar kortste cyclustijd.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 4 heeft de langste cyclustijd (4,13 uur)\par
\bullet Packager 1 heeft ook een lange cyclustijd (4,13 uur)\par
\bullet Onbekende machines hebben de kortste cyclustijd (4,04 uur)\par
\b Implicaties:\b0 Packager 4 en Packager 1 zijn potentiële bottlenecks in het productieproces en verdienen aandacht voor procesoptimalisatie.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Benutting per Machine\b0\fs22\par
\b Visualisatie:\b0 Staafdiagram van de benuttingsgraad per verpakkingsmachine\par
\b Beschrijving:\b0 Deze grafiek toont de gemiddelde benuttingsgraad (in procenten) voor elke verpakkingsmachine, gesorteerd van laagste naar hoogste benuttingsgraad.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 heeft de laagste benuttingsgraad (74,59%)\par
\bullet Packager 3 heeft de hoogste benuttingsgraad (75,33%)\par
\bullet Alle machines hebben een benuttingsgraad tussen 74,5% en 75,5%\par
\b Implicaties:\b0 Er is ruimte voor verbetering in de benuttingsgraad van alle machines, met speciale aandacht voor Packager 1.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Energie per Machine\b0\fs22\par
\b Visualisatie:\b0 Staafdiagram van het energieverbruik per verpakkingsmachine\par
\b Beschrijving:\b0 Deze grafiek toont het gemiddelde energieverbruik (in kWh) voor elke verpakkingsmachine, gesorteerd van hoogste naar laagste energieverbruik.\par
\b Belangrijkste bevindingen:\b0\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik (24.835,5 kWh)\par
\bullet Packager 4 heeft het laagste energieverbruik onder de bekende machines\par
\bullet Er zijn grote verschillen in energieverbruik tussen de machines\par
\b Implicaties:\b0 Identificeer welke machines onder 'Onbekend' vallen en onderzoek hun hoge energieverbruik voor potentiële kostenbesparingen en duurzaamheidsverbeteringen.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Panel Test\b0\fs22\par
\b Visualisatie:\b0 Taartdiagram van de Panel Test resultaten\par
\b Beschrijving:\b0 Deze grafiek toont de verdeling van de resultaten van de Panel Test, wat inzicht geeft in de kwaliteitsbeoordeling van de producten.\par
\b Belangrijkste bevindingen:\b0\par
\bullet De meerderheid van de producten voldoet aan de kwaliteitseisen\par
\bullet Een significant deel voldoet gedeeltelijk of voldoet niet\par
\b Implicaties:\b0 Focus op het verbeteren van de producten die gedeeltelijk of niet voldoen aan de kwaliteitseisen.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Pareto Analyse\b0\fs22\par
\b Visualisatie:\b0 Pareto-diagram van defectpercentages per machine-koffieboon combinatie\par
\b Beschrijving:\b0 Deze grafiek toont een Pareto-analyse van de defectpercentages per machine-koffieboon combinatie, met een cumulatieve lijn die het percentage van het totaal aangeeft.\par
\b Belangrijkste bevindingen:\b0\par
\bullet De top 3 combinaties zijn verantwoordelijk voor een significant deel van alle defecten\par
\bullet De cumulatieve lijn toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de top 5 combinaties\par
\b Implicaties:\b0 Volgens het Pareto-principe (80/20-regel) zou het verbeteren van de top combinaties het grootste effect hebben op de totale kwaliteit.\par

\pard\sa200\sl276\slmult1\b\fs26 Werkblad: Machine Vergelijking\b0\fs22\par
\b Visualisatie:\b0 Meerdere staafdiagrammen die verschillende metrieken per machine vergelijken\par
\b Beschrijving:\b0 Dit werkblad bevat vier grafieken die de prestaties van elke machine vergelijken op het gebied van defectpercentage, cyclustijd, benuttingsgraad en energieverbruik.\par
\b Belangrijkste bevindingen:\b0\par
\bullet Packager 1 combineert het hoogste defectpercentage met een lange cyclustijd en de laagste benuttingsgraad\par
\bullet Packager 4 heeft een lange cyclustijd maar het laagste defectpercentage\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik\par
\b Implicaties:\b0 Deze vergelijking helpt bij het identificeren van machines die op meerdere gebieden slecht presteren en dus prioriteit moeten krijgen bij verbeteringsinitiatieven.\par

\pard\sa200\sl276\slmult1\b\fs26 Conclusies op basis van de Excel-visualisaties\b0\fs22\par
\bullet \b Kwaliteit:\b0 Packager 1 heeft het hoogste defectpercentage en zou de focus moeten zijn voor kwaliteitsverbetering.\par
\bullet \b Efficiëntie:\b0 Packager 4 en Packager 1 hebben de langste cyclustijden en zijn potentiële bottlenecks in het productieproces.\par
\bullet \b Benutting:\b0 Packager 1 heeft de laagste benuttingsgraad en verdient aandacht voor efficiëntieverbetering.\par
\bullet \b Energieverbruik:\b0 De categorie 'Onbekend' heeft een extreem hoog energieverbruik dat nader onderzoek vereist.\par
\bullet \b Pareto-analyse:\b0 Door te focussen op de top combinaties van machines en koffiebonen kan een significant deel van de defecten worden aangepakt.\par

\pard\sa200\sl276\slmult1\i\fs20 Het Excel-bestand "Productieproces_Visualisaties.xlsx" bevat alle bovengenoemde visualisaties en biedt een uitgebreid inzicht in het productieproces op basis van de Kikker dataset.\i0\fs22\par
}
