{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management - Login Screen", "AppName": "EuroCaps Login", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(0, 120, 212, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366}, "Screens": [{"Name": "LoginScreen", "Template": "BlankScreen", "Fill": "RGBA(0, 120, 212, 1)", "Controls": [{"Name": "LoginContainer", "ControlType": "Rectangle", "X": 483, "Y": 184, "Width": 400, "Height": 400, "Fill": "RGBA(255, 255, 255, 1)", "BorderThickness": 0, "RadiusTopLeft": 10, "RadiusTopRight": 10, "RadiusBottomLeft": 10, "RadiusBottomRight": 10}, {"Name": "LogoImage", "ControlType": "Image", "X": 583, "Y": 214, "Width": 200, "Height": 80, "Image": "EuroCapsLogo.png", "ImagePosition": "ImagePosition.Fit"}, {"Name": "AppTitleLabel", "ControlType": "Label", "Text": "Order Management System", "X": 503, "Y": 304, "Width": 360, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 16, "Color": "RGBA(68, 68, 68, 1)", "Align": "Align.Center"}, {"Name": "UsernameTextInput", "ControlType": "TextInput", "X": 523, "Y": 354, "Width": 320, "Height": 40, "HintText": "Username", "Font": "Font.'Segoe UI'", "Size": 14, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5}, {"Name": "PasswordTextInput", "ControlType": "TextInput", "X": 523, "Y": 404, "Width": 320, "Height": 40, "HintText": "Password", "Mode": "TextMode.Password", "Font": "Font.'Segoe UI'", "Size": 14, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5}, {"Name": "LoginButton", "ControlType": "<PERSON><PERSON>", "Text": "<PERSON><PERSON>", "X": 523, "Y": 464, "Width": 320, "Height": 50, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 16, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5, "OnSelect": "If(LookUp(Users, Username = UsernameTextInput.Text && Password = PasswordTextInput.Text), Navigate(DashboardScreen, ScreenTransition.Fade, {CurrentUser: LookUp(Users, Username = UsernameTextInput.Text)}), Notify(\"Invalid username or password\", NotificationType.Error))"}, {"Name": "ForgotPasswordLabel", "ControlType": "Label", "Text": "Forgot Password?", "X": 523, "Y": 524, "Width": 320, "Height": 30, "Font": "Font.'Segoe UI'", "Size": 12, "Color": "RGBA(0, 120, 212, 1)", "Align": "Align.Center", "OnSelect": "Navigate(ForgotPasswordScreen, ScreenTransition.Fade)"}]}], "DataSources": [{"Name": "Users", "Type": "Excel", "ConnectionString": "Users.xlsx", "Table": "Users"}], "Variables": [{"Name": "CurrentUser", "Type": "Record", "DefaultValue": "Blank()"}]}