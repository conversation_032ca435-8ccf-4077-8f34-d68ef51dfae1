{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Script voor Video-presentatie\par
Analyse Productieproces Americaps Koffiecapsules\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document bevat het script voor de 5-minuten video-presentatie over de analyse van het productieproces van Americaps koffiecapsules.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 Introductie (30 seconden)\b0\fs22\par
Hallo, in deze presentatie zal ik de analyse van het productieproces van Americaps koffiecapsules toelichten. Ik zal uitleggen hoe ik de Kikker dataset heb opgeschoond, welke analyses ik heb uitgevoerd, en welke verbeteringen ik aanbeveel op basis van verschillende kwaliteitsmanagementmethoden.\par

De analyse bestond uit drie hoofdfasen: data-opschoning, data-analyse, en het formuleren van aanbevelingen. Laten we beginnen met de data-opschoning.\par

\pard\sa200\sl276\slmult1\b\fs26 Data-opschoning (1 minuut 30 seconden)\b0\fs22\par
[Toon Python-script op scherm]\par

De originele Kikker dataset bevatte verschillende datakwaliteitsproblemen die ik systematisch heb aangepakt met een Python-script. Hier zie je het script dat ik heb ontwikkeld voor de data-opschoning.\par

Ten eerste heb ik ontbrekende waarden geïdentificeerd en behandeld. In totaal waren er 3.097 missende waarden in 10 verschillende kolommen. Voor numerieke kolommen zoals 'Leveranciersbeoordeling' heb ik de mediaan gebruikt in plaats van het gemiddelde, omdat de mediaan robuuster is tegen uitschieters.\par

[Toon code voor behandeling van ontbrekende waarden]\par

Ten tweede heb ik onrealistische waarden gecorrigeerd, zoals negatieve scores en onmogelijke datums. Bijvoorbeeld, ik vond 6 negatieve waarden in 'Leveranciersbeoordeling' en meer dan 4.000 onmogelijke datums zoals "31-02-2025 25:61:61" in verschillende datumkolommen.\par

[Toon code voor correctie van onrealistische waarden]\par

Ten derde heb ik inconsistenties gestandaardiseerd, zoals verschillende schrijfwijzen voor dezelfde machine ('packager 1' vs 'Packager 1') en verschillende notaties voor eenheden.\par

[Toon code voor standaardisatie van inconsistenties]\par

Ten slotte heb ik duplicaten behandeld. Er waren geen exacte duplicaten, maar wel 2.680 dubbele batchnummers. In plaats van deze te verwijderen, heb ik ze voorzien van unieke identifiers om geen waardevolle informatie te verliezen.\par

[Toon code voor behandeling van duplicaten]\par

Het resultaat is een opgeschoonde dataset zonder missende waarden, onrealistische gegevens of inconsistenties, klaar voor betrouwbare analyse.\par

\pard\sa200\sl276\slmult1\b\fs26 Data-analyse en Visualisaties (1 minuut 30 seconden)\b0\fs22\par
[Toon Excel-dashboard op scherm]\par

Na de opschoning heb ik de data geanalyseerd en gevisualiseerd in Excel. Hier zie je het dashboard met de belangrijkste visualisaties, gegroepeerd in drie categorieën: kwaliteit, efficiëntie en duurzaamheid.\par

[Toon grafiek van defectpercentage per machine]\par

De analyse van defectpercentages toont dat Packager 1 het hoogste defectpercentage heeft met 2,05%, gevolgd door Packager 5 met 2,04%. Hoewel de verschillen klein lijken, kan een verbetering van 0,05% bij grote productievolumes significant zijn.\par

[Toon grafiek van cyclustijd per machine]\par

De analyse van cyclustijden laat zien dat Packager 4 en Packager 1 de langste cyclustijden hebben met 4,13 uur. Deze machines zijn potentiële bottlenecks in het productieproces.\par

[Toon grafiek van benuttingsgraad per machine]\par

De benuttingsgraad van machines varieert tussen 74,59% voor Packager 1 en 75,33% voor Packager 3. Er is dus ruimte voor verbetering in de benutting van alle machines.\par

[Toon Pareto-diagram]\par

De Pareto-analyse toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de twee slechtst presterende machines. Volgens het Pareto-principe zou het verbeteren van deze machines het grootste effect hebben op de totale kwaliteit.\par

\pard\sa200\sl276\slmult1\b\fs26 Aanbevelingen volgens Kwaliteitsmanagementmethoden (1 minuut 30 seconden)\b0\fs22\par
Op basis van de analyse heb ik aanbevelingen geformuleerd volgens verschillende kwaliteitsmanagementmethoden:\par

[Toon slide met Six Sigma aanbevelingen]\par

\b Six Sigma:\b0 Voor het verminderen van defecten beveel ik aan om Statistical Process Control (SPC) te implementeren bij Packager 1 en Packager 5, en een DMAIC-project uit te voeren specifiek gericht op Packager 1. Dit kan het defectpercentage met 25% verlagen binnen 6 maanden.\par

[Toon slide met Lean aanbevelingen]\par

\b Lean:\b0 Voor het verkorten van doorlooptijden beveel ik aan om Value Stream Mapping uit te voeren voor Packager 4 en Packager 1, SMED te implementeren om omsteltijden te verkorten, en werkprocedures te standaardiseren. Dit kan de cyclustijd met 10% verlagen en omsteltijden met 30% reduceren.\par

[Toon slide met TOC aanbevelingen]\par

\b TOC (Theory of Constraints):\b0 Voor het aanpakken van bottlenecks beveel ik aan om Drum-Buffer-Rope planning te implementeren met Packager 4 als 'drum', de capaciteit van Packager 4 te verhogen, en taken te herverdelen tussen machines. Dit kan de doorstroming verhogen en de benuttingsgraad van Packager 1 verhogen tot boven 80%.\par

[Toon slide met Kaizen aanbevelingen]\par

\b Kaizen:\b0 Voor continue verbetering beveel ik aan om Kaizen-events te starten gericht op energieverbruik, een suggestiesysteem voor medewerkers te implementeren, en 5S in te voeren. Dit kan het energieverbruik met 20-30% verlagen en de duurzaamheidsscore verhogen.\par

[Toon slide met TQM aanbevelingen]\par

\b TQM (Total Quality Management):\b0 Voor een organisatiebrede aanpak beveel ik aan om een integraal kwaliteitsmanagementsysteem te implementeren, een kwaliteitsdashboard te ontwikkelen, en cross-functionele kwaliteitscirkels te organiseren. Dit kan leiden tot integrale kwaliteitsverbetering en 15% lagere kwaliteitskosten.\par

\pard\sa200\sl276\slmult1\b\fs26 Conclusie (30 seconden)\b0\fs22\par
[Toon slide met verwachte impact]\par

De implementatie van deze aanbevelingen zal naar verwachting leiden tot:\par
\bullet Een reductie van het defectpercentage met 25%\par
\bullet Een verhoging van de productiecapaciteit met 10%\par
\bullet Een energiebesparing van 20-30%\par
\bullet Een reductie van de totale kwaliteitskosten met 15%\par

Door systematisch de datakwaliteitsproblemen aan te pakken en de opgeschoonde data te analyseren, hebben we waardevolle inzichten verkregen in het productieproces van Americaps koffiecapsules. De aanbevelingen op basis van verschillende kwaliteitsmanagementmethoden bieden een geïntegreerde aanpak voor het verbeteren van kwaliteit, efficiëntie en duurzaamheid.\par

Bedankt voor uw aandacht. Zijn er nog vragen?\par

\pard\sa200\sl276\slmult1\i\fs20 Totale duur: 5 minuten\i0\fs22\par
}
