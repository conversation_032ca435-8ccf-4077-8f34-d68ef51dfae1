# Database to Screen Mapping - EuroCaps Order Management App

## 📊 Database Files Overview

### **Excel Database Files Created:**
1. **EuroCaps_Users.xlsx** - User accounts and authentication
2. **EuroCaps_Customers.xlsx** - Customer information and contacts
3. **EuroCaps_Products.xlsx** - Product catalog and inventory
4. **EuroCaps_Orders.xlsx** - Order headers and main information
5. **EuroCaps_OrderItems.xlsx** - Individual items within orders

---

## 🔗 Screen to Database Mapping

### **1. Dashboard Screen**
**Primary Data Sources:**
- `Orders.xlsx` - Order counts, status distribution, recent orders
- `Customers.xlsx` - Customer names for recent orders display
- `Users.xlsx` - Current user information for welcome message

**Key Queries:**
- Total orders this month: `COUNT(Orders WHERE OrderDate >= FirstDayOfMonth)`
- Active orders: `COUNT(Orders WHERE Status IN ('New', 'Processing'))`
- Recent orders: `SELECT TOP 5 FROM Orders ORDER BY OrderDate DESC`

**UI Elements:**
- Date filter dropdown: `[📅 Last 30 Days ▼]`
- Status filter dropdown: `[All Status ▼]`
- User profile dropdown: `[<PERSON> ▼]`

---

### **2. Customer List Screen**
**Primary Data Sources:**
- `Customers.xlsx` - All customer information

**Key Queries:**
- Customer list: `SELECT * FROM Customers ORDER BY CompanyName`
- Customer count by type: `COUNT(Customers) GROUP BY CustomerType`
- Active customers: `COUNT(Customers WHERE Status = 'Active')`

**UI Elements:**
- Customer type filter: `[Customer Type ▼]` (Premium, Standard, VIP)
- Status filter: `[Status ▼]` (Active, Inactive, Pending)
- Sort dropdown: `[Sort By ▼]` (Name, Date, Orders)
- Items per page: `[10 ▼]` (5, 10, 25, 50, 100)

---

### **3. Product Catalog Screen**
**Primary Data Sources:**
- `Products.xlsx` - Product information, pricing, inventory

**Key Queries:**
- Product list: `SELECT * FROM Products ORDER BY ProductName`
- Stock levels: `Products.Stock` column
- Product categories: `DISTINCT Products.Category`

**UI Elements:**
- Category filter: `[All Types ▼]` (Espresso, Lungo, Ristretto, Flavored, Decaf)
- Price range filter: `[Price ▼]` (All, €0-10, €10-15, €15+)
- Stock filter: `[Stock ▼]` (All, In Stock, Low Stock, Out of Stock)
- Sort dropdown: `[Sort By ▼]` (Name, Price, Stock, Category)

---

### **4. New Order Screen**
**Primary Data Sources:**
- `Customers.xlsx` - Customer selection dropdown
- `Products.xlsx` - Product selection and pricing
- `Orders.xlsx` - New order creation
- `OrderItems.xlsx` - Order line items

**Key Queries:**
- Customer dropdown: `SELECT CustomerID, CompanyName FROM Customers WHERE Status = 'Active'`
- Product selection: `SELECT * FROM Products WHERE Stock > 0`
- Order total calculation: `SUM(OrderItems.Total) + VAT`

**UI Elements:**
- Customer dropdown: `[🔍 Select Customer ▼]`
- Date picker: `[📅 16/01/2025]`
- Priority dropdown: `[Normal ▼]` (Low, Normal, High, Urgent)
- Product quantity: `[1 ▼]` (1, 2, 3, 5, 10, Custom)

---

### **5. Order Detail Screen**
**Primary Data Sources:**
- `Orders.xlsx` - Order header information
- `OrderItems.xlsx` - Order line items
- `Customers.xlsx` - Customer details
- `Products.xlsx` - Product information for items

**Key Queries:**
- Order details: `SELECT * FROM Orders WHERE OrderID = ?`
- Order items: `SELECT * FROM OrderItems JOIN Products ON ProductID WHERE OrderID = ?`
- Customer info: `SELECT * FROM Customers WHERE CustomerID = ?`

**UI Elements:**
- Status dropdown: `[Change Status ▼]` (New, Processing, Shipped, Delivered, Cancelled)
- Edit items button: `[✏️ Edit Items]`
- Action buttons: `[📝 EDIT]`, `[🖨️ PRINT]`, `[✅ CONFIRM]`, `[❌ CANCEL]`

---

### **6. Order Items Screen**
**Primary Data Sources:**
- `Products.xlsx` - Available products for selection
- `OrderItems.xlsx` - Current order items
- `Orders.xlsx` - Order information

**Key Queries:**
- Available products: `SELECT * FROM Products WHERE Stock > 0`
- Current items: `SELECT * FROM OrderItems WHERE OrderID = ?`
- Stock check: `Products.Stock >= RequestedQuantity`

**UI Elements:**
- Product type filter: `[All Types ▼]`
- Product size filter: `[All Sizes ▼]`
- Quantity selector: `[1▼]` (1-99, based on stock)
- Sort dropdown: `[Price ▼]` (Name, Price, Stock, Category)

---

### **7. Order Confirmation Screen**
**Primary Data Sources:**
- `Orders.xlsx` - Order to be confirmed
- `OrderItems.xlsx` - Items to confirm
- `Customers.xlsx` - Customer information
- `Products.xlsx` - Product details

**Key Queries:**
- Order summary: `SELECT * FROM Orders WHERE OrderID = ?`
- Confirmation options: Configuration settings
- Payment calculation: `Subtotal + VAT + Delivery`

**UI Elements:**
- Confirmation checkboxes: `☑️ Send confirmation email`
- Action buttons: `[⬅️ BACK]`, `[📝 EDIT]`, `[✅ CONFIRM & SEND]`

---

### **8. Order History Screen**
**Primary Data Sources:**
- `Orders.xlsx` - Historical orders
- `Customers.xlsx` - Customer names
- `OrderItems.xlsx` - Order totals

**Key Queries:**
- Order history: `SELECT * FROM Orders ORDER BY OrderDate DESC`
- Order statistics: `COUNT(Orders) GROUP BY Status`
- Customer orders: `SELECT * FROM Orders WHERE CustomerID = ?`

**UI Elements:**
- Date range filter: `[📅 Last 30 Days ▼]`
- Status filter: `[📊 All Status ▼]`
- Customer filter: `[👥 All ▼]`
- Export button: `[📤 EXPORT]`

---

### **9. Customer Detail Screen**
**Primary Data Sources:**
- `Customers.xlsx` - Customer information
- `Orders.xlsx` - Customer order history
- `OrderItems.xlsx` - Order details for statistics

**Key Queries:**
- Customer details: `SELECT * FROM Customers WHERE CustomerID = ?`
- Order history: `SELECT * FROM Orders WHERE CustomerID = ? ORDER BY OrderDate DESC`
- Customer statistics: `SUM(Orders.Total), COUNT(Orders), AVG(Orders.Total)`

**UI Elements:**
- Edit button: `[📝 EDIT]`
- New order button: `[🛒]`
- Quick actions: `[🛒 NEW ORDER]`, `[📧 SEND EMAIL]`, `[📞 CALL]`

---

### **10. Product Detail Screen**
**Primary Data Sources:**
- `Products.xlsx` - Product information
- `OrderItems.xlsx` - Sales statistics

**Key Queries:**
- Product details: `SELECT * FROM Products WHERE ProductID = ?`
- Related products: `SELECT * FROM Products WHERE Category = ? AND ProductID != ?`
- Sales statistics: `COUNT(OrderItems WHERE ProductID = ?)`

**UI Elements:**
- Quantity selector: `[1 ▼]` (1, 2, 3, 5, 10)
- Add to cart: `[🛒 ADD TO CART]`
- Wishlist: `[💝 ADD TO WISHLIST]`

---

### **11. Reports & Analytics Screen**
**Primary Data Sources:**
- `Orders.xlsx` - Sales data and trends
- `OrderItems.xlsx` - Product performance
- `Customers.xlsx` - Customer analytics
- `Products.xlsx` - Product statistics

**Key Queries:**
- Revenue trends: `SUM(Orders.Total) GROUP BY MONTH(OrderDate)`
- Top products: `SELECT ProductID, SUM(Quantity) FROM OrderItems GROUP BY ProductID`
- Top customers: `SELECT CustomerID, SUM(Total) FROM Orders GROUP BY CustomerID`

**UI Elements:**
- Period filter: `[Last 30 Days ▼]`
- Report type: `[Sales Report ▼]`
- Export options: `[📤 Export]`

---

### **12. User Management Screen**
**Primary Data Sources:**
- `Users.xlsx` - User accounts and roles

**Key Queries:**
- User list: `SELECT * FROM Users ORDER BY LastName, FirstName`
- Role distribution: `COUNT(Users) GROUP BY Role`
- Active users: `COUNT(Users WHERE Status = 'Active')`

**UI Elements:**
- Role filter: `[All Roles ▼]`
- Status filter: `[Active ▼]`
- Department filter: `[Department ▼]`

---

## 🔧 Implementation Notes

### **Database Relationships:**
- Orders.CustomerID → Customers.CustomerID
- OrderItems.OrderID → Orders.OrderID
- OrderItems.ProductID → Products.ProductID
- Orders.CreatedBy → Users.UserID

### **Data Validation Rules:**
- Stock levels must be checked before order creation
- Customer status must be 'Active' for new orders
- User permissions control screen access
- Order status transitions follow business rules

### **Performance Considerations:**
- Index on frequently queried columns (CustomerID, OrderID, ProductID)
- Cache frequently accessed data (product catalog, customer list)
- Implement pagination for large datasets
- Use database views for complex queries
