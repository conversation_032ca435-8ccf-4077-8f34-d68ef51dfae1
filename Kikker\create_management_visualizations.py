import pandas as pd
import matplotlib.pyplot as plt
import os
import re

# Functie om CSV-bestanden te lezen met tab-gesche<PERSON> waarden
def read_analysis_file(file_path):
    print(f"Bestand lezen: {file_path}")

    # Probeer eerst het bestand regel voor regel te lezen
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Controleer of het bestand al in het juiste formaat is (met tabs of komma's)
    if len(lines) > 0 and ('\t' in lines[0] or ',' in lines[0]):
        try:
            # <PERSON>beer eerst met tab als scheidingsteken
            if '\t' in lines[0]:
                df = pd.read_csv(file_path, sep='\t', encoding='utf-8')
                print(f"Bestand gelezen met tab-scheidingsteken. Kolommen: {df.columns.tolist()}")
            else:
                df = pd.read_csv(file_path, sep=',', encoding='utf-8')
                print(f"Bestand gelezen met komma-scheidingsteken. Kolommen: {df.columns.tolist()}")

            # Zorg ervoor dat de Waarde kolom numeriek is
            if 'Waarde' in df.columns:
                try:
                    # Verwijder eventuele niet-numerieke tekens (behalve decimale punt)
                    df['Waarde'] = df['Waarde'].astype(str).str.replace('[^\d.]', '', regex=True)
                    # Converteer naar float
                    df['Waarde'] = pd.to_numeric(df['Waarde'], errors='coerce')
                    # Verwijder eventuele NaN waarden
                    df = df.dropna(subset=['Waarde'])
                    print(f"Waarde kolom geconverteerd naar numeriek. Aantal rijen: {len(df)}")
                except Exception as e:
                    print(f"Fout bij converteren van Waarde kolom: {e}")

            return df
        except Exception as e:
            print(f"Fout bij lezen met pandas: {e}")

    # Als dat niet werkt, probeer het bestand regel voor regel te lezen
    print("Handmatig bestand parsen...")
    data = []

    for line in lines:
        line = line.strip()
        if not line:  # Sla lege regels over
            continue

        # Controleer of dit een sectie-header is
        if line.isupper() or (not ':' in line and not '-' in line and not line.startswith('#')):
            # Sla headers over
            continue

        # Zoek naar regels met waarden
        if ':' in line:
            parts = line.split(':', 1)
            if len(parts) == 2:
                category = parts[0].strip()
                value_str = parts[1].strip()

                # Extraheer numerieke waarde
                numeric_value = re.search(r'([\d.]+)\s*(%|uur|eenheden|kg|kWh)?', value_str)
                if numeric_value:
                    try:
                        value = float(numeric_value.group(1))
                        unit = numeric_value.group(2) if numeric_value.group(2) else ''
                        data.append([category, value, unit])
                    except ValueError:
                        print(f"Kon waarde niet converteren naar float: {numeric_value.group(1)}")
        elif '-' in line and not line.startswith('#'):
            parts = line.split('-', 1)
            if len(parts) == 2:
                category = parts[0].strip()
                value_str = parts[1].strip()

                # Extraheer numerieke waarde
                numeric_value = re.search(r'([\d.]+)\s*(%|uur|eenheden|kg|kWh)?', value_str)
                if numeric_value:
                    try:
                        value = float(numeric_value.group(1))
                        unit = numeric_value.group(2) if numeric_value.group(2) else ''
                        data.append([category, value, unit])
                    except ValueError:
                        print(f"Kon waarde niet converteren naar float: {numeric_value.group(1)}")

    if data:
        df = pd.DataFrame(data, columns=['Categorie', 'Waarde', 'Eenheid'])
        print(f"Handmatig geparsed. Aantal rijen: {len(df)}")
        return df
    else:
        print("Geen data gevonden in het bestand.")
        return pd.DataFrame(columns=['Categorie', 'Waarde', 'Eenheid'])

# Functie om percentages te converteren naar numerieke waarden
def convert_percentages(df):
    # Controleer of de kolom 'Eenheid' bestaat
    if 'Eenheid' not in df.columns:
        # Voeg een Eenheid kolom toe als deze niet bestaat
        df['Eenheid'] = ''
        # Probeer percentages te detecteren in de Categorie kolom
        percentage_mask = df['Categorie'].str.contains('%', na=False)
        df.loc[percentage_mask, 'Eenheid'] = '%'

    # Converteer percentages naar decimale waarden (0.0-1.0)
    percentage_mask = df['Eenheid'] == '%'
    if any(percentage_mask):
        df.loc[percentage_mask, 'Waarde'] = df.loc[percentage_mask, 'Waarde'] / 100

    return df

# Functie om staafdiagram te maken
def create_bar_chart(df, title, filename, y_label='Waarde', figsize=(10, 6)):
    # Zorg ervoor dat de Eenheid kolom bestaat
    if 'Eenheid' not in df.columns:
        df = convert_percentages(df)

    plt.figure(figsize=figsize)

    # Maak kortere labels voor de x-as
    categories = []
    for cat in df['Categorie']:
        # Verwijder woorden als 'Packager', 'Batch', etc. en behoud alleen het unieke deel
        if 'Packager' in cat:
            categories.append(cat.replace('Packager ', 'P'))
        elif 'Batch' in cat:
            categories.append(cat.replace('Batch ', 'B'))
        else:
            # Beperk de lengte tot 15 tekens
            categories.append(cat[:15] + '...' if len(cat) > 15 else cat)

    bars = plt.bar(categories, df['Waarde'])

    # Voeg waarden toe boven de staven
    for bar in bars:
        height = bar.get_height()
        # Controleer of het een percentage is
        is_percentage = False
        if 'Eenheid' in df.columns:
            is_percentage = any(df['Eenheid'] == '%')

        if is_percentage or max(df['Waarde']) <= 1.0:  # Als het een percentage is
            plt.text(bar.get_x() + bar.get_width()/2., height + height*0.05,
                    f'{height:.1%}', ha='center', va='bottom', rotation=0)
        else:
            plt.text(bar.get_x() + bar.get_width()/2., height + height*0.05,
                    f'{height:.2f}', ha='center', va='bottom', rotation=0)

    plt.title(title)
    plt.xlabel('Categorie')

    # Voeg eenheid toe aan y-label indien beschikbaar
    if 'Eenheid' in df.columns and df['Eenheid'].nunique() == 1 and df['Eenheid'].iloc[0]:
        y_label = f"{y_label} ({df['Eenheid'].iloc[0]})"

    plt.ylabel(y_label)
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()

# Functie om cirkeldiagram te maken
def create_pie_chart(df, title, filename, figsize=(8, 8)):
    # Zorg ervoor dat de Eenheid kolom bestaat
    if 'Eenheid' not in df.columns:
        df = convert_percentages(df)

    plt.figure(figsize=figsize)

    # Maak kortere labels voor de legenda
    categories = []
    for cat in df['Categorie']:
        # Beperk de lengte tot 20 tekens
        categories.append(cat[:20] + '...' if len(cat) > 20 else cat)

    # Controleer of het een percentage is
    is_percentage = False
    if 'Eenheid' in df.columns:
        is_percentage = any(df['Eenheid'] == '%')

    # Voeg percentages toe aan de labels als ze nog niet in de data zitten
    if not is_percentage and max(df['Waarde']) <= 1.0:
        # Converteer waarden naar percentages voor de weergave
        values = df['Waarde'] * 100
        autopct = '%1.1f%%'
    else:
        values = df['Waarde']
        autopct = lambda p: f'{p:.1f}%' if p > 5 else ''

    plt.pie(values, labels=categories, autopct=autopct, startangle=90)
    plt.axis('equal')  # Zorgt ervoor dat de cirkel rond is
    plt.title(title)
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()

# Maak een map voor de visualisaties als deze nog niet bestaat
if not os.path.exists('visualisaties'):
    os.makedirs('visualisaties')

# Kaizen Analyse Visualisaties
print("\nGenereren van Kaizen Analyse visualisaties...")
kaizen_df = read_analysis_file('Kaizen_Analyse_Clean.csv')

if not kaizen_df.empty:
    # Filter voor Panel Test resultaten
    panel_test_df = kaizen_df[kaizen_df['Categorie'].str.contains('Panel Test|Voldoet', na=False, case=False)]
    if not panel_test_df.empty and len(panel_test_df) >= 3:
        print(f"Panel Test data gevonden: {len(panel_test_df)} rijen")
        create_pie_chart(panel_test_df, 'Panel Test Resultaten', 'visualisaties/kaizen_panel_test_pie.png')
    else:
        print("Geen Panel Test data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Klanttevredenheid per Koffieboon Type
    klanttevredenheid_df = kaizen_df[
        kaizen_df['Categorie'].str.contains('Klanttevredenheid', na=False, case=False) &
        (kaizen_df['Categorie'].str.contains('Excelsa|Arabica|Robusta|Liberica', na=False, case=False))
    ]
    if not klanttevredenheid_df.empty and len(klanttevredenheid_df) >= 3:
        print(f"Klanttevredenheid data gevonden: {len(klanttevredenheid_df)} rijen")
        create_bar_chart(klanttevredenheid_df, 'Klanttevredenheid per Koffieboon Type',
                        'visualisaties/kaizen_klanttevredenheid_bar.png', 'Score')
    else:
        print("Geen Klanttevredenheid data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Klantretourpercentage per Koffieboon Type
    klantretour_df = kaizen_df[
        kaizen_df['Categorie'].str.contains('Klantretourpercentage', na=False, case=False) &
        (kaizen_df['Categorie'].str.contains('Excelsa|Arabica|Robusta|Liberica', na=False, case=False))
    ]
    if not klantretour_df.empty and len(klantretour_df) >= 3:
        print(f"Klantretourpercentage data gevonden: {len(klantretour_df)} rijen")
        create_bar_chart(klantretour_df, 'Klantretourpercentage per Koffieboon Type',
                        'visualisaties/kaizen_klantretour_bar.png', 'Percentage')
    else:
        print("Geen Klantretourpercentage data gevonden of onvoldoende data voor visualisatie")

# Lean Analyse Visualisaties
print("\nGenereren van Lean Analyse visualisaties...")
lean_df = read_analysis_file('Lean_Analyse_Clean.csv')

if not lean_df.empty:
    # Filter voor Benuttingsgraad per Verpakkingsmachine
    benutting_df = lean_df[lean_df['Categorie'].str.contains('Packager|Onbekend', na=False, case=False)]
    if not benutting_df.empty and len(benutting_df) >= 3:
        print(f"Benuttingsgraad data gevonden: {len(benutting_df)} rijen")
        create_bar_chart(benutting_df, 'Benuttingsgraad per Verpakkingsmachine',
                        'visualisaties/lean_benutting_bar.png', 'Percentage')
    else:
        print("Geen Benuttingsgraad data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Voorraadniveaus
    voorraad_df = lean_df[lean_df['Categorie'].str.contains('Voorraadniveaus|Gemiddelde|Mediaan', na=False, case=False)]
    if not voorraad_df.empty and len(voorraad_df) >= 2:
        print(f"Voorraadniveaus data gevonden: {len(voorraad_df)} rijen")
        create_bar_chart(voorraad_df, 'Voorraadniveaus Statistieken',
                        'visualisaties/lean_voorraad_bar.png', 'Eenheden')
    else:
        print("Geen Voorraadniveaus data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Cyclustijd
    cyclustijd_df = lean_df[lean_df['Categorie'].str.contains('Cyclustijd', na=False, case=False)]
    if not cyclustijd_df.empty and len(cyclustijd_df) >= 2:
        print(f"Cyclustijd data gevonden: {len(cyclustijd_df)} rijen")
        create_bar_chart(cyclustijd_df, 'Cyclustijd Statistieken',
                        'visualisaties/lean_cyclustijd_bar.png', 'Uren')
    else:
        print("Geen Cyclustijd data gevonden of onvoldoende data voor visualisatie")

# Six Sigma Analyse Visualisaties
print("\nGenereren van Six Sigma Analyse visualisaties...")
sixsigma_df = read_analysis_file('SixSigma_Analyse_Clean.csv')

if not sixsigma_df.empty:
    # Filter voor Top 10 batches met hoogste defectpercentage
    batch_df = sixsigma_df[sixsigma_df['Categorie'].str.contains('Batch', na=False, case=False)]
    if not batch_df.empty and len(batch_df) >= 3:
        print(f"Batch defect data gevonden: {len(batch_df)} rijen")
        create_bar_chart(batch_df, 'Top 10 Batches met Hoogste Defectpercentage',
                        'visualisaties/sixsigma_batch_defect_bar.png', 'Defectpercentage')
    else:
        print("Geen Batch defect data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Process Capability
    capability_df = sixsigma_df[sixsigma_df['Categorie'].str.contains('capability|Cp|Cpk', na=False, case=False)]
    if not capability_df.empty and len(capability_df) >= 2:
        print(f"Process Capability data gevonden: {len(capability_df)} rijen")
        create_bar_chart(capability_df, 'Process Capability Analyse',
                        'visualisaties/sixsigma_capability_bar.png', 'Waarde')
    else:
        print("Geen Process Capability data gevonden of onvoldoende data voor visualisatie")

# TOC Analyse Visualisaties
print("\nGenereren van TOC Analyse visualisaties...")
toc_df = read_analysis_file('TOC_Analyse_Clean.csv')

if not toc_df.empty:
    # Filter voor Procestijd Analyse
    procestijd_df = toc_df[toc_df['Categorie'].str.contains('Grinding|Filling|Packaging', na=False, case=False)]
    if not procestijd_df.empty and len(procestijd_df) >= 3:
        print(f"Procestijd data gevonden: {len(procestijd_df)} rijen")
        create_bar_chart(procestijd_df, 'Procestijd Analyse',
                        'visualisaties/toc_procestijd_bar.png', 'Uren')
    else:
        print("Geen Procestijd data gevonden of onvoldoende data voor visualisatie")

    # Filter voor Energieverbruik per Verpakkingsmachine
    energie_df = toc_df[toc_df['Categorie'].str.contains('Packager|Onbekend', na=False, case=False)]
    if not energie_df.empty and len(energie_df) >= 3:
        print(f"Energieverbruik data gevonden: {len(energie_df)} rijen")
        create_bar_chart(energie_df, 'Energieverbruik per Verpakkingsmachine',
                        'visualisaties/toc_energie_bar.png', 'kWh')
    else:
        print("Geen Energieverbruik data gevonden of onvoldoende data voor visualisatie")

print("\nAlle visualisaties zijn gegenereerd in de map 'visualisaties'.")
