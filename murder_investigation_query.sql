-- Murder Investigation Query
-- This query helps investigate the murder discovered by cleaner <PERSON><PERSON><PERSON> on April 24, 2024

-- 1. Find <PERSON><PERSON><PERSON>'s ID and confirm their details
SELECT schoonmaker_id, naam, leeftijd
FROM schoonmaker
WHERE naam LIKE '%Sarione%';

-- 2. Find which train(s) <PERSON><PERSON><PERSON> was assigned to clean on April 24, 2024
SELECT sp.schoonmaker_id, s.naam AS cleaner_name, sp.trein_id, sp.datum, sp.tijd_start, sp.tijd_einde
FROM schoonmaak_planning sp
JOIN schoonmaker s ON sp.schoonmaker_id = s.schoonmaker_id
WHERE s.naam LIKE '%Sarione%'
AND sp.datum = '2024-04-24';

-- 3. Get details about the train(s) <PERSON><PERSON><PERSON> was cleaning
SELECT d.trein_id, d.station_id_vertrek, d.station_id_aan<PERSON><PERSON>t, 
       d.tijd_vertrek, d.tijd_a<PERSON><PERSON><PERSON><PERSON>, d.datum
FROM dienstregeling d
<PERSON><PERSON><PERSON> schoonmaak_planning sp ON d.trein_id = sp.trein_id
JOIN schoonmaker s ON sp.schoonmaker_id = s.schoonmaker_id
WHERE s.naam LIKE '%Sarione%'
AND d.datum = '2024-04-24';

-- 4. Find all passengers who were on the train(s) Sarione was cleaning
-- This assumes ov_info contains check-in/check-out data that can be linked to specific trains
SELECT ov.ov_chipkaart_id, ov.datum, ov.jaar, ov.maand, ov.dag, ov.week, ov.datum_activiteit, ov.station_id
FROM ov_info ov
JOIN dienstregeling d ON (ov.station_id = d.station_id_vertrek OR ov.station_id = d.station_id_aankomst)
JOIN schoonmaak_planning sp ON d.trein_id = sp.trein_id
JOIN schoonmaker s ON sp.schoonmaker_id = s.schoonmaker_id
WHERE s.naam LIKE '%Sarione%'
AND ov.datum = '2024-04-24'
ORDER BY ov.ov_chipkaart_id, ov.datum_activiteit;

-- 5. Find passengers who checked in but didn't check out (potential suspects)
SELECT ov1.ov_chipkaart_id, ov1.datum, ov1.station_id AS check_in_station
FROM ov_info ov1
WHERE ov1.datum = '2024-04-24'
AND ov1.datum_activiteit = 'inchecken'
AND NOT EXISTS (
    SELECT 1 
    FROM ov_info ov2 
    WHERE ov2.ov_chipkaart_id = ov1.ov_chipkaart_id
    AND ov2.datum = '2024-04-24'
    AND ov2.datum_activiteit = 'uitchecken'
);

-- 6. Find all passenger movements on April 24, 2024 (for timeline analysis)
SELECT ov.ov_chipkaart_id, ov.datum, ov.datum_activiteit, ov.station_id,
       s.naam AS station_naam
FROM ov_info ov
LEFT JOIN station s ON ov.station_id = s.station_id
WHERE ov.datum = '2024-04-24'
ORDER BY ov.ov_chipkaart_id, ov.datum_activiteit;

-- 7. Comprehensive query combining train, cleaner, and passenger information
SELECT 
    sp.schoonmaker_id, 
    s.naam AS cleaner_name,
    sp.trein_id, 
    sp.datum AS cleaning_date,
    sp.tijd_start AS cleaning_start,
    sp.tijd_einde AS cleaning_end,
    d.station_id_vertrek, 
    sv.naam AS departure_station,
    d.station_id_aankomst, 
    sa.naam AS arrival_station,
    d.tijd_vertrek, 
    d.tijd_aankomst,
    ov.ov_chipkaart_id,
    ov.datum_activiteit,
    ov.station_id AS passenger_station,
    st.naam AS passenger_station_name
FROM schoonmaak_planning sp
JOIN schoonmaker s ON sp.schoonmaker_id = s.schoonmaker_id
JOIN dienstregeling d ON sp.trein_id = d.trein_id AND sp.datum = d.datum
LEFT JOIN station sv ON d.station_id_vertrek = sv.station_id
LEFT JOIN station sa ON d.station_id_aankomst = sa.station_id
LEFT JOIN ov_info ov ON d.datum = ov.datum 
    AND (ov.station_id = d.station_id_vertrek OR ov.station_id = d.station_id_aankomst)
LEFT JOIN station st ON ov.station_id = st.station_id
WHERE s.naam LIKE '%Sarione%'
AND sp.datum = '2024-04-24'
ORDER BY sp.tijd_start, ov.ov_chipkaart_id, ov.datum_activiteit;
