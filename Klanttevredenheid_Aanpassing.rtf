{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Klanttevredenheid\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt aan het opschoningsscript om de lege waarden (blanks) in de Klanttevredenheid-kolom te filteren en te vervangen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset waren er 140 lege waarden (NaN of blanks) in de Klanttevredenheid-kolom. Deze lege waarden kunnen analyses verstoren en leiden tot onvolledige resultaten. Het doel was om deze lege waarden te vervangen door een geschikte waarde, zodat de kolom volledig en bruikbaar is voor analyses.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
In de functie \i fix_unrealistic_values\i0 is extra code toegevoegd voor de Klanttevredenheid-kolom om lege waarden te identificeren en te vervangen:\par

\i # Also check for NaN values and replace them\par
nan_count = cleaned_df[col].isna().sum()\par
if nan_count > 0:\par
    # Replace NaN values with the median of non-NaN values\par
    median_value = cleaned_df[col].dropna().median()\par
    cleaned_df[col] = cleaned_df[col].fillna(median_value)\par
    print(f"Fixed \{nan_count\} blank values in '\{col\}' by replacing with median: \{median_value\}")\i0\par

Deze code doet het volgende:\par
\bullet Telt het aantal lege waarden (NaN) in de Klanttevredenheid-kolom\par
\bullet Berekent de mediaan van de niet-lege waarden\par
\bullet Vervangt de lege waarden door deze mediaan\par
\bullet Rapporteert het aantal vervangen waarden en de gebruikte mediaan\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Klanttevredenheid-waarden als volgt aangepast:\par

\bullet 140 lege waarden (blanks) in 'Klanttevredenheid' zijn vervangen door de mediaan: 5.0\par

\b Statistieken van de aangepaste Klanttevredenheid-kolom:\b0\par
\bullet Minimum: 1.0\par
\bullet Maximum: 10.0\par
\bullet Gemiddelde: 4.61\par
\bullet Mediaan: 5.0\par
\bullet 25e percentiel: 3.0\par
\bullet 75e percentiel: 6.0\par

\b Verdeling van Klanttevredenheid:\b0\par
\bullet 1.0: 474 waarden\par
\bullet 2.0: 749 waarden\par
\bullet 3.0: 1297 waarden\par
\bullet 4.0: 1400 waarden\par
\bullet 5.0: 1560 waarden (inclusief 140 vervangen waarden)\par
\bullet 6.0: 1169 waarden\par
\bullet 7.0: 678 waarden\par
\bullet 8.0: 359 waarden\par
\bullet 9.0: 129 waarden\par
\bullet 10.0: 185 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Klanttevredenheid-waarden zijn nu volledig en bruikbaar voor analyses:\par

\bullet \b Geen lege waarden meer:\b0 Er zijn geen lege waarden (blanks) meer in de kolom.\par
\bullet \b Consistente waarden:\b0 Alle waarden zijn nu tussen 1 en 10, wat een realistische schaal is voor klanttevredenheid.\par
\bullet \b Volledige dataset:\b0 De kolom bevat nu 8000 waarden (voorheen 7860), wat betekent dat alle rijen een geldige Klanttevredenheid-waarde hebben.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Klanttevredenheid-kolom heeft succesvol alle lege waarden vervangen door de mediaan van de niet-lege waarden. Dit maakt de dataset volledig en bruikbaar voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Klanttevredenheid-kolom betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de Klanttevredenheid-kolom in de dataset.\i0\fs22\par
}
