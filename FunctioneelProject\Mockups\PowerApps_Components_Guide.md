# PowerApps Components Guide - EuroCaps Order Management App

## 📱 PowerApps Control Types Used

### **1. Gallery Controls (voor lijsten en grids)**

#### **Vertical Gallery** - Voor lijsten
```powerapps
Gallery1 (Vertical Gallery)
- Items: Filter(DataSource, Condition)
- Template Height: 80-120
- Template bevat: Labels, Buttons, Icons
```
**Gebruikt voor:**
- Customer List (klanten lijst)
- Order History (order geschiedenis)
- Notifications (meldingen lijst)
- User Management (gebruikers lijst)

#### **Flexible Height Gallery** - Voor product grids
```powerapps
Gallery2 (Flexible Height Gallery)
- Items: Filter(Products, FilterCondition)
- Template Size: 200x250
- Wrap Count: 4 (voor grid layout)
- Template bevat: Image, Labels, Dropdown, Button
```
**Gebruikt voor:**
- Product Catalog (product grid)
- Related Products (gerelateerde producten)

---

### **2. Input Controls**

#### **Text Input**
```powerapps
TextInput1
- HintText: "Search customers..."
- OnChange: Update gallery filter
```

#### **Dropdown**
```powerapps
Dropdown1
- Items: ["Option 1", "Option 2", "Option 3"]
- Default: "Option 1"
- OnChange: Update filters
```

#### **Date Picker**
```powerapps
DatePicker1
- DefaultDate: Today()
- Format: DateTimeFormat.ShortDate
```

#### **Combo Box** (voor searchable dropdowns)
```powerapps
ComboBox1
- Items: Customers
- SearchFields: ["CompanyName", "ContactPerson"]
- DisplayFields: ["CompanyName"]
```

---

### **3. Display Controls**

#### **Label** (voor tekst weergave)
```powerapps
Label1
- Text: "Welcome " & User().FullName
- Font: Arial
- Size: 14
- Color: RGBA(0,0,0,1)
```

#### **HTML Text** (voor formatted tekst)
```powerapps
HtmlText1
- HtmlText: "<b>Total Orders:</b> " & CountRows(Orders)
```

#### **Image** (voor afbeeldingen)
```powerapps
Image1
- Image: ThisItem.ProductImage
- ImagePosition: ImagePosition.Fit
```

---

### **4. Button Controls**

#### **Button** (standaard knoppen)
```powerapps
Button1
- Text: "New Customer"
- OnSelect: Navigate(NewCustomerScreen)
- Fill: RGBA(0, 120, 212, 1)
```

#### **Icon** (voor icoon knoppen)
```powerapps
Icon1
- Icon: Icon.Add
- OnSelect: Navigate(NewOrderScreen)
- Color: RGBA(0, 120, 212, 1)
```

---

### **5. Container Controls**

#### **Rectangle** (voor visuele containers/cards)
```powerapps
Rectangle1
- Fill: RGBA(255, 255, 255, 1)
- BorderColor: RGBA(200, 200, 200, 1)
- BorderThickness: 1
- RadiusTopLeft: 5
```
**Gebruikt voor:**
- KPI "cards" (statistiek blokken)
- Form containers
- Section dividers

#### **Container** (voor groepering)
```powerapps
Container1
- LayoutDirection: LayoutDirection.Horizontal
- LayoutAlignItems: LayoutAlignItems.Center
```

---

### **6. Form Controls**

#### **Form** (voor data entry)
```powerapps
Form1
- DataSource: Customers
- Item: Gallery1.Selected
- DefaultMode: FormMode.New
```

#### **Data Card** (binnen forms)
```powerapps
DataCard1
- DataField: "CompanyName"
- Required: true
```

---

## 🔗 Data Connection Patterns

### **Excel File Connections**
```powerapps
// Data Source Setup
Customers = Excel file connection to Customers.xlsx
Orders = Excel file connection to Orders.xlsx
Products = Excel file connection to Products.xlsx
```

### **Common Formulas**

#### **Gallery Filtering**
```powerapps
Filter(Customers,
  (SearchBox.Text = "" || CompanyName in SearchBox.Text) &&
  (StatusDropdown.Selected.Value = "All" || Status = StatusDropdown.Selected.Value)
)
```

#### **Lookup Values**
```powerapps
LookUp(Customers, CustomerID = ThisItem.CustomerID, CompanyName)
```

#### **Count and Sum**
```powerapps
CountRows(Filter(Orders, Status = "New"))
Sum(OrderItems, Total)
```

#### **Navigation with Context**
```powerapps
Navigate(CustomerDetailScreen, ScreenTransition.Fade, {SelectedCustomer: ThisItem})
```

---

## 📊 Screen Layout Patterns

### **Dashboard Layout**
```
Screen
├── Rectangle (Header)
│   ├── Icon (Menu)
│   ├── Label (Title)
│   └── Icon (Profile)
├── Container (KPI Section)
│   ├── Rectangle (KPI Card 1)
│   ├── Rectangle (KPI Card 2)
│   └── Rectangle (KPI Card 3)
├── Gallery (Recent Items)
└── Container (Quick Actions)
    ├── Button (Action 1)
    ├── Button (Action 2)
    └── Button (Action 3)
```

### **List Screen Layout**
```
Screen
├── Rectangle (Header)
├── Container (Search & Filters)
│   ├── TextInput (Search)
│   ├── Dropdown (Filter 1)
│   └── Dropdown (Filter 2)
├── Container (Statistics)
│   ├── Rectangle (Stat 1)
│   ├── Rectangle (Stat 2)
│   └── Rectangle (Stat 3)
└── Gallery (Data List)
    └── Template
        ├── Label (Field 1)
        ├── Label (Field 2)
        └── Container (Actions)
            ├── Icon (View)
            ├── Icon (Edit)
            └── Icon (Delete)
```

### **Detail Screen Layout**
```
Screen
├── Rectangle (Header)
├── Form (Main Data)
│   ├── DataCard (Field 1)
│   ├── DataCard (Field 2)
│   └── DataCard (Field 3)
├── Gallery (Related Items)
└── Container (Actions)
    ├── Button (Save)
    ├── Button (Cancel)
    └── Button (Delete)
```

---

## 🎨 Styling Patterns

### **Color Scheme**
```powerapps
// Primary Colors
PrimaryBlue = RGBA(0, 120, 212, 1)
SecondaryGray = RGBA(96, 94, 92, 1)
BackgroundGray = RGBA(243, 242, 241, 1)
White = RGBA(255, 255, 255, 1)

// Status Colors
StatusNew = RGBA(255, 152, 0, 1)      // Orange
StatusActive = RGBA(16, 124, 16, 1)   // Green
StatusError = RGBA(164, 38, 44, 1)    // Red
```

### **Typography**
```powerapps
// Font Sizes
HeaderSize = 18
SubHeaderSize = 16
BodySize = 14
CaptionSize = 12

// Font Weights
Bold = FontWeight.Bold
Normal = FontWeight.Normal
```

---

## 🔄 Common Functions

### **Refresh Data**
```powerapps
Refresh(Customers); Refresh(Orders); Refresh(Products)
```

### **Filter Reset**
```powerapps
Reset(SearchBox); Reset(FilterDropdown1); Reset(FilterDropdown2)
```

### **Collection Management**
```powerapps
// Add to collection
Collect(CurrentOrder, {ProductID: 1, Quantity: 2})

// Update collection
UpdateIf(CurrentOrder, ProductID = 1, {Quantity: 3})

// Remove from collection
RemoveIf(CurrentOrder, ProductID = 1)
```

### **Validation**
```powerapps
If(IsBlank(TextInput1.Text), 
   Notify("Field is required", NotificationType.Error),
   // Continue with action
)
```

Dit geeft je een complete PowerApps-specifieke benadering voor alle screens!
