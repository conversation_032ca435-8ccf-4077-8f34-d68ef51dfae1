"""
Americaps Coffee Capsule Production Analysis

This script analyzes production and quality data from Americaps coffee capsule production.
It loads, cleans, and analyzes the data to identify improvement opportunities in the production process.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.float_format', '{:.2f}'.format)

def load_data(file_path):
    """
    Load the CSV file and return a pandas DataFrame
    """
    print(f"Loading data from {file_path}...")
    try:
        # Try to load the data with automatic encoding detection
        df = pd.read_csv(file_path)
        print(f"Successfully loaded data with {df.shape[0]} rows and {df.shape[1]} columns.")
        return df
    except UnicodeDecodeError:
        # If automatic encoding fails, try with different encodings
        for encoding in ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"Successfully loaded data with encoding {encoding}.")
                print(f"Data shape: {df.shape[0]} rows and {df.shape[1]} columns.")
                return df
            except UnicodeDecodeError:
                continue
        print("Failed to load the data with common encodings.")
        return None

def explore_data(df):
    """
    Explore the data and print basic information
    """
    print("\n=== DATA EXPLORATION ===")
    
    # Display basic information
    print("\nBasic Information:")
    print(df.info())
    
    # Display summary statistics
    print("\nSummary Statistics:")
    print(df.describe(include='all').T)
    
    # Check for missing values
    print("\nMissing Values:")
    missing_values = df.isnull().sum()
    missing_percent = (df.isnull().sum() / len(df)) * 100
    missing_data = pd.DataFrame({'Missing Values': missing_values, 
                                'Percentage': missing_percent})
    print(missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False))
    
    # Check for duplicate rows
    duplicates = df.duplicated().sum()
    print(f"\nNumber of duplicate rows: {duplicates}")
    
    # Display sample data
    print("\nSample Data (first 5 rows):")
    print(df.head())
    
    return missing_data[missing_data['Missing Values'] > 0]

def clean_data(df):
    """
    Clean the data by handling missing values, fixing data types, and removing outliers
    """
    print("\n=== DATA CLEANING ===")
    
    # Create a copy to avoid modifying the original dataframe
    cleaned_df = df.copy()
    
    # Fix column names (remove any spaces)
    cleaned_df.columns = cleaned_df.columns.str.strip()
    
    # Check for and fix date format issues
    date_columns = [col for col in cleaned_df.columns if 'Datum' in col or 'datum' in col or 'Date' in col or 'date' in col]
    
    for col in date_columns:
        if cleaned_df[col].dtype == 'object':
            print(f"Converting {col} to datetime...")
            # Handle the specific invalid date format "31-02-2025 25:61:61"
            cleaned_df[col] = cleaned_df[col].replace("31-02-2025 25:61:61", np.nan)
            
            # Try to convert to datetime with different formats
            try:
                cleaned_df[col] = pd.to_datetime(cleaned_df[col], errors='coerce')
            except:
                print(f"Could not convert {col} to datetime. Keeping as is.")
    
    # Handle missing values
    print("\nHandling missing values...")
    
    # For numeric columns, fill with median
    numeric_cols = cleaned_df.select_dtypes(include=['float64', 'int64']).columns
    for col in numeric_cols:
        if cleaned_df[col].isnull().sum() > 0:
            median_value = cleaned_df[col].median()
            cleaned_df[col] = cleaned_df[col].fillna(median_value)
            print(f"Filled {col} missing values with median: {median_value}")
    
    # For categorical columns, fill with mode
    categorical_cols = cleaned_df.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        if cleaned_df[col].isnull().sum() > 0:
            mode_value = cleaned_df[col].mode()[0]
            cleaned_df[col] = cleaned_df[col].fillna(mode_value)
            print(f"Filled {col} missing values with mode: {mode_value}")
    
    # Convert percentage strings to float values
    percentage_cols = [col for col in cleaned_df.columns if cleaned_df[col].dtype == 'object' and 
                      cleaned_df[col].str.contains('%', na=False).any()]
    
    for col in percentage_cols:
        print(f"Converting {col} from percentage string to float...")
        cleaned_df[col] = cleaned_df[col].str.replace('%', '').astype(float) / 100
    
    # Convert cost values to numeric
    if 'Cost' in cleaned_df.columns:
        print("Converting Cost column to numeric...")
        cleaned_df['Cost'] = cleaned_df['Cost'].str.replace('euros', '').str.strip().astype(float)
    
    # Handle outliers using IQR method for numeric columns
    print("\nHandling outliers...")
    for col in numeric_cols:
        Q1 = cleaned_df[col].quantile(0.25)
        Q3 = cleaned_df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        # Count outliers
        outliers = ((cleaned_df[col] < lower_bound) | (cleaned_df[col] > upper_bound)).sum()
        if outliers > 0:
            print(f"Found {outliers} outliers in {col}")
            
            # Cap outliers instead of removing them
            cleaned_df[col] = cleaned_df[col].clip(lower=lower_bound, upper=upper_bound)
            print(f"Capped outliers in {col} to range [{lower_bound:.2f}, {upper_bound:.2f}]")
    
    # Check if cleaning was successful
    missing_after = cleaned_df.isnull().sum().sum()
    print(f"\nRemaining missing values after cleaning: {missing_after}")
    
    return cleaned_df

def analyze_data(df):
    """
    Analyze the data to identify improvement opportunities
    """
    print("\n=== DATA ANALYSIS ===")
    
    # 1. Defect Analysis
    print("\n1. Defect Analysis:")
    if 'Defectpercentage' in df.columns:
        print(f"Average defect percentage: {df['Defectpercentage'].mean():.2%}")
        print(f"Minimum defect percentage: {df['Defectpercentage'].min():.2%}")
        print(f"Maximum defect percentage: {df['Defectpercentage'].max():.2%}")
        
        # Group by machine to see which has highest defect rate
        if 'PackagingApparaat' in df.columns:
            defects_by_machine = df.groupby('PackagingApparaat')['Defectpercentage'].mean().sort_values(ascending=False)
            print("\nAverage defect percentage by packaging machine:")
            print(defects_by_machine)
    
    # 2. Process Time Analysis
    print("\n2. Process Time Analysis:")
    if 'ProcessTime' in df.columns:
        # Convert to numeric if it's not already
        if df['ProcessTime'].dtype == 'object':
            df['ProcessTime'] = df['ProcessTime'].str.replace('hours', '').str.replace('uur', '').str.strip().astype(float)
        
        print(f"Average process time: {df['ProcessTime'].mean():.2f} hours")
        print(f"Minimum process time: {df['ProcessTime'].min():.2f} hours")
        print(f"Maximum process time: {df['ProcessTime'].max():.2f} hours")
        
        # Group by machine to see process time differences
        if 'PackagingApparaat' in df.columns:
            time_by_machine = df.groupby('PackagingApparaat')['ProcessTime'].mean().sort_values()
            print("\nAverage process time by packaging machine:")
            print(time_by_machine)
    
    # 3. Quality Analysis
    print("\n3. Quality Analysis:")
    if 'Klanttevredenheid' in df.columns:
        # Convert to numeric if it contains numeric values
        if df['Klanttevredenheid'].dtype == 'object':
            df['Klanttevredenheid'] = pd.to_numeric(df['Klanttevredenheid'], errors='coerce')
        
        if pd.api.types.is_numeric_dtype(df['Klanttevredenheid']):
            print(f"Average customer satisfaction: {df['Klanttevredenheid'].mean():.2f}")
            print(f"Minimum customer satisfaction: {df['Klanttevredenheid'].min():.2f}")
            print(f"Maximum customer satisfaction: {df['Klanttevredenheid'].max():.2f}")
    
    # 4. Efficiency Analysis
    print("\n4. Efficiency Analysis:")
    if 'Benuttingsgraad' in df.columns:
        print(f"Average utilization rate: {df['Benuttingsgraad'].mean():.2%}")
        print(f"Minimum utilization rate: {df['Benuttingsgraad'].min():.2%}")
        print(f"Maximum utilization rate: {df['Benuttingsgraad'].max():.2%}")
        
        # Group by machine to see utilization differences
        if 'PackagingApparaat' in df.columns:
            util_by_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean().sort_values()
            print("\nAverage utilization rate by packaging machine:")
            print(util_by_machine)
    
    # 5. Sustainability Analysis
    print("\n5. Sustainability Analysis:")
    if 'Duurzaamheid Score' in df.columns:
        print(f"Average sustainability score: {df['Duurzaamheid Score'].mean():.2f}")
        
    if 'CO2-Footprint' in df.columns:
        # Convert to numeric if it's not already
        if df['CO2-Footprint'].dtype == 'object':
            df['CO2-Footprint'] = df['CO2-Footprint'].str.replace('kg CO2/kg', '').str.strip().astype(float)
        
        print(f"Average CO2 footprint: {df['CO2-Footprint'].mean():.2f} kg CO2/kg")
        
    # 6. Cost Analysis
    print("\n6. Cost Analysis:")
    if 'Cost' in df.columns:
        print(f"Average cost: {df['Cost'].mean():.2f} euros")
        print(f"Minimum cost: {df['Cost'].min():.2f} euros")
        print(f"Maximum cost: {df['Cost'].max():.2f} euros")
        
        # Group by coffee bean type to see cost differences
        if 'Koffieboon' in df.columns:
            cost_by_bean = df.groupby('Koffieboon')['Cost'].mean().sort_values()
            print("\nAverage cost by coffee bean type:")
            print(cost_by_bean)
    
    # 7. Correlation Analysis
    print("\n7. Correlation Analysis:")
    # Select only numeric columns for correlation
    numeric_df = df.select_dtypes(include=['float64', 'int64'])
    
    # Calculate correlation matrix
    correlation_matrix = numeric_df.corr()
    
    # Find strong correlations (positive or negative)
    strong_correlations = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i):
            if abs(correlation_matrix.iloc[i, j]) > 0.5:  # Threshold for strong correlation
                strong_correlations.append((correlation_matrix.columns[i], 
                                           correlation_matrix.columns[j], 
                                           correlation_matrix.iloc[i, j]))
    
    # Sort by absolute correlation value
    strong_correlations.sort(key=lambda x: abs(x[2]), reverse=True)
    
    print("\nStrong correlations between variables:")
    for var1, var2, corr in strong_correlations:
        print(f"{var1} and {var2}: {corr:.2f}")
    
    return {
        'defects_by_machine': df.groupby('PackagingApparaat')['Defectpercentage'].mean() if 'Defectpercentage' in df.columns and 'PackagingApparaat' in df.columns else None,
        'time_by_machine': df.groupby('PackagingApparaat')['ProcessTime'].mean() if 'ProcessTime' in df.columns and 'PackagingApparaat' in df.columns else None,
        'util_by_machine': df.groupby('PackagingApparaat')['Benuttingsgraad'].mean() if 'Benuttingsgraad' in df.columns and 'PackagingApparaat' in df.columns else None,
        'correlation_matrix': correlation_matrix
    }

def visualize_data(df, analysis_results):
    """
    Create visualizations to better understand the data
    """
    print("\n=== DATA VISUALIZATION ===")
    print("Creating visualizations... (figures will be saved to disk)")
    
    # Set up the plotting style
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # 1. Defect percentage by machine
    if analysis_results['defects_by_machine'] is not None:
        plt.figure(figsize=(10, 6))
        analysis_results['defects_by_machine'].sort_values().plot(kind='bar', color='salmon')
        plt.title('Average Defect Percentage by Packaging Machine')
        plt.xlabel('Packaging Machine')
        plt.ylabel('Defect Percentage')
        plt.tight_layout()
        plt.savefig('defects_by_machine.png')
        print("Saved: defects_by_machine.png")
    
    # 2. Process time by machine
    if analysis_results['time_by_machine'] is not None:
        plt.figure(figsize=(10, 6))
        analysis_results['time_by_machine'].sort_values().plot(kind='bar', color='skyblue')
        plt.title('Average Process Time by Packaging Machine')
        plt.xlabel('Packaging Machine')
        plt.ylabel('Process Time (hours)')
        plt.tight_layout()
        plt.savefig('process_time_by_machine.png')
        print("Saved: process_time_by_machine.png")
    
    # 3. Utilization rate by machine
    if analysis_results['util_by_machine'] is not None:
        plt.figure(figsize=(10, 6))
        analysis_results['util_by_machine'].sort_values().plot(kind='bar', color='lightgreen')
        plt.title('Average Utilization Rate by Packaging Machine')
        plt.xlabel('Packaging Machine')
        plt.ylabel('Utilization Rate')
        plt.tight_layout()
        plt.savefig('utilization_by_machine.png')
        print("Saved: utilization_by_machine.png")
    
    # 4. Correlation heatmap
    if 'correlation_matrix' in analysis_results:
        plt.figure(figsize=(12, 10))
        sns.heatmap(analysis_results['correlation_matrix'], annot=False, cmap='coolwarm', center=0)
        plt.title('Correlation Matrix of Numeric Variables')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png')
        print("Saved: correlation_heatmap.png")
    
    # 5. Distribution of defect percentage
    if 'Defectpercentage' in df.columns:
        plt.figure(figsize=(10, 6))
        sns.histplot(df['Defectpercentage'], kde=True, color='salmon')
        plt.title('Distribution of Defect Percentage')
        plt.xlabel('Defect Percentage')
        plt.ylabel('Frequency')
        plt.tight_layout()
        plt.savefig('defect_distribution.png')
        print("Saved: defect_distribution.png")
    
    # 6. Scatter plot of process time vs defect percentage
    if 'ProcessTime' in df.columns and 'Defectpercentage' in df.columns:
        plt.figure(figsize=(10, 6))
        sns.scatterplot(x='ProcessTime', y='Defectpercentage', hue='PackagingApparaat', data=df)
        plt.title('Process Time vs Defect Percentage')
        plt.xlabel('Process Time (hours)')
        plt.ylabel('Defect Percentage')
        plt.tight_layout()
        plt.savefig('process_time_vs_defects.png')
        print("Saved: process_time_vs_defects.png")
    
    # 7. Box plot of defect percentage by coffee bean type
    if 'Defectpercentage' in df.columns and 'Koffieboon' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x='Koffieboon', y='Defectpercentage', data=df)
        plt.title('Defect Percentage by Coffee Bean Type')
        plt.xlabel('Coffee Bean Type')
        plt.ylabel('Defect Percentage')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('defects_by_bean_type.png')
        print("Saved: defects_by_bean_type.png")
    
    print("\nAll visualizations have been saved.")

def generate_recommendations(df, analysis_results):
    """
    Generate recommendations based on the analysis
    """
    print("\n=== RECOMMENDATIONS ===")
    
    recommendations = []
    
    # 1. Recommendations based on defect analysis
    if 'defects_by_machine' in analysis_results and analysis_results['defects_by_machine'] is not None:
        worst_machine = analysis_results['defects_by_machine'].idxmax()
        worst_defect_rate = analysis_results['defects_by_machine'].max()
        best_machine = analysis_results['defects_by_machine'].idxmin()
        best_defect_rate = analysis_results['defects_by_machine'].min()
        
        if worst_defect_rate > 0.02:  # If worst machine has > 2% defect rate
            recommendations.append(f"Investigate and improve {worst_machine} which has the highest defect rate ({worst_defect_rate:.2%}).")
        
        if (worst_defect_rate - best_defect_rate) > 0.01:  # If difference is > 1%
            recommendations.append(f"Study best practices from {best_machine} (defect rate: {best_defect_rate:.2%}) and apply them to {worst_machine}.")
    
    # 2. Recommendations based on process time
    if 'time_by_machine' in analysis_results and analysis_results['time_by_machine'] is not None:
        slowest_machine = analysis_results['time_by_machine'].idxmax()
        slowest_time = analysis_results['time_by_machine'].max()
        fastest_machine = analysis_results['time_by_machine'].idxmin()
        fastest_time = analysis_results['time_by_machine'].min()
        
        if (slowest_time - fastest_time) > 1:  # If difference is > 1 hour
            recommendations.append(f"Optimize process time for {slowest_machine} ({slowest_time:.2f} hours) by studying {fastest_machine} ({fastest_time:.2f} hours).")
    
    # 3. Recommendations based on utilization rate
    if 'util_by_machine' in analysis_results and analysis_results['util_by_machine'] is not None:
        lowest_util_machine = analysis_results['util_by_machine'].idxmin()
        lowest_util = analysis_results['util_by_machine'].min()
        
        if lowest_util < 0.7:  # If utilization is below 70%
            recommendations.append(f"Improve utilization rate of {lowest_util_machine} (currently {lowest_util:.2%}) through better scheduling or maintenance.")
    
    # 4. Recommendations based on correlations
    if 'correlation_matrix' in analysis_results:
        # Check if there's a correlation between process time and defect percentage
        if 'ProcessTime' in analysis_results['correlation_matrix'].columns and 'Defectpercentage' in analysis_results['correlation_matrix'].columns:
            corr = analysis_results['correlation_matrix'].loc['ProcessTime', 'Defectpercentage']
            if corr > 0.3:
                recommendations.append(f"Longer process times correlate with higher defect rates (correlation: {corr:.2f}). Consider optimizing process speed without sacrificing quality.")
            elif corr < -0.3:
                recommendations.append(f"Faster process times correlate with higher defect rates (correlation: {corr:.2f}). Consider slowing down critical processes to improve quality.")
    
    # 5. Sustainability recommendations
    if 'CO2-Footprint' in df.columns:
        high_co2_beans = df.groupby('Koffieboon')['CO2-Footprint'].mean().sort_values(ascending=False).head(1)
        if not high_co2_beans.empty:
            bean_type = high_co2_beans.index[0]
            co2_value = high_co2_beans.values[0]
            recommendations.append(f"Consider alternatives to {bean_type} coffee beans which have the highest CO2 footprint ({co2_value:.2f} kg CO2/kg).")
    
    # 6. Cost optimization recommendations
    if 'Cost' in df.columns and 'Defectpercentage' in df.columns:
        # Calculate cost per good unit
        df['Cost_per_good_unit'] = df['Cost'] / (1 - df['Defectpercentage'])
        expensive_machines = df.groupby('PackagingApparaat')['Cost_per_good_unit'].mean().sort_values(ascending=False).head(1)
        
        if not expensive_machines.empty:
            machine = expensive_machines.index[0]
            cost = expensive_machines.values[0]
            recommendations.append(f"Focus cost reduction efforts on {machine} which has the highest cost per good unit ({cost:.2f} euros).")
    
    # Print recommendations
    print("\nBased on the analysis, here are the key recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    return recommendations

def main():
    """
    Main function to execute the analysis
    """
    # Load the data
    file_path = "Kikker.csv"
    df = load_data(file_path)
    
    if df is None:
        print("Failed to load the data. Please check the file path and format.")
        return
    
    # Explore the data
    missing_data = explore_data(df)
    
    # Clean the data
    cleaned_df = clean_data(df)
    
    # Analyze the data
    analysis_results = analyze_data(cleaned_df)
    
    # Visualize the data
    visualize_data(cleaned_df, analysis_results)
    
    # Generate recommendations
    recommendations = generate_recommendations(cleaned_df, analysis_results)
    
    # Save cleaned data to CSV
    cleaned_df.to_csv("Kikker_cleaned.csv", index=False)
    print("\nCleaned data saved to 'Kikker_cleaned.csv'")
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("The analysis has identified several improvement opportunities in the Americaps production process.")
    print("Please refer to the saved visualizations and recommendations above for detailed insights.")

if __name__ == "__main__":
    main()
