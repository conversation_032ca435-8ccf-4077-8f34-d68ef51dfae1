Option Explicit

' Script om alle data en analyses te combineren in één Excel-bestand met visualisaties
' Dit script combineert Kikker_cleaned.csv en alle analyse-bestanden in één Excel-bestand

' Constanten
Const xlDelimited = 1
Const xlTextQualifierDoubleQuote = 1
Const xlWindows = 2
Const xlNormal = -4143
Const xlCenter = -4108
Const xlBottom = -4107

' Grafiektypen
Const xlColumnClustered = 51 ' Gegroepeerde kolomgrafiek
Const xlPie = 5 ' Cirkeldiagram
Const xlLine = 4 ' Lijngrafiek
Const xlBarClustered = 57 ' Gegroepeerde staafgrafiek

' Hoofdfunctie
Sub Main()
    Dim fso, excel, mainDataFile, analyseFiles, file, fileName, analysisType
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of het hoofdbestand bestaat
    mainDataFile = "Kikker_cleaned.csv"
    If Not fso.FileExists(mainDataFile) Then
        WScript.Echo "Het bestand " & mainDataFile & " bestaat niet. Controleer of het bestand aanwezig is."
        WScript.Quit
    End If
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Dim wb, mainSheet
    Set wb = excel.Workbooks.Add
    Set mainSheet = wb.Sheets(1)
    mainSheet.Name = "Kikker_cleaned"
    
    ' Importeer de hoofddata
    WScript.Echo "Importeren van " & mainDataFile & "..."
    ImportCSVToSheet excel, mainDataFile, mainSheet
    
    ' Zoek alle analyse CSV-bestanden
    Dim analyseTypes, i
    analyseTypes = Array("Lean_Analyse_Clean.csv", "Kaizen_Analyse_Clean.csv", "TOC_Analyse_Clean.csv", "SixSigma_Analyse_Clean.csv")
    
    ' Verwerk elk analyse-bestand
    For i = 0 To UBound(analyseTypes)
        fileName = analyseTypes(i)
        
        ' Controleer of het bestand bestaat
        If fso.FileExists(fileName) Then
            ' Bepaal het type analyse
            If InStr(fileName, "Kaizen") > 0 Then
                analysisType = "kaizen"
            ElseIf InStr(fileName, "Lean") > 0 Then
                analysisType = "lean"
            ElseIf InStr(fileName, "SixSigma") > 0 Then
                analysisType = "sixsigma"
            ElseIf InStr(fileName, "TOC") > 0 Then
                analysisType = "toc"
            Else
                analysisType = ""
            End If
            
            ' Als het een bekend analysetype is, verwerk het bestand
            If analysisType <> "" Then
                WScript.Echo "Verwerken van " & fileName & "..."
                
                ' Voeg een nieuw werkblad toe voor deze analyse
                Dim analyseSheet
                wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
                Set analyseSheet = wb.Sheets(wb.Sheets.Count)
                analyseSheet.Name = Replace(fileName, ".csv", "")
                
                ' Importeer de analyse-data
                ImportCSVToSheet excel, fileName, analyseSheet
                
                ' Maak visualisaties voor deze analyse
                CreateVisualizationsForAnalysis analyseSheet, analysisType
            End If
        Else
            WScript.Echo "Het bestand " & fileName & " bestaat niet en wordt overgeslagen."
        End If
    Next
    
    ' Sla het Excel-bestand op
    Dim excelFileName
    excelFileName = "Kikker_Analyses_Met_Visualisaties.xlsx"
    wb.SaveAs excelFileName, 51 ' 51 = xlsx-formaat
    
    WScript.Echo "Bestand opgeslagen als " & excelFileName
    WScript.Echo "Alle analyses zijn gecombineerd in één Excel-bestand met visualisaties."
End Sub

' Functie om CSV-data te importeren in een werkblad
Sub ImportCSVToSheet(excel, csvFilePath, sheet)
    Dim fso, file, line, row, col, values, separator
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Bepaal het scheidingsteken (tab of komma)
    separator = DetermineDelimiter(csvFilePath)
    
    ' Open het CSV-bestand
    Set file = fso.OpenTextFile(csvFilePath, 1) ' 1 = ForReading
    
    row = 1
    
    ' Lees elke regel van het CSV-bestand
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        ' Sla lege regels over
        If Trim(line) <> "" Then
            ' Split de regel op het scheidingsteken
            values = Split(line, separator)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                sheet.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
    
    ' Pas de kolombreedtes aan
    sheet.Columns.AutoFit
End Sub

' Functie om het scheidingsteken van een CSV-bestand te bepalen
Function DetermineDelimiter(filePath)
    Dim fso, file, line, delimiter
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    
    ' Lees de eerste regel
    If Not file.AtEndOfStream Then
        line = file.ReadLine
        
        ' Controleer welk scheidingsteken wordt gebruikt
        If InStr(line, vbTab) > 0 Then
            delimiter = vbTab
        ElseIf InStr(line, ",") > 0 Then
            delimiter = ","
        Else
            delimiter = "," ' Standaard
        End If
    Else
        delimiter = "," ' Standaard
    End If
    
    file.Close
    
    DetermineDelimiter = delimiter
End Function

' Functie om visualisaties te maken voor een analyse
Sub CreateVisualizationsForAnalysis(ws, analysisType)
    ' Maak visualisaties op basis van het analysetype
    Select Case analysisType
        Case "kaizen"
            ' Maak een cirkeldiagram voor Panel Test Resultaten
            Dim panelTestRange
            panelTestRange = FindDataRange(ws, "Panel Test", "Voldoet")
            
            If Not panelTestRange Is Nothing Then
                CreatePieChart ws, panelTestRange, "Panel Test Resultaten", 10, ws.UsedRange.Rows.Count * 15 + 10
            End If
            
            ' Maak een staafdiagram voor Klanttevredenheid per Koffieboon Type
            Dim klanttevredenheidRange
            klanttevredenheidRange = FindDataRange(ws, "Klanttevredenheid", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klanttevredenheidRange Is Nothing Then
                CreateBarChart ws, klanttevredenheidRange, "Klanttevredenheid per Koffieboon Type", 10, ws.UsedRange.Rows.Count * 15 + 320
            End If
            
            ' Maak een staafdiagram voor Klantretourpercentage per Koffieboon Type
            Dim klantretourRange
            klantretourRange = FindDataRange(ws, "Klantretourpercentage", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klantretourRange Is Nothing Then
                CreateBarChart ws, klantretourRange, "Klantretourpercentage per Koffieboon Type", 10, ws.UsedRange.Rows.Count * 15 + 630
            End If
            
        Case "lean"
            ' Maak een staafdiagram voor Benuttingsgraad per Verpakkingsmachine
            Dim benuttingRange
            benuttingRange = FindDataRange(ws, "Benuttingsgraad", "Packager|Onbekend")
            
            If Not benuttingRange Is Nothing Then
                CreateBarChart ws, benuttingRange, "Benuttingsgraad per Verpakkingsmachine", 10, ws.UsedRange.Rows.Count * 15 + 10
            End If
            
            ' Maak een staafdiagram voor Voorraadniveaus
            Dim voorraadRange
            voorraadRange = FindDataRange(ws, "Voorraadniveaus", "Gemiddelde|Mediaan")
            
            If Not voorraadRange Is Nothing Then
                CreateBarChart ws, voorraadRange, "Voorraadniveaus Statistieken", 10, ws.UsedRange.Rows.Count * 15 + 320
            End If
            
        Case "sixsigma"
            ' Maak een staafdiagram voor Top 10 batches met hoogste defectpercentage
            Dim batchRange
            batchRange = FindDataRange(ws, "batch", "Batch")
            
            If Not batchRange Is Nothing Then
                CreateBarChart ws, batchRange, "Top 10 Batches met Hoogste Defectpercentage", 10, ws.UsedRange.Rows.Count * 15 + 10
            End If
            
            ' Maak een staafdiagram voor Process Capability
            Dim capabilityRange
            capabilityRange = FindDataRange(ws, "capability", "Cp|Cpk")
            
            If Not capabilityRange Is Nothing Then
                CreateBarChart ws, capabilityRange, "Process Capability Analyse", 10, ws.UsedRange.Rows.Count * 15 + 320
            End If
            
        Case "toc"
            ' Maak een staafdiagram voor Procestijd Analyse
            Dim procestijdRange
            procestijdRange = FindDataRange(ws, "Procestijd", "Grinding|Filling|Packaging")
            
            If Not procestijdRange Is Nothing Then
                CreateBarChart ws, procestijdRange, "Procestijd Analyse", 10, ws.UsedRange.Rows.Count * 15 + 10
            End If
            
            ' Maak een staafdiagram voor Energieverbruik per Verpakkingsmachine
            Dim energieRange
            energieRange = FindDataRange(ws, "Energieverbruik", "Packager|Onbekend")
            
            If Not energieRange Is Nothing Then
                CreateBarChart ws, energieRange, "Energieverbruik per Verpakkingsmachine", 10, ws.UsedRange.Rows.Count * 15 + 320
            End If
    End Select
End Sub

' Functie om een databereik te vinden op basis van zoektermen
Function FindDataRange(ws, sectionKeyword, dataKeywords)
    Dim row, lastRow, startRow, endRow, col, dataFound
    Dim keywordArray, keyword, i, cellValue
    
    lastRow = ws.UsedRange.Rows.Count
    dataFound = False
    startRow = 0
    endRow = 0
    
    ' Split de dataKeywords op |
    keywordArray = Split(dataKeywords, "|")
    
    ' Zoek naar de sectie
    For row = 1 To lastRow
        cellValue = ws.Cells(row, 1).Value
        
        ' Controleer of de cel de sectie-keyword bevat
        If Not IsEmpty(cellValue) And InStr(1, cellValue, sectionKeyword, vbTextCompare) > 0 Then
            ' Zoek naar de data binnen deze sectie
            startRow = row
            
            ' Zoek naar het einde van de sectie (lege regel of nieuwe sectie)
            For i = row + 1 To lastRow
                If IsEmpty(ws.Cells(i, 1).Value) Or Trim(ws.Cells(i, 1).Value) = "" Then
                    endRow = i - 1
                    Exit For
                End If
                
                ' Controleer of deze rij data bevat die we zoeken
                For Each keyword In keywordArray
                    If InStr(1, ws.Cells(i, 1).Value, keyword, vbTextCompare) > 0 Then
                        dataFound = True
                        Exit For
                    End If
                Next
            Next
            
            ' Als we het einde van het werkblad hebben bereikt
            If endRow = 0 And i > lastRow Then
                endRow = lastRow
            End If
            
            ' Als we data hebben gevonden, stop met zoeken
            If dataFound Then
                Exit For
            Else
                ' Reset en zoek verder
                startRow = 0
                endRow = 0
            End If
        End If
    Next
    
    ' Als we data hebben gevonden, maak een bereik
    If dataFound And startRow > 0 And endRow > 0 Then
        ' Bepaal het aantal kolommen
        Dim lastCol
        lastCol = 2 ' Standaard 2 kolommen (categorie en waarde)
        
        ' Maak het bereik
        Set FindDataRange = ws.Range(ws.Cells(startRow, 1), ws.Cells(endRow, lastCol))
    Else
        Set FindDataRange = Nothing
    End If
End Function

' Functie om een cirkeldiagram te maken
Sub CreatePieChart(ws, dataRange, chartTitle, left, top)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlPie)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 2 ' Layout met percentages
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = True
        .Legend.Position = xlBottom
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = top
    chartObj.Left = left
End Sub

' Functie om een staafdiagram te maken
Sub CreateBarChart(ws, dataRange, chartTitle, left, top)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlColumnClustered)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 1 ' Standaard layout
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = False
        
        ' Stel de as-opties in
        .Axes(1).HasTitle = True ' X-as
        .Axes(1).AxisTitle.Text = "Categorie"
        .Axes(2).HasTitle = True ' Y-as
        .Axes(2).AxisTitle.Text = "Waarde"
        
        ' Voeg datawaarden toe
        .SeriesCollection(1).HasDataLabels = True
        .SeriesCollection(1).DataLabels.ShowValue = True
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = top
    chartObj.Left = left
End Sub

' Start het script
Call Main()
