import pandas as pd

# Laad de dataset
df = pd.read_csv('Kikker_cleaned_percentage.csv')

# Maak een kopie van de originele dataset voor vergelijking
df.to_csv('Klantretourpercentage_max100_before.csv', index=False)
print("Originele dataset opgeslagen als 'Klantretourpercentage_max100_before.csv'")

# Controleer de huidige waarden van Klantretourpercentage
print("\nHuidige unieke waarden in Klantretourpercentage:")
print(sorted(df['Klantretourpercentage'].unique()))

# Tel hoeveel waarden boven 100% zijn
above_100_count = (df['Klantretourpercentage'] > 100).sum()
print(f"\nAantal waarden boven 100%: {above_100_count}")

# Beperk alle waarden tot maximaal 100%
df.loc[df['Klantretourpercentage'] > 100, 'Klantretourpercentage'] = 100

# Controleer de nieuwe waarden van Klantretourpercentage
print("\nNieuwe unieke waarden in Klantretourpercentage:")
print(sorted(df['Klantretourpercentage'].unique()))

# Sla de aangepaste dataset op
df.to_csv('Kikker_cleaned_percentage_max100.csv', index=False)
print("\nAangepaste dataset opgeslagen als 'Kikker_cleaned_percentage_max100.csv'")

# Maak een kopie voor vergelijking
df.to_csv('Klantretourpercentage_max100_after.csv', index=False)
print("Aangepaste dataset opgeslagen als 'Klantretourpercentage_max100_after.csv'")

# Toon statistieken
print("\nStatistieken van de aangepaste Klantretourpercentage-kolom:")
print(df['Klantretourpercentage'].describe())
