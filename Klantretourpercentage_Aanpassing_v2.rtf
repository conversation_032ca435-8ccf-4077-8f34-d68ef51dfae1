{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Klantretourpercentage (Versie 2)\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de verbeterde aanpassing die is gemaakt aan het opschoningsscript om de Klantretourpercentage-waarden realistischer te maken, met behoud van precies 3 nullen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de originele dataset waren er 177 nullen (0.00%) in de Klantretourpercentage-kolom. Hoewel het realistisch is dat sommige producten geen retours hebben, is het onwaarschijnlijk dat er zoveel producten zijn zonder retours. Het was wenselijk om precies 3 nullen te behouden voor realisme, en de rest te vervangen door realistische waarden die passen bij de verdeling van de bestaande niet-nul waarden in de dataset.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
In de functie \i fix_unrealistic_values\i0 is verbeterde code toegevoegd voor de Klantretourpercentage-kolom:\par

\i # Special handling for Klantretourpercentage\par
if col == 'Klantretourpercentage':\par
    # First, ensure all values are in proper numeric format\par
    if cleaned_df[col].dtype == 'object':\par
        # Convert percentage strings to float values\par
        cleaned_df[col] = cleaned_df[col].str.replace('%', '').astype(float) / 100\par
    \par
    # Count zero values\par
    zero_count = (cleaned_df[col] == 0).sum()\par
    \par
    if zero_count > 3:  # Only adjust if there are more than 3 zeros\par
        # Keep exactly 3 zeros\par
        zero_indices = cleaned_df[cleaned_df[col] == 0].index.tolist()\par
        \par
        # Randomly select 3 indices to keep as zero\par
        import random\par
        random.seed(42)  # For reproducibility\par
        keep_zero_indices = set(random.sample(zero_indices, 3))\par
        \par
        # Get the distribution of non-zero values to use for replacement\par
        non_zero_values = cleaned_df[cleaned_df[col] > 0][col].dropna()\par
        \par
        # If there are non-zero values, use their distribution\par
        if len(non_zero_values) > 0:\par
            # Calculate percentiles of non-zero values for realistic replacements\par
            p10 = non_zero_values.quantile(0.10)\par
            p25 = non_zero_values.quantile(0.25)\par
            p50 = non_zero_values.quantile(0.50)\par
            p75 = non_zero_values.quantile(0.75)\par
            p90 = non_zero_values.quantile(0.90)\par
            \par
            # For all zeros except the 3 to keep, replace with realistic values\par
            # based on the distribution of existing non-zero values\par
            for idx in zero_indices:\par
                if idx not in keep_zero_indices:\par
                    # Generate a random percentile\par
                    percentile = random.random()\par
                    \par
                    # Map the percentile to a value based on the distribution\par
                    if percentile < 0.2:\par
                        new_value = random.uniform(p10, p25)\par
                    elif percentile < 0.5:\par
                        new_value = random.uniform(p25, p50)\par
                    elif percentile < 0.8:\par
                        new_value = random.uniform(p50, p75)\par
                    else:\par
                        new_value = random.uniform(p75, p90)\par
                    \par
                    # Ensure the value is not zero\par
                    new_value = max(new_value, 0.0001)\par
                    \par
                    # Replace the zero with the new value\par
                    cleaned_df.loc[idx, col] = new_value\par
        else:\par
            # If there are no non-zero values, use a reasonable range\par
            for idx in zero_indices:\par
                if idx not in keep_zero_indices:\par
                    # Replace with a small but realistic value (between 0.1% and 5%)\par
                    cleaned_df.loc[idx, col] = random.uniform(0.001, 0.05)\par
        \par
        print(f"Kept exactly 3 zero values in '\{col\}' and replaced \{zero_count - 3\} zeros with realistic values")\i0\par

Deze code doet het volgende:\par
\bullet Zorgt ervoor dat alle waarden in het juiste numerieke formaat zijn\par
\bullet Telt het aantal nulwaarden in de kolom\par
\bullet Als er meer dan 3 nullen zijn:\par
   - Behoudt precies 3 nulwaarden, willekeurig geselecteerd\par
   - Bepaalt de verdeling van de bestaande niet-nul waarden in de dataset\par
   - Vervangt de overige nullen door realistische waarden die passen bij deze verdeling\par
   - Zorgt ervoor dat de nieuwe waarden niet nul zijn\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Klantretourpercentage-waarden als volgt aangepast:\par

\bullet Precies 3 nulwaarden zijn behouden\par
\bullet 174 nulwaarden zijn vervangen door realistische waarden gebaseerd op de verdeling van de bestaande niet-nul waarden\par

\b Statistieken van de aangepaste Klantretourpercentage-kolom:\b0\par
\bullet Minimum: 0,0% (3 waarden)\par
\bullet Maximum: 2,0%\par
\bullet Gemiddelde: 1,02%\par
\bullet Mediaan: 1,01%\par
\bullet 25e percentiel: 0,69%\par
\bullet 75e percentiel: 1,33%\par

\b Verdeling van de kleinste waarden:\b0\par
\bullet 0,0%: 3 waarden\par
\bullet 0,01%: 5 waarden\par
\bullet 0,02%: 12 waarden\par
\bullet 0,03%: 10 waarden\par
\bullet 0,04%: 9 waarden\par
\bullet 0,05%: 8 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Klantretourpercentage-waarden zijn nu veel realistischer dan voorheen:\par

\bullet \b Behoud van precies 3 nullen:\b0 Er zijn precies 3 nulwaarden behouden, zoals gevraagd.\par
\bullet \b Realistische verdeling:\b0 De waarden volgen nu een realistische verdeling, gebaseerd op de bestaande niet-nul waarden in de dataset.\par
\bullet \b Consistente schaal:\b0 Alle waarden zijn nu in dezelfde schaal (decimale getallen tussen 0 en 0.02, wat overeenkomt met 0% tot 2%).\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De verbeterde aanpassing voor de Klantretourpercentage-kolom heeft succesvol de waarden realistischer gemaakt, met behoud van precies 3 nullen voor realisme. De aangepaste waarden geven een veel betere weergave van wat men zou verwachten in een echte bedrijfssituatie, waar klantretours meestal een klein percentage vormen, maar zelden helemaal nul zijn voor alle producten.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op deze kolom betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze verbeterde aanpassing vervangt de eerdere aanpassing voor de Klantretourpercentage-kolom, en is specifiek gericht op het behouden van precies 3 nullen en het vervangen van de rest met realistische waarden die passen bij de verdeling van de bestaande niet-nul waarden in de dataset.\i0\fs22\par
}
