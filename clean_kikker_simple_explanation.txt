UITLEG VAN HET VEREENVOUDIGDE SCRIPT

Het vereenvoudigde script (clean_kikker_simple.py) is ontworpen om de Kikker dataset op te schonen op een duidelijke en gestructureerde manier. Hieronder volgt een uitleg van de verschillende onderdelen:

1. STRUCTUUR VAN HET SCRIPT
---------------------------
Het script is opgedeeld in verschillende functies, waarbij elke functie één specifieke opschoningstaak uitvoert:

- load_data(): Laadt de CSV-data
- clean_cyclustijd(): Schoont de Cyclustijd-kolom op
- clean_klanttevredenheid(): Schoont de Klanttevredenheid-kolom op
- clean_fillingid(): Schoont de FillingID-kolom op
- clean_duurzaamheid_score(): Schoont de Duurzaamheid Score-kolom op
- clean_gewichtscontrole(): <PERSON><PERSON><PERSON> de Gewichtscontrole-kolom op
- clean_klantretourpercentage(): <PERSON><PERSON><PERSON> de Klantretourpercentage-kolom op
- main(): Coördineert het hele opschoningsproces

2. OPSCHONINGSSTAPPEN PER KOLOM
-------------------------------

a) Cyclustijd:
   - Ongeldige waarden (Onbekend, ABC, vraagtekens) worden vervangen door '4 uur'
   - Negatieve waarden worden omgezet naar positieve waarden
   - Waarden met decimalen ≥ 0,5 worden naar boven afgerond
   - Waarden met decimalen < 0,5 worden naar beneden afgerond
   - Alle waarden worden gestandaardiseerd naar gehele getallen met 'uur' suffix

b) Klanttevredenheid:
   - Lege waarden worden vervangen door de mediaan van de niet-lege waarden

c) FillingID:
   - Onrealistische waarden (>1000) worden vervangen door willekeurige waarden tussen 1 en 1000

d) Duurzaamheid Score:
   - Lege waarden worden vervangen door de mediaan van de niet-lege waarden

e) Gewichtscontrole:
   - Waarden met decimalen ≥ 0,5 worden naar boven afgerond
   - Waarden met decimalen < 0,5 worden naar beneden afgerond
   - Nulwaarden worden vervangen door 1
   - Alle waarden worden geconverteerd naar gehele getallen

f) Klantretourpercentage:
   - Nulwaarden worden vervangen door willekeurige waarden tussen het 10e en 25e percentiel
   - Alle waarden worden vermenigvuldigd met 100 en afgerond naar gehele getallen
   - Waarden boven 100% worden beperkt tot 100%

3. VOORDELEN VAN DEZE AANPAK
----------------------------
- Elke functie heeft één duidelijke verantwoordelijkheid
- De code is gemakkelijk te begrijpen en uit te leggen
- Elke opschoningsstap wordt duidelijk gedocumenteerd met print-statements
- Het script is modulair: je kunt gemakkelijk stappen toevoegen, verwijderen of aanpassen
- De originele data wordt bewaard voor vergelijking

4. HOE HET SCRIPT TE GEBRUIKEN
-----------------------------
1. Zorg ervoor dat de benodigde bibliotheken zijn geïnstalleerd (pandas, numpy)
2. Plaats het script in dezelfde map als het Kikker.csv bestand
3. Voer het script uit met: python clean_kikker_simple.py
4. Het script zal de opgeschoonde data opslaan als 'Kikker_cleaned_final.csv'

5. UITLEG AAN DE DOCENT
----------------------
Bij het uitleggen aan de docent kun je de volgende punten benadrukken:

- Het script is ontworpen om specifieke problemen in de dataset op te lossen
- Elke functie heeft een duidelijke naam die aangeeft wat deze doet
- De code bevat uitgebreide commentaren die uitleggen wat elke stap doet
- Het script print informatie over wat er gebeurt, zodat je kunt zien wat er wordt aangepast
- De originele data wordt bewaard, zodat je altijd kunt vergelijken wat er is veranderd
- Het script is modulair, zodat je gemakkelijk aanpassingen kunt maken als dat nodig is

Dit vereenvoudigde script voert dezelfde opschoningen uit als het oorspronkelijke script, maar is veel gemakkelijker te begrijpen en uit te leggen.
