{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Samenvatting van Aanpassingen aan de Kikker Dataset\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft alle aanpassingen die zijn gemaakt aan de <PERSON> dataset om deze te verbeteren voor analyse.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Cyclustijd Aanpassing\b0\fs22\par
\b Probleem:\b0 De Cyclustijd-kolom bevatte ongeldige waarden (Onbekend, ABC, vraagtekens), wa<PERSON><PERSON> met kom<PERSON>'s (decimalen), negatieve waarden en inconsistente notatie.\par

\b Aanpassing:\b0\par
\bullet Ongeldige waarden (Onbekend, ABC, vraagtekens) zijn vervangen door '4 uur'\par
\bullet Negatieve waarden zijn omgezet naar positieve waarden\par
\bullet Waarden met decimalen \u8805? 0,5 zijn naar boven afgerond\par
\bullet Waarden met decimalen < 0,5 zijn naar beneden afgerond\par
\bullet Alle waarden zijn gestandaardiseerd naar gehele getallen met 'uur' suffix\par

\b Resultaat:\b0 Alle Cyclustijd-waarden zijn nu gehele getallen met 'uur' suffix (1 uur, 2 uur, etc.), wat de leesbaarheid en bruikbaarheid verbetert.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Klanttevredenheid Aanpassing\b0\fs22\par
\b Probleem:\b0 De Klanttevredenheid-kolom bevatte 140 lege waarden (blanks), wat analyses kon verstoren.\par

\b Aanpassing:\b0\par
\bullet Lege waarden (blanks) zijn vervangen door de mediaan van de niet-lege waarden (5.0)\par

\b Resultaat:\b0 De Klanttevredenheid-kolom bevat nu geen lege waarden meer, alle waarden zijn tussen 1 en 10, wat een realistische schaal is voor klanttevredenheid.\par

\pard\sa200\sl276\slmult1\b\fs26 3. FillingID Aanpassing\b0\fs22\par
\b Probleem:\b0 De FillingID-kolom bevatte veel onrealistische waarden (waarden > 1000), wat analyses kon verstoren.\par

\b Aanpassing:\b0\par
\bullet Onrealistische waarden (> 1000) zijn vervangen door willekeurige waarden tussen 1 en 1000\par

\b Resultaat:\b0 Alle FillingID-waarden zijn nu tussen 1 en 1000, wat een realistischer bereik is voor ID's.\par

\pard\sa200\sl276\slmult1\b\fs26 4. Duurzaamheid Score Aanpassing\b0\fs22\par
\b Probleem:\b0 De Duurzaamheid Score-kolom bevatte 135 lege waarden (blanks), wat analyses kon verstoren.\par

\b Aanpassing:\b0\par
\bullet Lege waarden (blanks) zijn vervangen door de mediaan van de niet-lege waarden (50.0)\par

\b Resultaat:\b0 De Duurzaamheid Score-kolom bevat nu geen lege waarden meer, alle waarden zijn tussen 0 en 100, wat een realistische schaal is voor duurzaamheidsscores.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Gewichtscontrole Aanpassing\b0\fs22\par
\b Probleem:\b0 De Gewichtscontrole-kolom bevatte waarden met komma's (decimalen) en nulwaarden, wat de leesbaarheid verminderde.\par

\b Aanpassing:\b0\par
\bullet Waarden met decimalen \u8805? 0,5 zijn naar boven afgerond\par
\bullet Waarden met decimalen < 0,5 zijn naar beneden afgerond\par
\bullet Nulwaarden zijn vervangen door 1 (de meest voorkomende waarde)\par
\bullet Alle waarden zijn geconverteerd naar gehele getallen\par

\b Resultaat:\b0 Alle Gewichtscontrole-waarden zijn nu gehele getallen (1 of 2), zonder decimalen of nulwaarden, wat de leesbaarheid verbetert.\par

\pard\sa200\sl276\slmult1\b\fs26 6. Klantretourpercentage Aanpassing\b0\fs22\par
\b Probleem:\b0 De Klantretourpercentage-kolom bevatte nulwaarden (0.0) en decimale waarden, wat de leesbaarheid verminderde. Bovendien waren er waarden boven 100%, wat onlogisch is voor percentages.\par

\b Aanpassing:\b0\par
\bullet Nulwaarden (0.0) zijn vervangen door willekeurige waarden tussen 0,4 en 0,7\par
\bullet Decimale waarden zijn vermenigvuldigd met 100 en afgerond naar gehele getallen\par
\bullet Waarden boven 100% zijn beperkt tot 100%\par

\b Resultaat:\b0 Alle Klantretourpercentage-waarden zijn nu gehele getallen tussen 10 en 100, zonder decimalen of waarden boven 100%, wat de leesbaarheid en logica verbetert.\par

\pard\sa200\sl276\slmult1\b\fs26 7. Conclusie\b0\fs22\par
Alle aanpassingen samen hebben geleid tot een veel betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules. De dataset is nu:\par

\bullet \b Volledig:\b0 Geen lege waarden meer\par
\bullet \b Consistent:\b0 Alle waarden hebben een consistente notatie\par
\bullet \b Realistisch:\b0 Alle waarden liggen binnen realistische bereiken\par
\bullet \b Leesbaar:\b0 Alle waarden zijn gemakkelijk te interpreteren\par

Het definitieve opgeschoonde bestand \i Kikker_cleaned_final.csv\i0 bevat alle bovenstaande aanpassingen en is klaar voor verdere analyse.\par

\pard\sa200\sl276\slmult1\i\fs20 Voor elke aanpassing is een voor/na versie van de dataset bewaard, zodat de veranderingen kunnen worden geverifieerd indien nodig.\i0\fs22\par
}
