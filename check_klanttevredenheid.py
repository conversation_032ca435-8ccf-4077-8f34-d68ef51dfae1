import pandas as pd

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon statistieken van Klanttevredenheid
print('Klanttevredenheid statistieken:')
print(df['Klanttevredenheid'].describe())

# Toon de verdeling van Klanttevredenheid (frequentietabel)
print('\nFrequentietabel van Klanttevredenheid:')
print(df['Klanttevredenheid'].value_counts().sort_index())

# Toon de unieke waarden van Klanttevredenheid
print('\nUnieke waarden van Klanttevredenheid:')
print(sorted(df['Klanttevredenheid'].unique()))

# Controleer op lege waarden
print('\nAantal lege waarden in Klanttevredenheid:')
print(df['Klanttevredenheid'].isna().sum())
print(df['Klanttevredenheid'].isnull().sum())
print((df['Klanttevredenheid'] == '').sum() if df['Klanttevredenheid'].dtype == 'object' else 0)
print((df['Klanttevredenheid'] == ' ').sum() if df['Klanttevredenheid'].dtype == 'object' else 0)
