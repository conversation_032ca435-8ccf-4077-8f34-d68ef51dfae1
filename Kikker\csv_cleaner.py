#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Data Cleaner for Americaps

This script loads CSV data, performs cleaning operations, and saves the cleaned data.
It handles common data cleaning tasks such as:
- Handling missing values
- Converting data types
- Removing duplicates
- Fixing formatting issues
- Standardizing text data
- Handling outliers

Author: Americaps Data Team
Date: April 2025
"""

import pandas as pd
import numpy as np
import os
import sys
import argparse
from datetime import datetime


def load_csv(file_path, delimiter=',', encoding='utf-8'):
    """
    Load a CSV file into a pandas DataFrame.

    Args:
        file_path (str): Path to the CSV file
        delimiter (str): CSV delimiter character
        encoding (str): File encoding

    Returns:
        pandas.DataFrame: Loaded data or None if loading fails
    """
    try:
        print(f"Loading CSV file: {file_path}")
        df = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding)
        print(f"Successfully loaded {len(df)} rows and {len(df.columns)} columns")
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return None


def identify_column_types(df):
    """
    Identify the data types of columns in the DataFrame.

    Args:
        df (pandas.DataFrame): Input DataFrame

    Returns:
        dict: Dictionary with column types categorized
    """
    # Initialize column type categories
    column_types = {
        'numeric': [],
        'text': [],
        'datetime': [],
        'boolean': [],
        'id': [],
        'percentage': []
    }

    # Check each column
    for col in df.columns:
        # Check if column name suggests it's an ID
        if col.lower().endswith('id') or col.lower() == 'id':
            column_types['id'].append(col)
            continue

        # Check data type
        if pd.api.types.is_numeric_dtype(df[col]):
            # Check if it might be a percentage column
            if df[col].dropna().astype(str).str.contains('%').any() or 'percentage' in col.lower() or 'percent' in col.lower():
                column_types['percentage'].append(col)
            else:
                column_types['numeric'].append(col)
        elif pd.api.types.is_bool_dtype(df[col]):
            column_types['boolean'].append(col)
        else:
            # Try to convert to datetime
            try:
                pd.to_datetime(df[col], errors='raise')
                column_types['datetime'].append(col)
            except:
                # Check if it might be a percentage column stored as text
                if df[col].dropna().astype(str).str.contains('%').any() or 'percentage' in col.lower() or 'percent' in col.lower():
                    column_types['percentage'].append(col)
                else:
                    column_types['text'].append(col)

    return column_types


def clean_missing_values(df, column_types, numeric_strategy='median', text_strategy='mode'):
    """
    Handle missing values in the DataFrame.

    Args:
        df (pandas.DataFrame): Input DataFrame
        column_types (dict): Dictionary with column types
        numeric_strategy (str): Strategy for filling numeric missing values ('mean', 'median', 'mode', 'zero', 'none')
        text_strategy (str): Strategy for filling text missing values ('mode', 'empty', 'unknown', 'none')

    Returns:
        pandas.DataFrame: DataFrame with missing values handled
        list: Summary of changes made
    """
    df_clean = df.copy()
    summary = []

    # Handle numeric columns
    if numeric_strategy != 'none':
        for col in column_types['numeric']:
            missing_count = df_clean[col].isna().sum()
            if missing_count > 0:
                if numeric_strategy == 'mean':
                    fill_value = df_clean[col].mean()
                    df_clean[col] = df_clean[col].fillna(fill_value)
                    summary.append(f"Filled {missing_count} missing values in '{col}' with mean ({fill_value:.2f})")
                elif numeric_strategy == 'median':
                    fill_value = df_clean[col].median()
                    df_clean[col] = df_clean[col].fillna(fill_value)
                    summary.append(f"Filled {missing_count} missing values in '{col}' with median ({fill_value:.2f})")
                elif numeric_strategy == 'mode':
                    if not df_clean[col].dropna().empty:
                        fill_value = df_clean[col].mode()[0]
                        df_clean[col] = df_clean[col].fillna(fill_value)
                        summary.append(f"Filled {missing_count} missing values in '{col}' with mode ({fill_value})")
                elif numeric_strategy == 'zero':
                    df_clean[col] = df_clean[col].fillna(0)
                    summary.append(f"Filled {missing_count} missing values in '{col}' with zero")

    # Handle text columns
    if text_strategy != 'none':
        for col in column_types['text']:
            missing_count = df_clean[col].isna().sum()
            if missing_count > 0:
                if text_strategy == 'mode':
                    if not df_clean[col].dropna().empty:
                        fill_value = df_clean[col].mode()[0]
                        df_clean[col] = df_clean[col].fillna(fill_value)
                        summary.append(f"Filled {missing_count} missing values in '{col}' with mode ('{fill_value}')")
                elif text_strategy == 'empty':
                    df_clean[col] = df_clean[col].fillna('')
                    summary.append(f"Filled {missing_count} missing values in '{col}' with empty string")
                elif text_strategy == 'unknown':
                    df_clean[col] = df_clean[col].fillna('Unknown')
                    summary.append(f"Filled {missing_count} missing values in '{col}' with 'Unknown'")

    # Handle datetime columns (leave as NaT)
    for col in column_types['datetime']:
        missing_count = df_clean[col].isna().sum()
        if missing_count > 0:
            summary.append(f"Left {missing_count} missing values in datetime column '{col}' as NaT")

    return df_clean, summary


def standardize_text(df, column_types, trim=True, lowercase=False):
    """
    Standardize text columns by trimming whitespace and/or converting to lowercase.

    Args:
        df (pandas.DataFrame): Input DataFrame
        column_types (dict): Dictionary with column types
        trim (bool): Whether to trim whitespace
        lowercase (bool): Whether to convert to lowercase

    Returns:
        pandas.DataFrame: DataFrame with standardized text
        list: Summary of changes made
    """
    df_clean = df.copy()
    summary = []

    for col in column_types['text']:
        if df_clean[col].dtype == 'object':
            # Trim whitespace
            if trim:
                df_clean[col] = df_clean[col].astype(str).str.strip()
                summary.append(f"Trimmed whitespace in '{col}'")

            # Convert to lowercase
            if lowercase:
                df_clean[col] = df_clean[col].astype(str).str.lower()
                summary.append(f"Converted '{col}' to lowercase")

    return df_clean, summary


def fix_data_types(df, column_types):
    """
    Fix data types for columns.

    Args:
        df (pandas.DataFrame): Input DataFrame
        column_types (dict): Dictionary with column types

    Returns:
        pandas.DataFrame: DataFrame with fixed data types
        list: Summary of changes made
    """
    df_clean = df.copy()
    summary = []

    # Convert datetime columns
    for col in column_types['datetime']:
        try:
            df_clean[col] = pd.to_datetime(df_clean[col], errors='coerce')
            summary.append(f"Converted '{col}' to datetime format")
        except Exception as e:
            summary.append(f"Failed to convert '{col}' to datetime format: {e}")

    # Convert percentage columns
    for col in column_types['percentage']:
        try:
            # If stored as string with % symbol
            if df_clean[col].dtype == 'object':
                df_clean[col] = df_clean[col].astype(str).str.replace('%', '').astype(float) / 100
                summary.append(f"Converted '{col}' from percentage string to decimal")
            # If stored as numeric but needs to be divided by 100
            elif df_clean[col].max() > 1 and 'percent' in col.lower():
                df_clean[col] = df_clean[col] / 100
                summary.append(f"Converted '{col}' from percentage to decimal")
        except Exception as e:
            summary.append(f"Failed to convert '{col}' percentage format: {e}")

    return df_clean, summary


def remove_duplicates(df):
    """
    Remove duplicate rows from the DataFrame.

    Args:
        df (pandas.DataFrame): Input DataFrame

    Returns:
        pandas.DataFrame: DataFrame with duplicates removed
        list: Summary of changes made
    """
    df_clean = df.copy()
    summary = []

    # Check for duplicates
    duplicate_count = df_clean.duplicated().sum()
    if duplicate_count > 0:
        df_clean = df_clean.drop_duplicates()
        summary.append(f"Removed {duplicate_count} duplicate rows")
    else:
        summary.append("No duplicate rows found")

    return df_clean, summary


def handle_outliers(df, column_types, method='iqr', threshold=1.5):
    """
    Handle outliers in numeric columns.

    Args:
        df (pandas.DataFrame): Input DataFrame
        column_types (dict): Dictionary with column types
        method (str): Method for detecting outliers ('iqr', 'zscore')
        threshold (float): Threshold for outlier detection

    Returns:
        pandas.DataFrame: DataFrame with outliers handled
        list: Summary of changes made
    """
    df_clean = df.copy()
    summary = []

    for col in column_types['numeric']:
        if method == 'iqr':
            # IQR method
            Q1 = df_clean[col].quantile(0.25)
            Q3 = df_clean[col].quantile(0.75)
            IQR = Q3 - Q1

            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR

            outliers = ((df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)).sum()

            if outliers > 0:
                # Replace outliers with NaN
                df_clean.loc[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound), col] = np.nan
                summary.append(f"Identified {outliers} outliers in '{col}' using IQR method (replaced with NaN)")

        elif method == 'zscore':
            # Z-score method
            z_scores = np.abs((df_clean[col] - df_clean[col].mean()) / df_clean[col].std())
            outliers = (z_scores > threshold).sum()

            if outliers > 0:
                # Replace outliers with NaN
                df_clean.loc[z_scores > threshold, col] = np.nan
                summary.append(f"Identified {outliers} outliers in '{col}' using Z-score method (replaced with NaN)")

    return df_clean, summary


def clean_csv_data(df, options=None):
    """
    Clean CSV data using various methods.

    Args:
        df (pandas.DataFrame): Input DataFrame
        options (dict): Cleaning options

    Returns:
        pandas.DataFrame: Cleaned DataFrame
        list: Summary of all cleaning operations
    """
    if df is None:
        return None, ["Failed to load data"]

    # Set default options if not provided
    if options is None:
        options = {
            'numeric_fill': 'median',
            'text_fill': 'mode',
            'trim_text': True,
            'lowercase_text': False,
            'remove_duplicates': True,
            'handle_outliers': False,
            'outlier_method': 'iqr',
            'outlier_threshold': 1.5
        }

    # Initialize summary
    all_summary = []

    # Identify column types
    print("Identifying column types...")
    column_types = identify_column_types(df)

    # Print column type summary
    print(f"Found {len(column_types['numeric'])} numeric columns, {len(column_types['text'])} text columns, "
          f"{len(column_types['datetime'])} datetime columns, {len(column_types['percentage'])} percentage columns")

    # 1. Remove duplicates
    if options['remove_duplicates']:
        print("Removing duplicate rows...")
        df, summary = remove_duplicates(df)
        all_summary.extend(summary)

    # 2. Standardize text
    print("Standardizing text columns...")
    df, summary = standardize_text(df, column_types,
                                  trim=options['trim_text'],
                                  lowercase=options['lowercase_text'])
    all_summary.extend(summary)

    # 3. Fix data types
    print("Fixing data types...")
    df, summary = fix_data_types(df, column_types)
    all_summary.extend(summary)

    # 4. Handle outliers
    if options['handle_outliers']:
        print("Handling outliers...")
        df, summary = handle_outliers(df, column_types,
                                     method=options['outlier_method'],
                                     threshold=options['outlier_threshold'])
        all_summary.extend(summary)

    # 5. Clean missing values (do this last to handle any NaNs created by previous steps)
    print("Handling missing values...")
    df, summary = clean_missing_values(df, column_types,
                                      numeric_strategy=options['numeric_fill'],
                                      text_strategy=options['text_fill'])
    all_summary.extend(summary)

    return df, all_summary


def save_cleaned_data(df, output_path, delimiter=',', encoding='utf-8'):
    """
    Save cleaned DataFrame to CSV.

    Args:
        df (pandas.DataFrame): DataFrame to save
        output_path (str): Path to save the CSV file
        delimiter (str): CSV delimiter character
        encoding (str): File encoding

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Save the DataFrame - use sep instead of delimiter (pandas parameter name)
        df.to_csv(output_path, index=False, sep=delimiter, encoding=encoding)
        print(f"Successfully saved cleaned data to {output_path}")
        return True
    except Exception as e:
        print(f"Error saving cleaned data: {e}")
        return False


def generate_cleaning_report(df_original, df_cleaned, summary, output_path=None):
    """
    Generate a report of the cleaning process.

    Args:
        df_original (pandas.DataFrame): Original DataFrame
        df_cleaned (pandas.DataFrame): Cleaned DataFrame
        summary (list): Summary of cleaning operations
        output_path (str): Path to save the report (optional)

    Returns:
        str: Report text
    """
    # Create report
    report = []
    report.append("=" * 80)
    report.append("CSV DATA CLEANING REPORT")
    report.append("=" * 80)
    report.append("")

    # Add timestamp
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")

    # Add data summary
    report.append("DATA SUMMARY")
    report.append("-" * 80)
    report.append(f"Original data: {len(df_original)} rows, {len(df_original.columns)} columns")
    report.append(f"Cleaned data: {len(df_cleaned)} rows, {len(df_cleaned.columns)} columns")
    report.append(f"Rows removed: {len(df_original) - len(df_cleaned)}")
    report.append("")

    # Add missing values summary
    original_missing = df_original.isna().sum().sum()
    cleaned_missing = df_cleaned.isna().sum().sum()
    report.append(f"Original missing values: {original_missing}")
    report.append(f"Cleaned missing values: {cleaned_missing}")
    report.append(f"Missing values resolved: {original_missing - cleaned_missing}")
    report.append("")

    # Add cleaning operations
    report.append("CLEANING OPERATIONS")
    report.append("-" * 80)
    for item in summary:
        report.append(f"- {item}")
    report.append("")

    # Add column statistics
    report.append("COLUMN STATISTICS")
    report.append("-" * 80)
    for col in df_cleaned.columns:
        report.append(f"{col}:")

        # Handle numeric columns
        if pd.api.types.is_numeric_dtype(df_cleaned[col]):
            # Safely get min/max values
            try:
                orig_min = df_original[col].min() if not df_original[col].isna().all() else 'N/A'
                orig_max = df_original[col].max() if not df_original[col].isna().all() else 'N/A'
                clean_min = df_cleaned[col].min() if not df_cleaned[col].isna().all() else 'N/A'
                clean_max = df_cleaned[col].max() if not df_cleaned[col].isna().all() else 'N/A'

                report.append(f"  - Original range: [{orig_min}, {orig_max}]")
                report.append(f"  - Cleaned range: [{clean_min}, {clean_max}]")
            except Exception as e:
                report.append(f"  - Could not calculate range: {str(e)}")

        # For non-numeric columns, show unique values count
        else:
            try:
                orig_unique = df_original[col].nunique()
                clean_unique = df_cleaned[col].nunique()
                report.append(f"  - Original unique values: {orig_unique}")
                report.append(f"  - Cleaned unique values: {clean_unique}")
            except Exception as e:
                report.append(f"  - Could not calculate unique values: {str(e)}")

        # Show missing values for all columns
        orig_missing = df_original[col].isna().sum()
        clean_missing = df_cleaned[col].isna().sum()
        report.append(f"  - Original missing: {orig_missing} ({orig_missing/len(df_original):.1%})")
        report.append(f"  - Cleaned missing: {clean_missing} ({clean_missing/len(df_cleaned):.1%})")
        report.append("")

    # Join report lines
    report_text = "\n".join(report)

    # Save report if output path is provided
    if output_path:
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"Cleaning report saved to {output_path}")
        except Exception as e:
            print(f"Error saving cleaning report: {e}")

    return report_text


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Clean CSV data.')

    parser.add_argument('input_file', help='Input CSV file path')
    parser.add_argument('-o', '--output', help='Output CSV file path')
    parser.add_argument('-d', '--delimiter', default=',', help='CSV delimiter (default: comma)')
    parser.add_argument('-e', '--encoding', default='utf-8', help='File encoding (default: utf-8)')

    parser.add_argument('--numeric-fill', default='median',
                        choices=['mean', 'median', 'mode', 'zero', 'none'],
                        help='Strategy for filling numeric missing values')
    parser.add_argument('--text-fill', default='mode',
                        choices=['mode', 'empty', 'unknown', 'none'],
                        help='Strategy for filling text missing values')

    parser.add_argument('--no-duplicates', dest='remove_duplicates', action='store_false',
                        help='Do not remove duplicate rows')
    parser.add_argument('--no-trim', dest='trim_text', action='store_false',
                        help='Do not trim whitespace from text columns')
    parser.add_argument('--lowercase', dest='lowercase_text', action='store_true',
                        help='Convert text columns to lowercase')
    parser.add_argument('--handle-outliers', action='store_true',
                        help='Handle outliers in numeric columns')

    parser.add_argument('--report', help='Path to save cleaning report')

    parser.set_defaults(remove_duplicates=True, trim_text=True, lowercase_text=False, handle_outliers=False)

    return parser.parse_args()


def main():
    """Main function."""
    # Parse arguments
    args = parse_arguments()

    # Set default output file if not provided
    if not args.output:
        base, ext = os.path.splitext(args.input_file)
        args.output = f"{base}_cleaned{ext}"

    # Set default report file if not provided
    if not args.report:
        base, _ = os.path.splitext(args.output)
        args.report = f"{base}_report.txt"

    # Print header
    print("=" * 80)
    print("AMERICAPS CSV DATA CLEANER")
    print("=" * 80)

    # Load data
    df_original = load_csv(args.input_file, args.delimiter, args.encoding)
    if df_original is None:
        sys.exit(1)

    # Set cleaning options
    options = {
        'numeric_fill': args.numeric_fill,
        'text_fill': args.text_fill,
        'trim_text': args.trim_text,
        'lowercase_text': args.lowercase_text,
        'remove_duplicates': args.remove_duplicates,
        'handle_outliers': args.handle_outliers,
        'outlier_method': 'iqr',
        'outlier_threshold': 1.5
    }

    # Clean data
    print("\nCleaning data...")
    df_cleaned, summary = clean_csv_data(df_original, options)

    # Save cleaned data
    if not save_cleaned_data(df_cleaned, args.output, args.delimiter, args.encoding):
        sys.exit(1)

    # Generate and save report
    print("\nGenerating cleaning report...")
    report = generate_cleaning_report(df_original, df_cleaned, summary, args.report)

    print("\nCleaning process completed successfully!")


if __name__ == "__main__":
    main()
