{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management - Order Detail Screen", "AppName": "EuroCaps Order Detail", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(243, 242, 241, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366}, "Screens": [{"Name": "OrderDetailScreen", "Template": "BlankScreen", "Fill": "RGBA(243, 242, 241, 1)", "Controls": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 0, "Y": 0, "Width": 1366, "Height": 60, "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "Page<PERSON><PERSON>le", "ControlType": "Label", "Text": "\"Order Details - \" & SelectedOrder.OrderID", "X": 280, "Y": 80, "Width": 400, "Height": 40, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "BackButton", "ControlType": "<PERSON><PERSON>", "Text": "← Back to Orders", "X": 280, "Y": 130, "Width": 150, "Height": 35, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(OrderHistoryScreen, ScreenTransition.Fade)"}, {"Name": "EditButton", "ControlType": "<PERSON><PERSON>", "Text": "📝", "X": 1100, "Y": 80, "Width": 50, "Height": 40, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "PrintButton", "ControlType": "<PERSON><PERSON>", "Text": "🖨️", "X": 1160, "Y": 80, "Width": 50, "Height": 40, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "CancelButton", "ControlType": "<PERSON><PERSON>", "Text": "❌", "X": 1220, "Y": 80, "Width": 50, "Height": 40, "Fill": "RGBA(164, 38, 44, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "StatusContainer", "ControlType": "Rectangle", "X": 450, "Y": 130, "Width": 300, "Height": 35, "Fill": "RGBA(255, 152, 0, 0.2)", "BorderColor": "RGBA(255, 152, 0, 1)", "BorderThickness": 2, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5}, {"Name": "StatusLabel", "ControlType": "Label", "Text": "\"ORDER STATUS: 🟠 \" & SelectedOrder.Status", "X": 460, "Y": 135, "Width": 200, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 12, "Color": "RGBA(255, 152, 0, 1)"}, {"Name": "StatusDropdown", "ControlType": "Dropdown", "Items": "[\"New\", \"Processing\", \"Shipped\", \"Delivered\", \"Cancelled\"]", "X": 670, "Y": 130, "Width": 120, "Height": 35, "Default": "SelectedOrder.Status", "OnChange": "UpdateOrderStatus(StatusDropdown.Selected.Value)"}, {"Name": "OrderInfoContainer", "ControlType": "Rectangle", "X": 280, "Y": 180, "Width": 1000, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "OrderInfoTitle", "ControlType": "Label", "Text": "ORDER INFORMATION", "X": 300, "Y": 190, "Width": 200, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "OrderNumberLabel", "ControlType": "Label", "Text": "\"Order #: \" & SelectedOrder.OrderID", "X": 320, "Y": 220, "Width": 200, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "Customer<PERSON>abel", "ControlType": "Label", "Text": "\"Customer: \" & LookUp(Customers, CustomerID = SelectedOrder.CustomerID, CompanyName)", "X": 320, "Y": 240, "Width": 300, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "OrderDateLabel", "ControlType": "Label", "Text": "\"📅 Order: \" & Text(SelectedOrder.OrderDate, \"dd/mm/yy\")", "X": 320, "Y": 260, "Width": 200, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "DeliveryDateLabel", "ControlType": "Label", "Text": "\"🚚 Delivery: \" & Text(SelectedOrder.DeliveryDate, \"dd/mm/yy\")", "X": 540, "Y": 260, "Width": 200, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "NotesLabel", "ControlType": "Label", "Text": "\"📝 Notes: \" & SelectedOrder.Notes", "X": 760, "Y": 220, "Width": 300, "Height": 60, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "OrderItemsContainer", "ControlType": "Rectangle", "X": 280, "Y": 320, "Width": 1000, "Height": 200, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "OrderItemsTitle", "ControlType": "Label", "Text": "ORDER ITEMS", "X": 300, "Y": 330, "Width": 200, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "EditItemsButton", "ControlType": "<PERSON><PERSON>", "Text": "✏️ Edit Items", "X": 1150, "Y": 330, "Width": 120, "Height": 30, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(OrderItemsScreen, ScreenTransition.Fade, {OrderID: SelectedOrder.OrderID})"}, {"Name": "OrderItemsGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "X": 300, "Y": 370, "Width": 950, "Height": 120, "Items": "Filter(OrderItems, OrderID = SelectedOrder.OrderID)", "TemplatePadding": 5, "TemplateSize": 35, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 280, "Y": 540, "Width": 1000, "Height": 100, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "Summary<PERSON><PERSON><PERSON>", "ControlType": "Label", "Text": "DELIVERY & PAYMENT", "X": 300, "Y": 550, "Width": 200, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "SubtotalLabel", "ControlType": "Label", "Text": "\"Subtotal: €\" & Text(SelectedOrder.Subtotal, \"0.00\")", "X": 800, "Y": 580, "Width": 150, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "VATLabel", "ControlType": "Label", "Text": "\"VAT (21%): €\" & Text(SelectedOrder.VAT, \"0.00\")", "X": 800, "Y": 600, "Width": 150, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "TotalLabel", "ControlType": "Label", "Text": "\"Total: €\" & Text(SelectedOrder.Total, \"0.00\")", "X": 800, "Y": 620, "Width": 150, "Height": 20, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "ActionButtonsContainer", "ControlType": "Container", "X": 280, "Y": 660, "Width": 1000, "Height": 60, "LayoutDirection": "LayoutDirection.Horizontal", "LayoutJustifyContent": "LayoutJustifyContent.Start", "LayoutAlignItems": "LayoutAlignItems.Center"}, {"Name": "EditOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "📝 EDIT", "X": 280, "Y": 670, "Width": 120, "Height": 50, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "PrintOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "🖨️ PRINT", "X": 420, "Y": 670, "Width": 120, "Height": 50, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "ConfirmOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "✅ CONFIRM", "X": 560, "Y": 670, "Width": 120, "Height": 50, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "CancelOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "❌ CANCEL", "X": 700, "Y": 670, "Width": 120, "Height": 50, "Fill": "RGBA(164, 38, 44, 1)", "Color": "RGBA(255, 255, 255, 1)"}]}], "DataSources": [{"Name": "Orders", "Type": "Excel", "ConnectionString": "Orders.xlsx", "Table": "Orders"}, {"Name": "OrderItems", "Type": "Excel", "ConnectionString": "OrderItems.xlsx", "Table": "OrderItems"}, {"Name": "Customers", "Type": "Excel", "ConnectionString": "Customers.xlsx", "Table": "Customers"}, {"Name": "Products", "Type": "Excel", "ConnectionString": "Products.xlsx", "Table": "Products"}], "Variables": [{"Name": "SelectedOrder", "Type": "Record", "DefaultValue": "Blank()"}]}