# Product Catalog Screen - EuroCaps Order Management App

## Screen Layout (Improved with Clear UI Elements & Database Integration)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management    [🔔3] [<PERSON> ▼] [⚙️]      |
+---------------------------------------------------------------+
| SIDEBAR MENU  | Product Catalog                [➕ NEW PRODUCT]|
|               |                                               |
| 🏠 Dashboard  | SEARCH & FILTERS (Data from Products.xlsx)   |
| 👥 Customers  | ┌─────────────────────────────────────────────┐ |
| 📦 Products   | │🔍 Search: [Search products...         ][🔍]│ |
| 📋 Orders     | └─────────────────────────────────────────────┘ |
| 📊 Reports    |                                               |
| 🔔 Messages   | FILTER OPTIONS:                               |
| 👤 Users      | [Category ▼] [Price Range ▼] [Stock ▼]       |
| ⚙️ Settings   | [📤 Export] [🔄 Refresh] [🗑️ Clear Filters]   |
| 🚪 Logout     |                                               |
|               | PRODUCT OVERVIEW (Calculated from data)      |
|               | ┌─────────────┬─────────────┬─────────────┐   |
|               | │📦 TOTAL     │✅ IN STOCK  │⚠️ LOW STOCK │   |
|               | │PRODUCTS     │PRODUCTS     │ALERT        │   |
|               | │    24       │    22       │     2       │   |
|               | │All Types    │Available    │Reorder Soon │   |
|               | │             │             │             │   |
|               | └─────────────┴─────────────┴─────────────┘   |
|               |                                               |
|               | PRODUCT GRID (Data from Products.xlsx)       |
|               | ┌─────────────────────────────────────────────┐ |
|               | │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────┐ │ |
|               | │ │[📷]     │ │[📷]     │ │[📷]     │ │[📷] │ │ |
|               | │ │Espresso │ │Lungo    │ │Ristretto│ │Vanil│ │ |
|               | │ │Classic  │ │Intense  │ │Strong   │ │Flav │ │ |
|               | │ │€12.50   │ │€14.00   │ │€13.75   │ │€15.5│ │ |
|               | │ │Stock:50 │ │Stock:30 │ │Stock:25 │ │St:40│ │ |
|               | │ │Qty:[1▼] │ │Qty:[1▼] │ │Qty:[1▼] │ │[1▼]│ │ |
|               | │ │[🛒 Add] │ │[🛒 Add] │ │[🛒 Add] │ │[Add]│ │ |
|               | │ └─────────┘ └─────────┘ └─────────┘ └─────┘ │ |
|               | │                                             │ |
|               | │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────┐ │ |
|               | │ │[📷]     │ │[📷]     │ │[📷]     │ │[📷] │ │ |
|               | │ │Caramel  │ │Decaf    │ │Organic  │ │Prem │ │ |
|               | │ │Flavored │ │Espresso │ │Lungo    │ │Espr │ │ |
|               | │ │€16.00   │ │€11.50   │ │€18.00   │ │€20.0│ │ |
|               | │ │Stock:15 │ │Stock:35 │ │Stock:20 │ │St:10│ │ |
|               | │ │Qty:[1▼] │ │Qty:[1▼] │ │Qty:[1▼] │ │[1▼]│ │ |
|               | │ │[🛒 Add] │ │[🛒 Add] │ │[🛒 Add] │ │[Add]│ │ |
|               | │ └─────────┘ └─────────┘ └─────────┘ └─────┘ │ |
|               | └─────────────────────────────────────────────┘ |
|               |                                               |
|               | PAGINATION & VIEW OPTIONS                     |
|               | [◀ Previous] Page 1 of 3 [Next ▶]            |
|               | View: [Grid ▼] | Per page: [8 ▼] | 1-8 of 24 |
|               |                                               |
+---------------------------------------------------------------+
```

## PowerApps Components Specification

### **PowerApps Controls Used:**

1. **Text Input Control:**
   - **Search Text Input**: `TextInput1`
     - HintText: "Search products..."
     - OnChange: Update gallery filter

2. **Dropdown Controls:**
   - **Category Dropdown**: `Dropdown1`
     - Items: `["All Categories"].Concat(Distinct(Products, Category))`
     - Default: "All Categories"
   - **Price Range Dropdown**: `Dropdown2`
     - Items: `["All Prices", "€0-10", "€10-15", "€15-20", "€20+"]`
     - Default: "All Prices"
   - **Stock Filter Dropdown**: `Dropdown3`
     - Items: `["All Stock", "In Stock (>10)", "Low Stock (1-10)", "Out of Stock (0)"]`
     - Default: "All Stock"
   - **View Type Dropdown**: `Dropdown4`
     - Items: `["Grid View", "List View", "Table View"]`
     - Default: "Grid View"
   - **Items Per Page Dropdown**: `Dropdown5`
     - Items: `[4, 8, 12, 16, 24]`
     - Default: 8

3. **Button Controls:**
   - **New Product Button**: `Button1` - OnSelect: `Navigate(NewProductScreen)` (Visible: User().Role = "Admin")
   - **Search Button**: `Button2` - OnSelect: Apply search filter
   - **Export Button**: `Button3` - OnSelect: Export to Excel
   - **Refresh Button**: `Button4` - OnSelect: `Refresh(Products)`
   - **Clear Filters Button**: `Button5` - OnSelect: Reset all filters

4. **Gallery Control for Product Grid:**
   - **Product Gallery**: `Gallery1` (Flexible Height Gallery)
     - Items: `Filter(Products, FilterCondition)`
     - Data Source: Products.xlsx
     - Template Size: 200x250 (for grid view)
     - Wrap Count: 4 (for grid layout)
     - Template contains:
       - **Product Image**: `Image1` - Image: `ThisItem.ProductImage` (placeholder)
       - **Product Name Label**: `Label1` - Text: `ThisItem.ProductName`
       - **Category Label**: `Label2` - Text: `ThisItem.Category`
       - **Price Label**: `Label3` - Text: `"€" & Text(ThisItem.Price, "0.00")`
       - **Stock Label**: `Label4` - Text: `"Stock: " & ThisItem.Stock`
       - **Quantity Dropdown**: `Dropdown6` - Items: `Sequence(Min(ThisItem.Stock, 10))`
       - **Add to Cart Button**: `Button6` - OnSelect: Add to current order

5. **Label Controls for Statistics:**
   - **Total Products Label**: `Label5` - Text: `CountRows(Products)`
   - **In Stock Products Label**: `Label6` - Text: `CountRows(Filter(Products, Stock > 10))`
   - **Low Stock Alert Label**: `Label7` - Text: `CountRows(Filter(Products, Stock > 0 && Stock <= 10))`

6. **Rectangle Controls for Statistics Containers:**
   - **Stats Container 1**: `Rectangle1` - Contains total products info
   - **Stats Container 2**: `Rectangle2` - Contains in stock info
   - **Stats Container 3**: `Rectangle3` - Contains low stock alert

7. **Icon Controls:**
   - **Stock Status Icons**: `Icon1` - Show green/orange/red based on stock level
   - **Category Icons**: `Icon2` - Different icons per product category

### **Data Connections:**
- **Products Data Source**: Excel file connection to Products.xlsx
- **Current Order Collection**: Local collection for cart functionality

### **Formulas Used:**
- **Filtered Product List**:
  ```
  Filter(Products,
    (TextInput1.Text = "" || ProductName in TextInput1.Text || Description in TextInput1.Text || SKU in TextInput1.Text) &&
    (Dropdown1.Selected.Value = "All Categories" || Category = Dropdown1.Selected.Value) &&
    (Dropdown2.Selected.Value = "All Prices" ||
      (Dropdown2.Selected.Value = "€0-10" && Price <= 10) ||
      (Dropdown2.Selected.Value = "€10-15" && Price > 10 && Price <= 15) ||
      (Dropdown2.Selected.Value = "€15-20" && Price > 15 && Price <= 20) ||
      (Dropdown2.Selected.Value = "€20+" && Price > 20)
    ) &&
    (Dropdown3.Selected.Value = "All Stock" ||
      (Dropdown3.Selected.Value = "In Stock (>10)" && Stock > 10) ||
      (Dropdown3.Selected.Value = "Low Stock (1-10)" && Stock > 0 && Stock <= 10) ||
      (Dropdown3.Selected.Value = "Out of Stock (0)" && Stock = 0)
    )
  )
  ```

- **Add to Cart Function**:
  ```
  Collect(CurrentOrder, {
    ProductID: ThisItem.ProductID,
    ProductName: ThisItem.ProductName,
    Quantity: Dropdown6.Selected.Value,
    UnitPrice: ThisItem.Price,
    Total: ThisItem.Price * Dropdown6.Selected.Value
  })
  ```

- **Stock Validation**: `If(ThisItem.Stock > 0, true, false)` - Disable button if out of stock

### **Navigation & Context:**
- **Product Detail Navigation**: `Navigate(ProductDetailScreen, ScreenTransition.Fade, {SelectedProduct: ThisItem})`
- **Gallery Layout**: Use `TemplateSize` and `WrapCount` properties for responsive grid

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Product cards: White (#ffffff)
- Product types:
  - Espresso: Dark brown (#5d4037)
  - Lungo: Medium brown (#8d6e63)
  - Ristretto: Black (#212121)
  - Flavored: Various accent colors

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Product name: Arial, 14pt, Bold
- Product type: Arial, 12pt, Italic
- Product size: Arial, 12pt
- Button text: Arial, 12pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Products" highlighted
   - Icons for each menu item

3. **Search and Filter Section**
   - Search input with search icon
   - Type filter dropdown (Espresso, Lungo, Ristretto, Flavored)
   - Size filter dropdown (10, 20, 44)
   - Sort dropdown (Name, Type, Size)
   - Reset filters button

4. **Product Grid**
   - Cards arranged in a responsive grid (4 columns)
   - Each card contains:
     - Product image
     - Product name
     - Product type
     - Package size
     - Add button

5. **Pagination**
   - Previous/Next buttons
   - Page indicator
   - Items per page selector (optional)

## Interactions

1. **Search Functionality**
   - Real-time filtering as user types
   - Search across product name and description

2. **Filtering and Sorting**
   - Type filter to show specific product types
   - Size filter to show specific package sizes
   - Sort dropdown to order by different fields
   - Reset button clears all filters and search

3. **Product Actions**
   - Click on product card to view detailed information
   - "Add" button to quickly add to current order
     - If no order is in progress, prompt to create one
     - If order exists, add with default quantity (1)

4. **Pagination**
   - Previous/Next buttons navigate between pages
   - Disable Previous on first page
   - Disable Next on last page

## Accessibility Considerations
- Clear visual hierarchy
- Color is not the only indicator of product type
- Sufficient contrast for all text elements
- Alt text for all product images

## Notes for Implementation
- Consider adding quick view functionality
- Add quantity selector on hover/click
- For prototype: Use mock data and placeholder images
- Implement client-side pagination for the prototype
