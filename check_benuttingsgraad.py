import pandas as pd

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon statistieken van Benuttingsgraad
print('Benuttingsgraad statistieken:')
print(df['Benuttingsgraad'].describe())

# Toon de verdeling van Benuttingsgraad (frequentietabel)
print('\nFrequentietabel van Benuttingsgraad:')
print(df['Benuttingsgraad'].value_counts().sort_index())

# Toon de unieke waarden van Benuttingsgraad
print('\nUnieke waarden van Benuttingsgraad:')
print(sorted(df['Benuttingsgraad'].unique()))
