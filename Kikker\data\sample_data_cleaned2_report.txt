================================================================================
CSV DATA CLEANING REPORT
================================================================================

Generated: 2025-04-17 23:55:54

DATA SUMMARY
--------------------------------------------------------------------------------
Original data: 10 rows, 7 columns
Cleaned data: 10 rows, 7 columns
Rows removed: 0

Original missing values: 5
Cleaned missing values: 2
Missing values resolved: 3

CLEANING OPERATIONS
--------------------------------------------------------------------------------
- No duplicate rows found
- Trimmed whitespace in 'ProductName'
- Trimmed whitespace in 'InStock'
- Trimmed whitespace in 'ManufactureDate'
- Converted 'DefectRate' from percentage string to decimal
- Identified 1 outliers in 'Price' using IQR method (replaced with NaN)
- Filled 2 missing values in 'Price' with median (2.95)
- Filled 1 missing values in 'CustomerRating' with median (4.50)

COLUMN STATISTICS
--------------------------------------------------------------------------------
ProductID:
  - Original range: [1.0, 10.0]
  - Cleaned range: [1.0, 10.0]
  - Original missing: 1 (10.0%)
  - Cleaned missing: 1 (10.0%)

ProductName:
  - Original unique values: 9
  - Cleaned unique values: 9
  - Original missing: 0 (0.0%)
  - Cleaned missing: 0 (0.0%)

Price:
  - Original range: [2.5, 100.0]
  - Cleaned range: [2.5, 3.25]
  - Original missing: 1 (10.0%)
  - Cleaned missing: 0 (0.0%)

InStock:
  - Original unique values: 2
  - Cleaned unique values: 3
  - Original missing: 1 (10.0%)
  - Cleaned missing: 0 (0.0%)

ManufactureDate:
  - Original unique values: 10
  - Cleaned unique values: 10
  - Original missing: 0 (0.0%)
  - Cleaned missing: 0 (0.0%)

DefectRate:
  - Could not calculate range: '<=' not supported between instances of 'str' and 'float'
  - Original missing: 1 (10.0%)
  - Cleaned missing: 1 (10.0%)

CustomerRating:
  - Original range: [4.0, 4.9]
  - Cleaned range: [4.0, 4.9]
  - Original missing: 1 (10.0%)
  - Cleaned missing: 0 (0.0%)
