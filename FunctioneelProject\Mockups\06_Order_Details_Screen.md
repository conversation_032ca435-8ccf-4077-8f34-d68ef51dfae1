# Order Details Screen - EuroCaps Order Management App

## Screen Layout (Improved based on Use Cases & PowerApps)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management        [🔔] [User ▼] [⚙ Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Order Details - ORD-1089        [📝] [🖨️] [❌]    |
|           |                                                    |
| 🏠 Dashboard | ← Back to Orders                               |
| 👥 Customers |                                                |
| 📦 Products  | ORDER STATUS: 🟠 NEW [Change Status ▼]         |
| 📋 Orders    |                                                |
| 📊 Reports   | ORDER INFORMATION                              |
|              | +--------------------------------------------+ |
| ⚙ Settings   | | Order #: ORD-1089    | Priority: Normal   | |
| 🚪 Logout     | | Customer: Coffee World | 👤 David Lee      | |
|              | | 📧 <EMAIL> | 📞 +31-6-123456 | |
|              | | 📅 Order: 15/01/25   | 🚚 Delivery: 22/01 | |
|              | | 📝 Notes: Please deliver before noon        | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | ORDER ITEMS                    [✏️ Edit Items] |
|              | +--------------------------------------------+ |
|              | | Product      | Qty | Price  | Total    | ❌ | |
|              | |--------------------------------------------| |
|              | | Espresso     | 5   | €12.50 | €62.50   | ❌ | |
|              | | Lungo        | 3   | €14.00 | €42.00   | ❌ | |
|              | | Vanilla      | 2   | €15.50 | €31.00   | ❌ | |
|              | |--------------------------------------------| |
|              | | Total Items: 3 | Total Qty: 10 | €135.50 | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | DELIVERY & PAYMENT                             |
|              | +--------------------------------------------+ |
|              | | 🚚 Status: Not Shipped                     | |
|              | | 📅 Est. Delivery: 22/01/25                 | |
|              | | 💳 Payment: Pending                        | |
|              | | 📍 Address: Koffiestraat 123, Amsterdam    | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | ORDER TIMELINE                                 |
|              | +--------------------------------------------+ |
|              | | 🟠 15/01 10:30 - Order Created             | |
|              | | ⏳ Pending - Processing                     | |
|              | | ⏳ Pending - Shipping                       | |
|              | | ⏳ Pending - Delivery                       | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | [📝 EDIT] [🖨️ PRINT] [✅ CONFIRM] [❌ CANCEL]   |
|              |                                                |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Information sections: White (#ffffff)
- Status indicators:
  - New: Orange (#ff9800)
  - Processing: Blue (#4a6fa5)
  - Shipped: Purple (#9c27b0)
  - Delivered: Green (#4caf50)
  - Cancelled: Red (#f44336)
- Edit button: Blue (#4a6fa5)
- Print button: Gray (#757575)
- Cancel button: Red (#f44336)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Information labels: Arial, 12pt, Bold
- Information values: Arial, 12pt
- Status text: Arial, 14pt, Bold, White
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Icons for each menu item

3. **Order Status Section**
   - Prominent status display with color coding
   - Status change dropdown (for authorized users)

4. **Order Information Section**
   - Order details in a structured layout
   - Customer information with contact details
   - Dates and notes

5. **Order Items Section**
   - Table of ordered products
   - Columns: Product, Type, Size, Quantity, Total
   - Summary row with totals

6. **Delivery Information Section**
   - Current delivery status
   - Estimated delivery date
   - If shipped: tracking information, carrier details

7. **Action Buttons**
   - "Edit Order" (if status allows)
   - "Print" for order documentation
   - "Cancel Order" (if status allows)

## Interactions

1. **Status Management**
   - Status change dropdown shows allowed transitions
   - Confirmation required for status changes
   - Status changes may trigger notifications

2. **Order Editing**
   - "Edit Order" navigates to edit mode
   - Only available for orders in "New" status
   - Restricted fields based on order status

3. **Order Actions**
   - "Print" generates printable order document
   - "Cancel Order" prompts for confirmation
     - If confirmed, changes status to "Cancelled"
     - May require cancellation reason

4. **Navigation**
   - Back button returns to order list
   - Customer name links to customer details

## Conditional Elements

1. **Status-dependent Actions**
   - New: Edit, Cancel available
   - Processing: Cancel available, Edit disabled
   - Shipped/Delivered: All modification actions disabled

2. **Role-based Permissions**
   - Status change dropdown only for authorized roles
   - Cancel action may be restricted by role
   - Financial information visibility based on role

## Accessibility Considerations
- Clear visual hierarchy
- Color is not the only indicator of status
- Sufficient contrast for all text elements
- Logical tab order for interactive elements

## Notes for Implementation
- Consider adding order history/audit log section
- Add communication log for customer interactions
- For prototype: Use mock data for order details
- Implement basic status change functionality
