Option Explicit

' Script om direct grafieken te maken in Excel vanuit CSV-bestanden
' Dit script gebruikt alleen VBS en Excel, geen Python

' Constanten
Const xlDelimited = 1
Const xlTextQualifierDoubleQuote = 1
Const xlWindows = 2
Const xlNormal = -4143
Const xlCenter = -4108
Const xlBottom = -4107

' Grafiektypen
Const xlColumnClustered = 51 ' Gegroepeerde kolomgrafiek
Const xlPie = 5 ' Cirkeldiagram
Const xlLine = 4 ' Lijngrafiek
Const xlBarClustered = 57 ' Gegroepeerde staafgrafiek

' Hoofdfunctie
Sub Main()
    Dim fso, excel, csvFiles, csvFile, fileName, analysisType
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Zoek alle CSV-bestanden met "_Analyse_Clean" in de naam
    Set csvFiles = fso.GetFolder(".").Files
    
    ' Maak een map voor de visualisaties als deze nog niet bestaat
    Dim visualizationsFolder
    visualizationsFolder = fso.BuildPath(fso.GetAbsolutePathName("."), "visualisaties")
    If Not fso.FolderExists(visualizationsFolder) Then
        fso.CreateFolder(visualizationsFolder)
    End If
    
    ' Verwerk elk CSV-bestand
    For Each csvFile In csvFiles
        fileName = fso.GetFileName(csvFile)
        
        ' Controleer of het een analyse CSV-bestand is
        If InStr(fileName, "_Analyse_Clean.csv") > 0 Then
            ' Bepaal het type analyse
            If InStr(fileName, "Kaizen") > 0 Then
                analysisType = "kaizen"
            ElseIf InStr(fileName, "Lean") > 0 Then
                analysisType = "lean"
            ElseIf InStr(fileName, "SixSigma") > 0 Then
                analysisType = "sixsigma"
            ElseIf InStr(fileName, "TOC") > 0 Then
                analysisType = "toc"
            Else
                analysisType = ""
            End If
            
            ' Als het een bekend analysetype is, verwerk het bestand
            If analysisType <> "" Then
                WScript.Echo "Verwerken van " & fileName & "..."
                
                ' Verwerk het CSV-bestand en maak grafieken
                ProcessCSVFile excel, csvFile.Path, analysisType, visualizationsFolder
            End If
        End If
    Next
    
    ' Sluit Excel
    excel.Quit
    
    WScript.Echo "Alle bestanden zijn verwerkt. Grafieken zijn opgeslagen in de map 'visualisaties'."
End Sub

' Functie om een CSV-bestand te verwerken en grafieken te maken
Sub ProcessCSVFile(excel, csvFilePath, analysisType, visualizationsFolder)
    Dim wb, ws, fso, separator, dataRange
    Dim lastRow, lastCol, chartTitle, chartFileName
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Bepaal het scheidingsteken (tab of komma)
    separator = DetermineDelimiter(csvFilePath)
    
    ' Open het CSV-bestand in Excel
    Set wb = excel.Workbooks.Add
    Set ws = wb.Sheets(1)
    
    ' Importeer de CSV-data
    ImportCSVData excel, wb, csvFilePath, separator
    
    ' Bepaal het bereik van de data
    lastRow = ws.UsedRange.Rows.Count
    lastCol = ws.UsedRange.Columns.Count
    
    ' Maak grafieken op basis van het analysetype
    Select Case analysisType
        Case "kaizen"
            ' Maak een cirkeldiagram voor Panel Test Resultaten
            Dim panelTestRange
            panelTestRange = FindDataRange(ws, "Panel Test", "Voldoet")
            
            If Not panelTestRange Is Nothing Then
                chartTitle = "Panel Test Resultaten"
                chartFileName = fso.BuildPath(visualizationsFolder, "kaizen_panel_test_pie.png")
                CreatePieChart ws, panelTestRange, chartTitle, chartFileName
            End If
            
            ' Maak een staafdiagram voor Klanttevredenheid per Koffieboon Type
            Dim klanttevredenheidRange
            klanttevredenheidRange = FindDataRange(ws, "Klanttevredenheid", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klanttevredenheidRange Is Nothing Then
                chartTitle = "Klanttevredenheid per Koffieboon Type"
                chartFileName = fso.BuildPath(visualizationsFolder, "kaizen_klanttevredenheid_bar.png")
                CreateBarChart ws, klanttevredenheidRange, chartTitle, chartFileName
            End If
            
            ' Maak een staafdiagram voor Klantretourpercentage per Koffieboon Type
            Dim klantretourRange
            klantretourRange = FindDataRange(ws, "Klantretourpercentage", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klantretourRange Is Nothing Then
                chartTitle = "Klantretourpercentage per Koffieboon Type"
                chartFileName = fso.BuildPath(visualizationsFolder, "kaizen_klantretour_bar.png")
                CreateBarChart ws, klantretourRange, chartTitle, chartFileName
            End If
            
        Case "lean"
            ' Maak een staafdiagram voor Benuttingsgraad per Verpakkingsmachine
            Dim benuttingRange
            benuttingRange = FindDataRange(ws, "Benuttingsgraad", "Packager|Onbekend")
            
            If Not benuttingRange Is Nothing Then
                chartTitle = "Benuttingsgraad per Verpakkingsmachine"
                chartFileName = fso.BuildPath(visualizationsFolder, "lean_benutting_bar.png")
                CreateBarChart ws, benuttingRange, chartTitle, chartFileName
            End If
            
            ' Maak een staafdiagram voor Voorraadniveaus
            Dim voorraadRange
            voorraadRange = FindDataRange(ws, "Voorraadniveaus", "Gemiddelde|Mediaan")
            
            If Not voorraadRange Is Nothing Then
                chartTitle = "Voorraadniveaus Statistieken"
                chartFileName = fso.BuildPath(visualizationsFolder, "lean_voorraad_bar.png")
                CreateBarChart ws, voorraadRange, chartTitle, chartFileName
            End If
            
        Case "sixsigma"
            ' Maak een staafdiagram voor Top 10 batches met hoogste defectpercentage
            Dim batchRange
            batchRange = FindDataRange(ws, "batch", "Batch")
            
            If Not batchRange Is Nothing Then
                chartTitle = "Top 10 Batches met Hoogste Defectpercentage"
                chartFileName = fso.BuildPath(visualizationsFolder, "sixsigma_batch_defect_bar.png")
                CreateBarChart ws, batchRange, chartTitle, chartFileName
            End If
            
            ' Maak een staafdiagram voor Process Capability
            Dim capabilityRange
            capabilityRange = FindDataRange(ws, "capability", "Cp|Cpk")
            
            If Not capabilityRange Is Nothing Then
                chartTitle = "Process Capability Analyse"
                chartFileName = fso.BuildPath(visualizationsFolder, "sixsigma_capability_bar.png")
                CreateBarChart ws, capabilityRange, chartTitle, chartFileName
            End If
            
        Case "toc"
            ' Maak een staafdiagram voor Procestijd Analyse
            Dim procestijdRange
            procestijdRange = FindDataRange(ws, "Procestijd", "Grinding|Filling|Packaging")
            
            If Not procestijdRange Is Nothing Then
                chartTitle = "Procestijd Analyse"
                chartFileName = fso.BuildPath(visualizationsFolder, "toc_procestijd_bar.png")
                CreateBarChart ws, procestijdRange, chartTitle, chartFileName
            End If
            
            ' Maak een staafdiagram voor Energieverbruik per Verpakkingsmachine
            Dim energieRange
            energieRange = FindDataRange(ws, "Energieverbruik", "Packager|Onbekend")
            
            If Not energieRange Is Nothing Then
                chartTitle = "Energieverbruik per Verpakkingsmachine"
                chartFileName = fso.BuildPath(visualizationsFolder, "toc_energie_bar.png")
                CreateBarChart ws, energieRange, chartTitle, chartFileName
            End If
    End Select
    
    ' Sluit het werkboek zonder op te slaan
    wb.Close False
End Sub

' Functie om het scheidingsteken van een CSV-bestand te bepalen
Function DetermineDelimiter(filePath)
    Dim fso, file, line, delimiter
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    
    ' Lees de eerste regel
    If Not file.AtEndOfStream Then
        line = file.ReadLine
        
        ' Controleer welk scheidingsteken wordt gebruikt
        If InStr(line, vbTab) > 0 Then
            delimiter = vbTab
        ElseIf InStr(line, ",") > 0 Then
            delimiter = ","
        Else
            delimiter = "," ' Standaard
        End If
    Else
        delimiter = "," ' Standaard
    End If
    
    file.Close
    
    DetermineDelimiter = delimiter
End Function

' Functie om CSV-data te importeren in Excel
Sub ImportCSVData(excel, wb, csvFilePath, delimiter)
    Dim ws, fso, file, line, row, col, values
    
    Set ws = wb.Sheets(1)
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(csvFilePath, 1) ' 1 = ForReading
    
    row = 1
    
    ' Lees elke regel van het CSV-bestand
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        ' Sla lege regels over
        If Trim(line) <> "" Then
            ' Split de regel op het scheidingsteken
            values = Split(line, delimiter)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                ws.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
End Sub

' Functie om een databereik te vinden op basis van zoektermen
Function FindDataRange(ws, sectionKeyword, dataKeywords)
    Dim row, lastRow, startRow, endRow, col, dataFound
    Dim keywordArray, keyword, i, cellValue
    
    lastRow = ws.UsedRange.Rows.Count
    dataFound = False
    startRow = 0
    endRow = 0
    
    ' Split de dataKeywords op |
    keywordArray = Split(dataKeywords, "|")
    
    ' Zoek naar de sectie
    For row = 1 To lastRow
        cellValue = ws.Cells(row, 1).Value
        
        ' Controleer of de cel de sectie-keyword bevat
        If Not IsEmpty(cellValue) And InStr(1, cellValue, sectionKeyword, vbTextCompare) > 0 Then
            ' Zoek naar de data binnen deze sectie
            startRow = row
            
            ' Zoek naar het einde van de sectie (lege regel of nieuwe sectie)
            For i = row + 1 To lastRow
                If IsEmpty(ws.Cells(i, 1).Value) Or Trim(ws.Cells(i, 1).Value) = "" Then
                    endRow = i - 1
                    Exit For
                End If
                
                ' Controleer of deze rij data bevat die we zoeken
                For Each keyword In keywordArray
                    If InStr(1, ws.Cells(i, 1).Value, keyword, vbTextCompare) > 0 Then
                        dataFound = True
                        Exit For
                    End If
                Next
            Next
            
            ' Als we het einde van het werkblad hebben bereikt
            If endRow = 0 And i > lastRow Then
                endRow = lastRow
            End If
            
            ' Als we data hebben gevonden, stop met zoeken
            If dataFound Then
                Exit For
            Else
                ' Reset en zoek verder
                startRow = 0
                endRow = 0
            End If
        End If
    Next
    
    ' Als we data hebben gevonden, maak een bereik
    If dataFound And startRow > 0 And endRow > 0 Then
        ' Bepaal het aantal kolommen
        Dim lastCol
        lastCol = 2 ' Standaard 2 kolommen (categorie en waarde)
        
        ' Maak het bereik
        Set FindDataRange = ws.Range(ws.Cells(startRow, 1), ws.Cells(endRow, lastCol))
    Else
        Set FindDataRange = Nothing
    End If
End Function

' Functie om een cirkeldiagram te maken
Sub CreatePieChart(ws, dataRange, chartTitle, chartFileName)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlPie)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 2 ' Layout met percentages
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = True
        .Legend.Position = xlBottom
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 400
    chartObj.Height = 300
    chartObj.Top = dataRange.Top
    chartObj.Left = dataRange.Left + dataRange.Width + 20
    
    ' Exporteer de grafiek als afbeelding
    chart.Export chartFileName
End Sub

' Functie om een staafdiagram te maken
Sub CreateBarChart(ws, dataRange, chartTitle, chartFileName)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlColumnClustered)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 1 ' Standaard layout
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = False
        
        ' Stel de as-opties in
        .Axes(1).HasTitle = True ' X-as
        .Axes(1).AxisTitle.Text = "Categorie"
        .Axes(2).HasTitle = True ' Y-as
        .Axes(2).AxisTitle.Text = "Waarde"
        
        ' Voeg datawaarden toe
        .SeriesCollection(1).HasDataLabels = True
        .SeriesCollection(1).DataLabels.ShowValue = True
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = dataRange.Top
    chartObj.Left = dataRange.Left + dataRange.Width + 20
    
    ' Exporteer de grafiek als afbeelding
    chart.Export chartFileName
End Sub

' Start het script
Call Main()
