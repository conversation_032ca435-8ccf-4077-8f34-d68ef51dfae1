<!DOCTYPE html>
<html>
<head>
    <title>Argumentatieschema Bol Data Coaching</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            margin-top: 30px;
            font-weight: bold;
        }
        .stelling {
            font-weight: bold;
            margin-top: 20px;
        }
        .argument, .onderbouwing, .tegenargument, .weerlegging {
            margin-left: 40px;
            margin-bottom: 10px;
        }
        .argument-label, .onderbouwing-label, .tegenargument-label, .weerlegging-label {
            font-style: italic;
        }
        .conclusie {
            margin-top: 30px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Argumentatieschema</h1>
    
    <h2>Hoofdvraag: Wat maakt een data coaching aanpak succesvol en hoe kan deze worden toegepast in organisaties?</h2>
    
    <div class="stelling">1. Stelling: Een succesvolle data coaching aanpak vereist een gestructureerd framework voor het meten en verbeteren van data maturity.</div>
    <div class="argument"><span class="argument-label">Argument:</span> Een goed Data Maturity Framework met verschillende pijlers helpt teams om hun huidige niveau te bepalen en gerichte verbeteringen te maken.</div>
    <div class="onderbouwing"><span class="onderbouwing-label">Onderbouwing:</span> Door middel van een nulmeting krijgen teams inzicht in hun huidige niveau en kunnen ze gericht kiezen aan welke aspecten ze willen werken.</div>
    <div class="tegenargument"><span class="tegenargument-label">Tegenargument:</span> Een framework alleen is niet voldoende; er moet ook draagvlak zijn binnen teams om ermee aan de slag te gaan.</div>
    <div class="weerlegging"><span class="weerlegging-label">Weerlegging:</span> Daarom is het effectiever om te werken met teams die zelf aankloppen voor hulp, waardoor er al intrinsieke motivatie aanwezig is.</div>
    
    <div class="stelling">2. Stelling: Data literacy moet op verschillende niveaus worden aangeboden om effectief te zijn voor de hele organisatie.</div>
    <div class="argument"><span class="argument-label">Argument:</span> Effectieve data literacy programma's bieden verschillende niveaus (bijvoorbeeld 0-3) en meerdere componenten (zoals lezen, schrijven en spreken van data).</div>
    <div class="onderbouwing"><span class="onderbouwing-label">Onderbouwing:</span> Niet iedereen in de organisatie heeft hetzelfde niveau van data-expertise nodig, maar iedereen moet wel basisvaardigheden hebben.</div>
    <div class="tegenargument"><span class="tegenargument-label">Tegenargument:</span> Het kan moeilijk zijn om mensen te motiveren om hun data vaardigheden te verbeteren als ze dit niet direct nodig hebben in hun dagelijkse werk.</div>
    <div class="weerlegging"><span class="weerlegging-label">Weerlegging:</span> Door trainingen te personaliseren met organisatie-specifieke voorbeelden en bovendien een data community te creeren, wordt de relevantie duidelijker.</div>
    
    <div class="stelling">3. Stelling: Management buy-in is cruciaal voor het succes van een datagedreven cultuur.</div>
    <div class="argument"><span class="argument-label">Argument:</span> De rol van leiders in het promoten van datagedreven werken is een belangrijke factor in het succes van data initiatieven.</div>
    <div class="onderbouwing"><span class="onderbouwing-label">Onderbouwing:</span> Als managers niet regelmatig vragen naar KPI's en OKR's, dan zullen teams deze ook niet serieus nemen.</div>
    <div class="tegenargument"><span class="tegenargument-label">Tegenargument:</span> Bottom-up initiatieven kunnen ook succesvol zijn zonder directe betrokkenheid van management.</div>
    <div class="weerlegging"><span class="weerlegging-label">Weerlegging:</span> Hoewel bottom-up initiatieven waardevol zijn, laat de praktijkervaring zien dat zowel teams als management betrokken moeten zijn voor duurzaam succes.</div>
    
    <div class="stelling">4. Stelling: KPI's en OKR's moeten zo worden geformuleerd dat teams er zelf invloed op hebben.</div>
    <div class="argument"><span class="argument-label">Argument:</span> Effectieve KPI's moeten gaan over zaken waar teams zelf verantwoordelijkheid voor kunnen nemen.</div>
    <div class="onderbouwing"><span class="onderbouwing-label">Onderbouwing:</span> Wanneer teams invloed hebben op hun KPI's, dan voelen ze zich verantwoordelijk en zullen ze eerder actie ondernemen bij afwijkingen.</div>
    <div class="tegenargument"><span class="tegenargument-label">Tegenargument:</span> Sommige belangrijke bedrijfsdoelen (zoals NPS) zijn niet direct te beinvloeden door individuele teams.</div>
    <div class="weerlegging"><span class="weerlegging-label">Weerlegging:</span> In dat geval moeten teams zoeken naar de drijvers van deze KPI's die ze wel kunnen beinvloeden, of naar leading indicators die eerder in de funnel zitten.</div>
    
    <div class="stelling">5. Stelling: Een schaalbare aanpak vereist self-service tools en een community naast persoonlijke coaching.</div>
    <div class="argument"><span class="argument-label">Argument:</span> Organisaties kunnen Data Coaching Toolkits ontwikkelen en actief bouwen aan data communities om schaalbaar te blijven.</div>
    <div class="onderbouwing"><span class="onderbouwing-label">Onderbouwing:</span> Met slechts een beperkt aantal data coaches in een grote organisatie is persoonlijke coaching alleen niet voldoende, daarom zijn self-service tools essentieel.</div>
    <div class="tegenargument"><span class="tegenargument-label">Tegenargument:</span> Self-service tools kunnen niet de persoonlijke begeleiding en maatwerk bieden die nodig is voor echte verandering.</div>
    <div class="weerlegging"><span class="weerlegging-label">Weerlegging:</span> Door een combinatie van persoonlijke coaching, self-service tools en een community-aanpak kunnen organisaties toch effectief zijn ondanks beperkte capaciteit.</div>
    
    <div class="conclusie">
        <h2>Conclusie:</h2>
        <p>Een succesvolle data coaching aanpak is dankzij een combinatie van een gestructureerd framework, verschillende niveaus van data literacy training, management buy-in, goed geformuleerde KPI's/OKR's en een schaalbare aanpak met self-service tools. Deze elementen kunnen als best practice dienen voor organisaties, mits aangepast aan hun specifieke context en cultuur. Het belangrijkste inzicht is dat datagedreven werken niet alleen gaat over tools en technologie, maar vooral over cultuur, vaardigheden en eigenaarschap binnen teams.</p>
    </div>
</body>
</html>
