import pandas as pd

# Laad de aangepaste dataset
df = pd.read_csv('Ki<PERSON>_cleaned_percentage_max100.csv')

# Toon statistieken van Klantretourpercentage
print('Klantretourpercentage statistieken:')
print(df['Klantretourpercentage'].describe())

# Toon de verdeling van Klantretourpercentage (frequentietabel)
print('\nFrequentietabel van Klantretourpercentage:')
print(df['Klantretourpercentage'].value_counts().sort_index())

# Toon de unieke waarden van Klantretourpercentage
print('\nUnieke waarden van Klantretourpercentage:')
print(sorted(df['Klantretourpercentage'].unique()))

# Controleer of er nog waarden boven 100% zijn
above_100 = (df['Klantretourpercentage'] > 100).sum()
print(f'\nAantal waarden boven 100%: {above_100}')
