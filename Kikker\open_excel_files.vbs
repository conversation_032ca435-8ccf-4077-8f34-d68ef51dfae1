Option Explicit

' Script om Excel-bestanden te openen zodat u handmatig grafieken kunt toevoegen

Sub Main()
    ' Maak een Excel-applicatie
    Dim excel
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Open de Excel-bestanden
    OpenExcelFile "Kaizen_Analyse_Clean.xlsx", excel
    OpenExcelFile "Lean_Analyse_Clean.xlsx", excel
    OpenExcelFile "TOC_Analyse_Clean.xlsx", excel
    OpenExcelFile "SixSigma_Analyse_Clean.xlsx", excel
    
    ' Toon instructies
    MsgBox "Excel-bestanden zijn geopend. Volg deze stappen om grafieken toe te voegen:" & vbCrLf & vbCrLf & _
           "1. Selecteer de data die u wilt visualiseren" & vbCrLf & _
           "2. Ga naar het menu 'Invoegen'" & vbCrLf & _
           "3. Kies het gewenste grafiektype (staafdiagram, cirkeldiagram, etc.)" & vbCrLf & _
           "4. Pas de grafiek aan naar wens" & vbCrLf & _
           "5. Sla het bestand op"
End Sub

' Functie om een Excel-bestand te openen
Sub OpenExcelFile(excelFile, excel)
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of het bestand bestaat
    If fso.FileExists(excelFile) Then
        ' Open het Excel-bestand
        excel.Workbooks.Open fso.GetAbsolutePathName(excelFile)
    Else
        MsgBox "Het bestand " & excelFile & " bestaat niet."
    End If
End Sub

' Start het script
Call Main()
