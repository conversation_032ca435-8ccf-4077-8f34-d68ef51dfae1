# Use Case Scenario's - EuroCaps Systeem

## 10 Hoofdcategorieën Use Cases

| **Naam Use Case** | **Actor** | **Aannames** | **Beschrijving** | **Uitzonderingen** | **Resultaat** |
|-------------------|-----------|--------------|------------------|-------------------|---------------|
| **UC1 - Login Systeem** | Manager <PERSON>ri<PERSON><PERSON>, Productiemedewerker, Manager In<PERSON>op, Manager Logis<PERSON>k | Gebruiker heeft geldige inloggegevens en systeem is operationeel | De gebruiker opent het EuroCaps systeem, voert gebruikersnaam en wachtwoord in, en klikt op inloggen. Het systeem verifieert de gegevens en geeft toegang tot de juiste modules op basis van gebruikersrol | Foutieve inloggegevens, account geb<PERSON><PERSON>, systeem niet beschik<PERSON>, netwerk problemen | Gebruiker krijgt toegang tot het systeem met rolspecifieke rechten en modules |
| **UC2 - Voorraad Beheren** | Manager bedrijfsvoering, Manager Inkoop | Gebruiker is ingelogd en voorraadsysteem is operationeel | De gebruiker navigeert naar de voorraadmodule, selecteert grondstofcategorieën, bekijkt actuele voorraadniveaus, ontvangt automatische meldingen bij minimumvoorraad, en kan voorraadgegevens bijwerken na ontvangst of verbruik | Geen toegang rechten, systeem niet beschikbaar, ongeldige voorraadwaarden, foutieve minimumwaarden | Actuele voorraadstatus wordt weergegeven, meldingen worden verzonden, en voorraadniveaus worden correct bijgewerkt |
| **UC3 - Productie Beheren** | Productiemedewerker, Manager bedrijfsvoering | Machines zijn operationeel, grondstoffen beschikbaar, productieorders zijn aanwezig | De medewerker selecteert productieorder, controleert beschikbaarheid grondstoffen, start productieproces, registreert productiedata (aantallen, tijdsduur, gebruikte materialen), voert kwaliteitscontroles uit, en manager bekijkt productierapporten | Machine defect, onvoldoende grondstoffen, geen productieorder, test apparatuur defect, systeem storing | Productieproces wordt gestart en geregistreerd, kwaliteitsresultaten worden vastgelegd, productierapporten zijn beschikbaar |
| **UC4 - Logistiek Beheren** | Manager Logistiek, Manager bedrijfsvoering | Orders zijn gereed voor verzending, transportsysteem is beschikbaar | De manager bekijkt openstaande orders, plant verzendingen op basis van prioriteit, organiseert transport, boekt transporteurs, werkt leveringsstatus bij tijdens transport, en genereert logistiekrapporten | Geen orders beschikbaar, transporteur niet beschikbaar, documentatie ontbreekt, communicatie problemen | Verzendschema wordt aangemaakt, transport wordt geboekt en bevestigd, leveringsstatus wordt bijgehouden |
| **UC5 - Kwaliteitscontrole** | Productiemedewerker, Manager bedrijfsvoering | Test apparatuur is gekalibreerd, producten zijn gereed voor controle | De medewerker neemt monsters van geproduceerde capsules, voert gestandaardiseerde kwaliteitstests uit volgens procedures, registreert testresultaten in het systeem, rapporteert afwijkingen aan management, en manager bekijkt kwaliteitsrapporten en trends | Apparatuur defect, onvoldoende monsters, procedure onduidelijk, systeem niet beschikbaar, onduidelijke kwaliteitsnormen | Testresultaten worden vastgelegd, afwijkingsrapporten worden aangemaakt, kwaliteitsstatistieken zijn beschikbaar |
| **UC6 - Inkoop Beheren** | Manager Inkoop, Manager bedrijfsvoering | Leveranciersdatabase is beschikbaar, budget is goedgekeurd | De manager beheert leveranciersgegevens, creëert en plaatst inkooporders, specificeert producten en hoeveelheden, verzendt orders naar leveranciers, controleert ontvangen leveringen tegen orders, en genereert inkooprapporten | Database niet beschikbaar, leverancier niet beschikbaar, onvoldoende budget, leveringen komen niet overeen, kwaliteit onvoldoende | Leveranciersgegevens worden bijgewerkt, inkooporders worden geplaatst en bevestigd, leveringen worden goedgekeurd |
| **UC7 - Rapportage** | Manager bedrijfsvoering, Manager Inkoop, Manager Logistiek | Relevante data is beschikbaar in het systeem | De manager selecteert gewenst rapporttype (productie, voorraad, inkoop, of logistiek), specificeert periode en parameters, genereert rapporten met statistieken, trends en KPI's, en exporteert rapporten voor analyse | Onvoldoende data beschikbaar, systeem storing, ongeldige parameters, geen toegangsrechten | Gedetailleerde rapporten worden gegenereerd en weergegeven met relevante statistieken en trends |
| **UC8 - Gebruikersbeheer** | Manager bedrijfsvoering, Productiemedewerker | Beheerrechten zijn toegekend, gebruikersdatabase is beschikbaar | De manager creëert nieuwe gebruikersaccounts, wijst rollen en rechten toe op basis van functie, beheert gebruikersprofielen, en gebruikers kunnen hun eigen wachtwoord wijzigen en profielgegevens bijwerken | Gebruikersnaam al bestaat, ongeldige gegevens, geen beheerrechten, foutief huidig wachtwoord | Nieuwe gebruikersaccounts worden aangemaakt, rechten worden correct toegewezen, profielgegevens worden bijgewerkt |
| **UC9 - Onderhoud** | Productiemedewerker, Manager bedrijfsvoering | Onderhoudsschema is beschikbaar, technici zijn toegewezen | De manager plant preventief onderhoud op basis van schema, wijst technici toe, medewerkers melden storingen via het systeem, voeren onderhoudswerkzaamheden uit, testen functionaliteit, en rapporteren voltooiing | Geen technici beschikbaar, machine in gebruik, onderdelen niet beschikbaar, onverwachte problemen | Onderhoudsplanning wordt vastgelegd, storingen worden geregistreerd, onderhoud wordt uitgevoerd en gerapporteerd |
| **UC10 - Traceerbaarheid** | Manager bedrijfsvoering, Manager Inkoop, Manager Logistiek | Traceerbaarheidsdata is volledig geregistreerd | De manager voert batch- of verzendnummers in, zoekt volledige productiehistorie, bekijkt traceerbaarheid van grondstoffen tot eindproduct, traceert leveranciers en oorsprong materialen, en volgt verzendroutes en leveringsstatus | Batch-/verzendnummer niet gevonden, onvolledige traceerbaarheidsdata, systeem storing | Volledige traceerbaarheidshistorie wordt weergegeven van grondstof tot eindklant |

