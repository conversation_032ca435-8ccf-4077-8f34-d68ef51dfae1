<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-15T10:00:00.000Z" agent="5.0" etag="abc123" version="22.1.16" type="device" pages="10">
  <diagram id="page1" name="UC1 - Login Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system1" value="EuroCaps Order Management App" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="275" y="50" width="200" height="150" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor1_1" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="125" y="70" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor1_2" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="125" y="150" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor1_3" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="525" y="70" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor1_4" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="525" y="150" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc1_1" value="Login" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="315" y="100" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc1_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_1" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc1_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_2" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc1_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_3" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc1_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_4" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  
  <diagram id="page2" name="UC2 - Dashboard Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system2" value="Dashboard Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor2_1" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor2_2" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor2_3" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="510" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc2_1" value="Dashboard bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc2_2" value="KPI's monitoren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc2_3" value="Overzicht orders bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc2_4" value="Navigeren naar modules" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc2_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_1" target="uc2_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc2_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_1" target="uc2_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc2_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_2" target="uc2_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc2_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_3" target="uc2_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  
  <diagram id="page3" name="UC3 - Productie Beheren">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- System boundary -->
        <mxCell id="system3" value="Productiesysteem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>
        
        <!-- Actors -->
        <mxCell id="actor3_1" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor3_2" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>
        
        <!-- Use Cases -->
        <mxCell id="uc3_1" value="Productieproces starten" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_2" value="Productiedata registreren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_3" value="Kwaliteitscontrole uitvoeren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_4" value="Productie rapportage bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Associations -->
        <mxCell id="assoc3_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_1" target="uc3_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_1" target="uc3_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_1" target="uc3_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_2" target="uc3_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page4" name="UC4 - Logistiek Beheren">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system4" value="Logistiek Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor4_1" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor4_2" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc4_1" value="Verzending plannen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_2" value="Transport organiseren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_3" value="Leveringsstatus bijwerken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_4" value="Logistiek rapportage bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc4_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_1" target="uc4_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_1" target="uc4_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_1" target="uc4_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_2" target="uc4_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page5" name="UC5 - Kwaliteitscontrole">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system5" value="Kwaliteitscontrole Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor5_1" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor5_2" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc5_1" value="Kwaliteitstest uitvoeren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_2" value="Testresultaten registreren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_3" value="Afwijkingen rapporteren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_4" value="Kwaliteitsrapport bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc5_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_1" target="uc5_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_1" target="uc5_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_1" target="uc5_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_2" target="uc5_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page6" name="UC6 - Inkoop Beheren">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system6" value="Inkoop Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor6_1" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor6_2" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc6_1" value="Leveranciers beheren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_2" value="Inkooporders plaatsen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_3" value="Leveringen controleren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_4" value="Inkoop rapportage bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc6_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_1" target="uc6_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_1" target="uc6_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_1" target="uc6_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_2" target="uc6_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page7" name="UC7 - Rapportage">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system7" value="Rapportage Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor7_1" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor7_2" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor7_3" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="510" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc7_1" value="Productie rapportage genereren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_2" value="Voorraad rapportage genereren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_3" value="Inkoop rapportage genereren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_4" value="Logistiek rapportage genereren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc7_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_1" target="uc7_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_1" target="uc7_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_2" target="uc7_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_3" target="uc7_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page8" name="UC8 - Gebruikersbeheer">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system8" value="Gebruikersbeheer Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor8_1" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor8_2" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc8_1" value="Gebruiker aanmaken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_2" value="Rechten toewijzen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_3" value="Wachtwoord wijzigen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_4" value="Profiel beheren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc8_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_1" target="uc8_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_1" target="uc8_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_2" target="uc8_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_2" target="uc8_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page9" name="UC9 - Onderhoud">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system9" value="Onderhoud Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor9_1" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor9_2" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc9_1" value="Onderhoud plannen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_2" value="Storingen melden" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_3" value="Onderhoud uitvoeren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_4" value="Onderhoudsrapport bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc9_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_2" target="uc9_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_1" target="uc9_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_1" target="uc9_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_2" target="uc9_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page10" name="UC10 - Traceerbaarheid">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system10" value="Traceerbaarheid Systeem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor10_1" value="Manager bedrijfsvoering" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor10_2" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor10_3" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="510" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc10_1" value="Batch traceren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_2" value="Producthistorie opvragen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_3" value="Leverancier traceren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_4" value="Verzending traceren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc10_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_1" target="uc10_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_1" target="uc10_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_2" target="uc10_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_3" target="uc10_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
