#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Cleaning Script for Americaps

This script provides a direct implementation of CSV cleaning using pandas and numpy.
It handles common data cleaning tasks such as:
- Handling missing values
- Converting data types
- Removing duplicates
- Fixing formatting issues
- Standardizing text data
- Handling outliers

Usage:
    python clean_csv.py input.csv [options]

Example:
    python clean_csv.py data.csv --output cleaned_data.csv --handle-outliers
"""

import pandas as pd
import numpy as np
import argparse
import os
import sys
from datetime import datetime


def main():
    """Main function to clean a CSV file."""
    # Parse command line arguments
    args = parse_arguments()

    # Set default output file if not provided
    if not args.output:
        base, ext = os.path.splitext(args.input_file)
        args.output = f"{base}_cleaned{ext}"

    # Set default report file if not provided
    if not args.report:
        base, _ = os.path.splitext(args.output)
        args.report = f"{base}_report.txt"

    # Print header
    print("=" * 80)
    print("AMERICAPS CSV DATA CLEANER")
    print("=" * 80)

    # Load the CSV file
    df_original = load_csv(args.input_file, args.delimiter, args.encoding)
    if df_original is None:
        print(f"Error: Could not load CSV file '{args.input_file}'")
        sys.exit(1)

    # Set cleaning options
    options = {
        'numeric_fill': args.numeric_fill,
        'text_fill': args.text_fill,
        'trim_text': args.trim_text,
        'lowercase_text': args.lowercase_text,
        'remove_duplicates': args.remove_duplicates,
        'handle_outliers': args.handle_outliers,
        'outlier_method': 'iqr',
        'outlier_threshold': 1.5
    }

    # Clean the data
    print("\nCleaning data...")
    df_cleaned, summary = clean_csv_data(df_original, options)

    # Save the cleaned data
    if not save_cleaned_data(df_cleaned, args.output, args.delimiter, args.encoding):
        print(f"Error: Could not save cleaned data to '{args.output}'")
        sys.exit(1)

    # Generate cleaning report
    print("\nGenerating cleaning report...")
    generate_cleaning_report(df_original, df_cleaned, summary, args.report)

    # Print summary
    print("\nCleaning summary:")
    for item in summary:
        print(f"- {item}")

    print("\nCleaning process completed successfully!")
    print(f"Cleaned data saved to: {args.output}")
    print(f"Cleaning report saved to: {args.report}")


if __name__ == "__main__":
    main()
