HANDLEIDING VOOR HET MAKEN VAN VISUALISATIES BIJ KWALITEITSMANAGEMENT ANALYSES

In deze handleiding wordt uitgelegd hoe u visualisaties kunt maken bij de kwaliteitsmanagement analyses. Voor elke kwaliteitsmanagementmethode (Six Sigma, Lean, TOC, Kaizen) wordt beschreven welke visualisaties u kunt maken en hoe deze de analyse ondersteunen.

=== SIX SIGMA VISUALISATIES ===

1. STAAFDIAGRAM: TOP 10 BATCHES MET HOOGSTE DEFECTPERCENTAGE

   Gebruik de volgende data:
   Batchnr,Defectpercentage
   7078,5.00%
   7569,5.00%
   8636,5.00%
   2329,5.00%
   7886,4.99%
   8788,4.98%
   2443,4.97%
   9787,4.96%
   9695,4.94%
   6534,4.78%

   Stappen:
   1. Maak een staafdiagram met de batchnummers op de x-as en de defectpercentages op de y-as
   2. Voeg een horizontale lijn toe bij 4.90% als streefwaarde
   3. Gee<PERSON> de grafiek de titel "Top 10 Batches met Hoogste Defectpercentage"

   Analyse:
   - Deze visualisatie toont duidelijk welke batches de hoogste defectpercentages hebben
   - De meeste batches liggen boven de streefwaarde van 4.90%
   - Er zijn vier batches met exact 5.00% defecten, wat mogelijk wijst op een systematisch probleem
   - De variatie tussen batches is relatief klein (4.78% - 5.00%), wat wijst op een stabiel maar niet-capabel proces

2. PROCES CAPABILITY VISUALISATIE:

   Gebruik de volgende data:
   - LSL (Lower Specification Limit): 0.90 kg
   - Target: 1.00 kg
   - USL (Upper Specification Limit): 1.10 kg
   - Cpk: 0.16 (Target: > 1.33)

   Stappen:
   1. Maak een normale verdeling met de target in het midden
   2. Teken verticale lijnen voor de LSL en USL
   3. Toon de huidige procesvariatie (6σ) die breder is dan de specificatiegrenzen
   4. Voeg tekst toe: "Cpk = 0.16 (Target: > 1.33)"

   Analyse:
   - Deze visualisatie toont dat de huidige procesvariatie groter is dan de toegestane specificatiegrenzen
   - De lage Cpk-waarde (0.16) geeft aan dat het proces niet capabel is
   - Er is een grote kans op producten buiten de specificatiegrenzen
   - Statistical Process Control (SPC) is nodig om de variatie te verminderen

=== LEAN VISUALISATIES ===

1. STAAFDIAGRAM: BENUTTINGSGRAAD PER VERPAKKINGSMACHINE

   Gebruik de volgende data:
   PackagingApparaat,Benuttingsgraad
   Packager 3,75.33%
   Onbekend,75.15%
   Packager 2,75.14%
   Packager 4,74.92%
   Packager 1,74.59%
   Packager 5,74.59%

   Stappen:
   1. Maak een staafdiagram met de verpakkingsmachines op de x-as en de benuttingsgraad op de y-as
   2. Voeg een horizontale lijn toe bij 85% als streefwaarde
   3. Geef de grafiek de titel "Benuttingsgraad per Verpakkingsmachine"

   Analyse:
   - Deze visualisatie toont dat alle verpakkingsmachines onder de streefwaarde van 85% benuttingsgraad liggen
   - De verschillen tussen de machines zijn klein (74.59% - 75.33%)
   - Er is potentieel voor verbetering bij alle machines
   - Value Stream Mapping kan helpen om verspilling te identificeren en de benuttingsgraad te verhogen

2. VALUE STREAM MAPPING (CONCEPTUEEL):

   Stappen:
   1. Maak een flowchart met de volgende elementen:
      - Leverancier → Voorraad → Grinding → Filling → Packaging → Klant
   2. Voeg symbolen toe voor verspilling, wachttijd, defecten en transport
   3. Gebruik verschillende kleuren voor waarde-toevoegende en niet-waarde-toevoegende activiteiten

   Analyse:
   - Deze visualisatie toont de volledige waardestroom van leverancier tot klant
   - Niet-waarde-toevoegende activiteiten (verspilling, wachttijd, defecten, transport) worden geïdentificeerd
   - Door deze activiteiten te elimineren of te verminderen kan de efficiëntie worden verhoogd
   - De voorraadniveaus kunnen worden geoptimaliseerd met Just-in-Time principes

=== TOC VISUALISATIES ===

1. STAAFDIAGRAM: PROCESTIJDEN EN BOTTLENECK IDENTIFICATIE

   Gebruik de volgende data:
   Proces,GemiddeldeProcestijd
   Grinding,0.34 uur
   Filling,0.25 uur
   Packaging,0.50 uur

   Stappen:
   1. Maak een staafdiagram met de processen op de x-as en de gemiddelde procestijd op de y-as
   2. Gebruik een afwijkende kleur voor de bottleneck (Packaging)
   3. Geef de grafiek de titel "Procestijden en Bottleneck Identificatie"

   Analyse:
   - Deze visualisatie toont duidelijk dat Packaging de bottleneck is met 0.50 uur
   - Grinding (0.34 uur) en Filling (0.25 uur) zijn sneller dan Packaging
   - De doorvoer van het hele proces wordt beperkt door de bottleneck
   - Door de bottleneck te optimaliseren kan de totale doorvoer worden verhoogd

2. DRUM-BUFFER-ROPE CONCEPT:

   Stappen:
   1. Maak een flowchart met de volgende elementen:
      - Grinding → Buffer → Filling → Buffer → Packaging → Klant
   2. Markeer Packaging als de "Drum" (trommel)
   3. Teken een "Rope" (touw) van Packaging terug naar het begin van het proces
   4. Gebruik verschillende kleuren voor de drum, buffer en rope

   Analyse:
   - Deze visualisatie toont het Drum-Buffer-Rope concept voor het beheren van de bottleneck
   - De "Drum" (Packaging) bepaalt het tempo van het hele proces
   - De "Buffer" beschermt de bottleneck tegen verstoringen
   - De "Rope" zorgt ervoor dat materiaal alleen wordt vrijgegeven wanneer de bottleneck het kan verwerken
   - Dit voorkomt overproductie en vermindert WIP (Work In Progress)

=== KAIZEN VISUALISATIES ===

1. CIRKELDIAGRAM: PANEL TEST RESULTATEN

   Gebruik de volgende data:
   Resultaat,Percentage
   Voldoet,71.00%
   Voldoet gedeeltelijk,14.56%
   Voldoet niet,14.44%

   Stappen:
   1. Maak een cirkeldiagram met de drie categorieën
   2. Gebruik verschillende kleuren voor elk segment
   3. Geef de grafiek de titel "Panel Test Resultaten"

   Analyse:
   - Deze visualisatie toont dat 71% van de producten voldoet aan de kwaliteitseisen
   - Bijna 30% voldoet niet of slechts gedeeltelijk
   - Er is ruimte voor verbetering in de productkwaliteit
   - Kaizen-events kunnen worden gericht op het verbeteren van de producten die niet volledig voldoen

2. STAAFDIAGRAM: KLANTTEVREDENHEID PER KOFFIEBOON TYPE

   Gebruik de volgende data:
   Koffieboon,Klanttevredenheid
   Liberica,3.57/5
   Robusta,3.51/5
   Arabica,3.47/5
   Excelsa,3.43/5

   Stappen:
   1. Maak een staafdiagram met de koffieboontypen op de x-as en de klanttevredenheid op de y-as
   2. Voeg een horizontale lijn toe bij 4.5/5 als streefwaarde
   3. Geef de grafiek de titel "Klanttevredenheid per Koffieboon Type"

   Analyse:
   - Deze visualisatie toont dat alle koffieboontypen onder de streefwaarde van 4.5/5 liggen
   - Liberica scoort het hoogst met 3.57/5
   - De verschillen tussen de koffieboontypen zijn klein (3.43/5 - 3.57/5)
   - Er is potentieel voor verbetering bij alle koffieboontypen
   - Kleine, frequente verbeterinitiatieven kunnen worden gericht op het verhogen van de klanttevredenheid

3. PDCA-CYCLUS:

   Stappen:
   1. Maak een circulair diagram met de vier stappen:
      - Plan
      - Do
      - Check
      - Act
   2. Gebruik verschillende kleuren voor elke stap

   Analyse:
   - Deze visualisatie toont de PDCA-cyclus voor continue verbetering
   - Plan: Identificeer problemen en plan verbeteringen
   - Do: Implementeer de verbeteringen
   - Check: Controleer of de verbeteringen effectief zijn
   - Act: Standaardiseer succesvolle verbeteringen en begin opnieuw
   - Deze cyclus kan worden gebruikt voor alle Kaizen-initiatieven

=== SAMENVATTING ===

Maak een overzichtstabel met de volgende kolommen:
- Kwaliteitsmanagement Methode
- Focus
- Belangrijkste Metric
- Huidige Waarde
- Streefwaarde
- Advies

Vul de tabel met de volgende gegevens:

1. Six Sigma
   - Focus: Defectpercentage per batch
   - Belangrijkste Metric: Process Capability Index (Cpk)
   - Huidige Waarde: 0.16
   - Streefwaarde: > 1.33
   - Advies: Implementeer Statistical Process Control (SPC) voor gewichtscontrole

2. Lean
   - Focus: Verspilling in procestijd en voorraadniveaus
   - Belangrijkste Metric: Benuttingsgraad
   - Huidige Waarde: 74.93%
   - Streefwaarde: > 85%
   - Advies: Implementeer Value Stream Mapping om verspilling te identificeren en te elimineren

3. Theory of Constraints
   - Focus: Knelpunten in het proces
   - Belangrijkste Metric: Bottleneck procestijd
   - Huidige Waarde: 0.50 uur (Packaging)
   - Streefwaarde: < 0.35 uur
   - Advies: Optimaliseer het verpakkingsproces en implementeer Drum-Buffer-Rope planning

4. Kaizen
   - Focus: Klanttevredenheid en klantretourpercentage
   - Belangrijkste Metric: Klanttevredenheid
   - Huidige Waarde: 3.49/5
   - Streefwaarde: > 4.5/5
   - Advies: Organiseer kleine, frequente verbeterinitiatieven gericht op klanttevredenheid
