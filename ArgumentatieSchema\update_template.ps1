# PowerShell script to update the argumentatieschema template with arguments

# Load the Word application
$word = New-Object -ComObject Word.Application
$word.Visible = $true

# Open the template file
$templatePath = Join-Path $PSScriptRoot "Argumentatieschema_Bol_Data_Coaching.docx"
$document = $word.Documents.Open($templatePath)

# Read the arguments from the text file
$argumentsPath = Join-Path $PSScriptRoot "argumenten.txt"
$arguments = Get-Content -Path $argumentsPath -Raw

# Add the arguments to the document
$selection = $word.Selection
$selection.EndKey(6) # Go to the end of the document
$selection.TypeText("`n`n") # Add some space
$selection.TypeText($arguments)

# Save and close the document
$document.Save()
$document.Close()
$word.Quit()

# Release COM objects
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($document) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Template updated successfully with arguments."
