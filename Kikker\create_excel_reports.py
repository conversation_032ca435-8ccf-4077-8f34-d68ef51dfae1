import os
import pandas as pd
import re
from openpyxl import Workbook
from openpyxl.drawing.image import Image
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PatternFill
from openpyxl.utils import get_column_letter

def read_csv_file(file_path):
    """Lees een CSV-bestand en geef de inhoud terug als een lijst met regels."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return lines
    except Exception as e:
        print(f"Fout bij lezen van {file_path}: {e}")
        return []

def parse_csv_content(lines):
    """Parseer de inhoud van een CSV-bestand en geef de secties terug."""
    sections = []
    current_section = {"title": "", "data": [], "type": "text"}
    
    for line in lines:
        line = line.strip()
        
        # Sla lege regels over
        if not line:
            if current_section["data"]:
                sections.append(current_section)
                current_section = {"title": "", "data": [], "type": "text"}
            continue
        
        # Controleer of dit een visualisatie-sectie is
        if line == "Visualisaties:":
            if current_section["data"]:
                sections.append(current_section)
            current_section = {"title": "Visualisaties", "data": [], "type": "visualizations"}
            continue
        
        # Verwerk visualisatie regel
        if current_section["type"] == "visualizations":
            match = re.search(r'- (.*): (.*)', line)
            if match:
                vis_title = match.group(1)
                vis_path = match.group(2)
                current_section["data"].append((vis_title, vis_path))
            continue
        
        # Controleer of dit een sectie-header is
        if line.isupper() or (not ':' in line and not '-' in line and not '\t' in line and not ',' in line):
            if current_section["data"]:
                sections.append(current_section)
            current_section = {"title": line, "data": [], "type": "header"}
            continue
        
        # Controleer of dit een tabel-header is
        if ':' in line and not '-' in line and not '\t' in line and not ',' in line:
            if current_section["data"]:
                sections.append(current_section)
            section_title = line.split(':', 1)[0].strip()
            current_section = {"title": section_title, "data": [], "type": "table"}
            continue
        
        # Verwerk tabelregel
        if '\t' in line:
            parts = line.split('\t')
            current_section["data"].append(parts)
            current_section["type"] = "table"
        elif ',' in line:
            parts = line.split(',')
            current_section["data"].append(parts)
            current_section["type"] = "table"
        elif '-' in line and not line.startswith('#'):
            parts = line.split('-', 1)
            if len(parts) == 2:
                category = parts[0].strip()
                value = parts[1].strip()
                current_section["data"].append([category, value])
                current_section["type"] = "table"
        else:
            current_section["data"].append(line)
    
    # Voeg de laatste sectie toe
    if current_section["data"]:
        sections.append(current_section)
    
    return sections

def create_excel_report(csv_file, title):
    """Maak een Excel-rapport op basis van een CSV-bestand."""
    lines = read_csv_file(csv_file)
    if not lines:
        return
    
    # Parseer de inhoud
    sections = parse_csv_content(lines)
    
    # Maak een Excel-bestand
    excel_file = csv_file.replace(".csv", ".xlsx")
    wb = Workbook()
    ws = wb.active
    ws.title = "Analyse"
    
    # Stel de titel in
    ws['A1'] = title
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:D1')
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # Stel de kolombreedtes in
    ws.column_dimensions['A'].width = 30
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 30
    ws.column_dimensions['D'].width = 20
    
    # Begin met rij 3
    row = 3
    
    # Verwerk elke sectie
    for section in sections:
        if section["type"] == "header":
            # Voeg een header toe
            ws[f'A{row}'] = section["title"]
            ws[f'A{row}'].font = Font(size=14, bold=True)
            ws.merge_cells(f'A{row}:D{row}')
            row += 2
        
        elif section["type"] == "table":
            # Voeg een tabel toe
            ws[f'A{row}'] = section["title"]
            ws[f'A{row}'].font = Font(size=12, bold=True)
            ws.merge_cells(f'A{row}:D{row}')
            row += 1
            
            # Voeg tabelkop toe als de eerste rij kolomnamen bevat
            if section["data"] and "Categorie" in section["data"][0][0]:
                for col, header in enumerate(section["data"][0], 1):
                    ws.cell(row=row, column=col).value = header
                    ws.cell(row=row, column=col).font = Font(bold=True)
                    ws.cell(row=row, column=col).fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
                row += 1
                start_idx = 1
            else:
                # Voeg standaard kolomnamen toe
                ws.cell(row=row, column=1).value = "Categorie"
                ws.cell(row=row, column=2).value = "Waarde"
                ws.cell(row=row, column=1).font = Font(bold=True)
                ws.cell(row=row, column=2).font = Font(bold=True)
                ws.cell(row=row, column=1).fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
                ws.cell(row=row, column=2).fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
                row += 1
                start_idx = 0
            
            # Voeg tabelrijen toe
            for data_row in section["data"][start_idx:]:
                for col, cell in enumerate(data_row, 1):
                    ws.cell(row=row, column=col).value = cell
                row += 1
            
            row += 1
        
        elif section["type"] == "visualizations":
            # Voeg visualisaties toe
            ws[f'A{row}'] = section["title"]
            ws[f'A{row}'].font = Font(size=12, bold=True)
            ws.merge_cells(f'A{row}:D{row}')
            row += 1
            
            for vis_title, vis_path in section["data"]:
                # Voeg visualisatie titel toe
                ws[f'A{row}'] = vis_title
                ws[f'A{row}'].font = Font(size=11, bold=True)
                ws.merge_cells(f'A{row}:D{row}')
                row += 1
                
                try:
                    # Voeg afbeelding toe
                    img = Image(vis_path)
                    # Schaal de afbeelding
                    img.width = 500
                    img.height = 300
                    # Plaats de afbeelding
                    ws.add_image(img, f'A{row}')
                    # Verhoog de rij met voldoende ruimte voor de afbeelding
                    row += 20
                except Exception as e:
                    print(f"Fout bij toevoegen van afbeelding {vis_path}: {e}")
                    ws[f'A{row}'] = f"Kon afbeelding niet laden: {vis_path}"
                    row += 1
            
            row += 1
        
        else:
            # Voeg tekst toe
            for line in section["data"]:
                ws[f'A{row}'] = line
                row += 1
            row += 1
    
    # Sla het Excel-bestand op
    try:
        wb.save(excel_file)
        print(f"Excel-rapport succesvol gemaakt: {excel_file}")
    except Exception as e:
        print(f"Fout bij opslaan van Excel-rapport {excel_file}: {e}")

def main():
    """Hoofdfunctie om Excel-rapporten te maken voor alle analyses."""
    print("Excel-rapporten maken...")
    
    # Maak Excel-rapporten voor elke analyse
    create_excel_report("Kaizen_Analyse_Clean.csv", "Kaizen Analyse")
    create_excel_report("Lean_Analyse_Clean.csv", "Lean Analyse")
    create_excel_report("SixSigma_Analyse_Clean.csv", "Six Sigma Analyse")
    create_excel_report("TOC_Analyse_Clean.csv", "Theory of Constraints Analyse")
    
    print("Alle Excel-rapporten zijn gemaakt.")

if __name__ == "__main__":
    main()
