# Customer Detail Screen - EuroCaps Order Management App

## Screen Layout (New Screen based on Use Cases)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management        [🔔] [User ▼] [⚙ Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Customer Details                    [📝 EDIT] [🛒] |
|           |                                                    |
| 🏠 Dashboard | ← Back to Customers                            |
| 👥 Customers |                                                |
| 📦 Products  | CUSTOMER INFORMATION                           |
| 📋 Orders    | +--------------------------------------------+ |
| 📊 Reports   | | Company: Bean Lovers                       | |
|              | | Contact: <PERSON>                        | |
| ⚙ Settings   | | Email: <EMAIL>           | |
| 🚪 Logout     | | Phone: +31-6-12345678                      | |
|              | | Address: Koffiestraat 123, Amsterdam       | |
|              | | Status: 🟢 Active | Type: Premium          | |
|              | | Since: 15/03/2023 | Last Order: 10/01/25   | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | ORDER HISTORY                    [View All →]  |
|              | +--------------------------------------------+ |
|              | | Order #  | Date     | Items | Total  | Status|
|              | |--------------------------------------------| |
|              | | ORD-1089 | 15/01/25 | 5     | €125.50| 🟢 Delv|
|              | | ORD-1076 | 08/01/25 | 3     | €89.25 | 🟢 Delv|
|              | | ORD-1063 | 28/12/24 | 8     | €210.00| 🟢 Delv|
|              | | ORD-1045 | 20/12/24 | 4     | €95.75 | 🟢 Delv|
|              | | ORD-1032 | 15/12/24 | 6     | €156.50| 🟢 Delv|
|              | +--------------------------------------------+ |
|              |                                                |
|              | CUSTOMER STATISTICS                            |
|              | +--------------------------------------------+ |
|              | | Total Orders: 24    | Avg Order: €142.30   | |
|              | | Total Spent: €3,415 | Last 30 days: €425   | |
|              | | Favorite: Espresso  | Payment: On Time     | |
|              | +--------------------------------------------+ |
|              |                                                |
|              | QUICK ACTIONS                                  |
|              | [🛒 NEW ORDER] [📧 SEND EMAIL] [📞 CALL]       |
|              |                                                |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Info sections: White (#ffffff)
- Status indicators:
  - Active: Green (#4caf50)
  - Inactive: Red (#f44336)
  - Pending: Orange (#ff9800)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Customer info: Arial, 14pt
- Statistics: Arial, 12pt, Bold
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo and title
   - User profile dropdown
   - Settings icon

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Customers" highlighted
   - Icons for each menu item

3. **Action Bar**
   - Back button to customer list
   - Edit customer button
   - New order button

4. **Customer Information Panel**
   - Company name and contact person
   - Contact details (email, phone, address)
   - Customer status and type
   - Registration date and last order date

5. **Order History Table**
   - Recent orders with key details
   - Status indicators with colors
   - Link to view all orders

6. **Customer Statistics**
   - Key metrics in card format
   - Total orders, spending, averages
   - Favorite products and payment status

7. **Quick Actions**
   - Common tasks as buttons
   - New order, email, call functions

## Interactions

1. **Navigation**
   - Back button returns to customer list
   - Edit button opens customer edit form
   - New order button creates order with customer pre-selected

2. **Order History**
   - Click on order number to view order details
   - "View All" link shows complete order history
   - Status indicators show current order state

3. **Quick Actions**
   - New Order: Navigate to new order screen
   - Send Email: Open email client or internal messaging
   - Call: Initiate call or show phone number

4. **Statistics**
   - Clickable metrics to drill down into details
   - Hover effects for additional information

## Use Case Alignment

This screen supports these use cases:
- **View Customer Details**: Complete customer information display
- **Customer Management**: Easy access to edit and manage customer
- **Order Creation**: Quick access to create new order for customer
- **Order History**: View customer's order patterns and history

## Accessibility Considerations
- Clear visual hierarchy with proper headings
- Consistent navigation patterns
- Color is not the only indicator of status
- Sufficient contrast for all text elements
- Keyboard navigation support

## Notes for Implementation
- Load customer data dynamically based on selected customer ID
- Implement lazy loading for order history
- Add export functionality for customer data
- Consider adding notes/comments section for customer management
- For prototype: Use mock data for customer details and order history
