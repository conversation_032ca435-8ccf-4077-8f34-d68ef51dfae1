# Customer List Screen - EuroCaps Order Management App

## Screen Layout (Improved with Clear UI Elements & Database Integration)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management    [🔔3] [<PERSON> ▼] [⚙️]      |
+---------------------------------------------------------------+
| SIDEBAR MENU  | Customers                    [➕ NEW CUSTOMER] |
|               |                                               |
| 🏠 Dashboard  | SEARCH & FILTERS (Data from Customers.xlsx)  |
| 👥 Customers  | ┌─────────────────────────────────────────────┐ |
| 📦 Products   | │🔍 Search: [Search customers...        ][🔍]│ |
| 📋 Orders     | └─────────────────────────────────────────────┘ |
| 📊 Reports    |                                               |
| 🔔 Messages   | FILTER OPTIONS:                               |
| 👤 Users      | [Customer Type ▼] [Status ▼] [Sort By ▼]     |
| ⚙️ Settings   | [📤 Export] [🔄 Refresh] [🗑️ Clear Filters]   |
| 🚪 Logout     |                                               |
|               | CUSTOMER OVERVIEW (Calculated from data)     |
|               | ┌─────────────┬─────────────┬─────────────┐   |
|               | │📊 TOTAL     │✅ ACTIVE     │🆕 NEW       │   |
|               | │CUSTOMERS    │CUSTOMERS    │THIS MONTH   │   |
|               | │    24       │    22       │     2       │   |
|               | │All Types    │Premium: 8   │Pending: 1   │   |
|               | │             │Standard: 14 │             │   |
|               | └─────────────┴─────────────┴─────────────┘   |
|               |                                               |
|               | CUSTOMER LIST (Data from Customers.xlsx)     |
|               | ┌─────────────────────────────────────────────┐ |
|               | │Company Name │Contact   │Type    │Actions   │ |
|               | ├─────────────────────────────────────────────┤ |
|               | │Coffee World │David Lee │Premium │[👁️][📝][🛒]│ |
|               | │Bean Lovers  │John Smith│Standard│[👁️][📝][🛒]│ |
|               | │Café Express │Maria G.  │Premium │[👁️][📝][🛒]│ |
|               | │Morning Brew │Sarah J.  │Standard│[👁️][📝][🛒]│ |
|               | │The Daily Cup│Robert B. │Standard│[👁️][📝][🛒]│ |
|               | │Coffee Corner│Emma W.   │Standard│[👁️][📝][🛒]│ |
|               | │Espresso E.  │Michael C.│Premium │[👁️][📝][🛒]│ |
|               | │Fresh Grounds│Lisa T.   │Standard│[👁️][📝][🛒]│ |
|               | │Java Junction│Thomas W. │Standard│[👁️][📝][🛒]│ |
|               | │Perfect Pour │Amanda B. │Premium │[👁️][📝][🛒]│ |
|               | └─────────────────────────────────────────────┘ |
|               |                                               |
|               | PAGINATION CONTROLS                           |
|               | [◀ Previous] Page 1 of 3 [Next ▶]            |
|               | Items per page: [10 ▼] | Showing 1-10 of 24  |
|               |                                               |
+---------------------------------------------------------------+
```

## PowerApps Components Specification

### **PowerApps Controls Used:**

1. **Text Input Control:**
   - **Search Text Input**: `TextInput1`
     - HintText: "Search customers..."
     - OnChange: Update gallery filter

2. **Dropdown Controls:**
   - **Customer Type Dropdown**: `Dropdown1`
     - Items: `["All Types", "Premium", "Standard", "VIP"]`
     - Default: "All Types"
   - **Status Dropdown**: `Dropdown2`
     - Items: `Distinct(Customers, Status)`
     - Default: "All"
   - **Sort By Dropdown**: `Dropdown3`
     - Items: `["Name A-Z", "Name Z-A", "Registration Date", "Customer Type"]`
     - Default: "Name A-Z"
   - **Items Per Page Dropdown**: `Dropdown4`
     - Items: `[5, 10, 25, 50, 100]`
     - Default: 10

3. **Button Controls:**
   - **New Customer Button**: `Button1` - OnSelect: `Navigate(NewCustomerScreen)`
   - **Search Button**: `Button2` - OnSelect: Apply search filter
   - **Export Button**: `Button3` - OnSelect: Export to Excel
   - **Refresh Button**: `Button4` - OnSelect: `Refresh(Customers)`
   - **Clear Filters Button**: `Button5` - OnSelect: Reset all filters

4. **Gallery Control for Customer List:**
   - **Customer Gallery**: `Gallery1` (Vertical Gallery)
     - Items: `SortByColumns(Filter(Customers, SearchCondition), SortColumn, SortOrder)`
     - Data Source: Customers.xlsx
     - Template Height: 80
     - Template contains:
       - **Company Name Label**: `Label1` - Text: `ThisItem.CompanyName`
       - **Contact Person Label**: `Label2` - Text: `ThisItem.ContactPerson`
       - **Customer Type Label**: `Label3` - Text: `ThisItem.CustomerType`
       - **View Button**: `Button6` - OnSelect: `Navigate(CustomerDetailScreen, ScreenTransition.Fade, {CustomerID: ThisItem.CustomerID})`
       - **Edit Button**: `Button7` - OnSelect: `Navigate(EditCustomerScreen, ScreenTransition.Fade, {CustomerID: ThisItem.CustomerID})`
       - **New Order Button**: `Button8` - OnSelect: `Navigate(NewOrderScreen, ScreenTransition.Fade, {CustomerID: ThisItem.CustomerID})`

5. **Label Controls for Statistics:**
   - **Total Customers Label**: `Label4` - Text: `CountRows(Customers)`
   - **Active Customers Label**: `Label5` - Text: `CountRows(Filter(Customers, Status = "Active"))`
   - **New Customers Label**: `Label6` - Text: `CountRows(Filter(Customers, DateDiff(RegistrationDate, Today(), Days) <= 30))`

6. **Rectangle Controls for Statistics Containers:**
   - **Stats Container 1**: `Rectangle1` - Contains total customers info
   - **Stats Container 2**: `Rectangle2` - Contains active customers info
   - **Stats Container 3**: `Rectangle3` - Contains new customers info

### **Data Connections:**
- **Customers Data Source**: Excel file connection to Customers.xlsx
- **Orders Data Source**: Excel file connection to Orders.xlsx (for statistics)

### **Formulas Used:**
- **Filtered Customer List**:
  ```
  SortByColumns(
    Filter(Customers,
      (TextInput1.Text = "" || CompanyName in TextInput1.Text || ContactPerson in TextInput1.Text) &&
      (Dropdown1.Selected.Value = "All Types" || CustomerType = Dropdown1.Selected.Value) &&
      (Dropdown2.Selected.Value = "All" || Status = Dropdown2.Selected.Value)
    ),
    Switch(Dropdown3.Selected.Value,
      "Name A-Z", "CompanyName", Ascending,
      "Name Z-A", "CompanyName", Descending,
      "Registration Date", "RegistrationDate", Descending,
      "Customer Type", "CustomerType", Ascending
    )
  )
  ```

- **Search Function**: `CompanyName in TextInput1.Text || ContactPerson in TextInput1.Text || Email in TextInput1.Text`

### **Navigation & Context:**
- **Customer Detail Navigation**: `Navigate(CustomerDetailScreen, ScreenTransition.Fade, {SelectedCustomer: ThisItem})`
- **Context Variables**: Use `Set()` to store filter states and selected customer data

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Table: White (#ffffff)
- Action buttons:
  - View: Blue (#4a6fa5)
  - Edit: Orange (#ff9800)
  - New Order: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Table headers: Arial, 12pt, Bold
- Table content: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Customers" highlighted
   - Icons for each menu item

3. **Action Bar**
   - Page title "Customers"
   - "New Customer" button (right-aligned)

4. **Search and Filter Section**
   - Search input with search icon
   - Filter dropdown for customer type
   - Sort dropdown (Name, Recent, etc.)
   - Reset filters button

5. **Customer Table**
   - Sortable columns
   - Alternating row colors for readability
   - Action buttons in last column:
     - View (eye icon)
     - Edit (pencil icon)
     - New Order (cart icon)

6. **Pagination**
   - Previous/Next buttons
   - Page indicator
   - Items per page selector (optional)

## Interactions

1. **Search Functionality**
   - Real-time filtering as user types
   - Search across all customer fields

2. **Filtering and Sorting**
   - Filter dropdown to show specific customer types
   - Sort dropdown to order by different fields
   - Reset button clears all filters and search

3. **Customer Actions**
   - View: Navigate to Customer Details screen
   - Edit: Navigate to Edit Customer screen
   - New Order: Navigate to New Order screen with customer pre-selected

4. **New Customer Button**
   - Navigate to Add Customer screen

5. **Pagination**
   - Previous/Next buttons navigate between pages
   - Disable Previous on first page
   - Disable Next on last page

## Accessibility Considerations
- Clear visual hierarchy
- Consistent navigation patterns
- Icon buttons have text labels on hover
- Sufficient contrast for all text elements

## Notes for Implementation
- Table should be sortable by clicking column headers
- Consider adding export functionality (Excel, CSV)
- For prototype: Use mock data for customer list
- Implement client-side pagination for the prototype
