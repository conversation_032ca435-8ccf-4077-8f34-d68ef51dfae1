{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Klantretourpercentage\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de specifieke aanpassing die is gemaakt aan het opschoningsscript om de Klantretourpercentage-waarden realistischer te maken, met behoud van enkele nullen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset waren er te veel nullen of extreem kleine waarden in de Klantretourpercentage-kolom, wat niet realistisch is voor een echte bedrijfssituatie. Het was echter wel wenselijk om een klein aantal nullen te behouden voor realisme, aangezien sommige producten inderdaad geen retours kunnen hebben.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
In de functie \i fix_unrealistic_values\i0 is speciale code toegevoegd voor de Klantretourpercentage-kolom:\par

\i # Special handling for Klantretourpercentage\par
if col == 'Klantretourpercentage':\par
    # Identify values that are too small (too many zeros)\par
    too_small_mask = (cleaned_df[col] < 0.001) & (cleaned_df[col] > 0)\par
    too_small_count = too_small_mask.sum()\par
    \par
    if too_small_count > 0:\par
        # Keep 3 rows with zeros for realism, randomly select them\par
        zero_indices = cleaned_df[cleaned_df[col] == 0].index.tolist()\par
        if len(zero_indices) > 3:\par
            # Randomly select 3 indices to keep as zero\par
            import random\par
            random.seed(42)  # For reproducibility\par
            keep_zero_indices = set(random.sample(zero_indices, 3))\par
            \par
            # For all other zeros, replace with more realistic values\par
            for idx in zero_indices:\par
                if idx not in keep_zero_indices:\par
                    # Replace with a small but realistic value (between 0.1% and 1%)\par
                    cleaned_df.loc[idx, col] = random.uniform(0.001, 0.01)\par
        \par
        # For values that are too small (but not zero), multiply by 100 to make them more realistic\par
        cleaned_df.loc[too_small_mask, col] = cleaned_df.loc[too_small_mask, col] * 100\par
        print(f"Adjusted \{too_small_count\} extremely small values in '\{col\}' to more realistic percentages")\par
        print(f"Kept 3 zero values in '\{col\}' for realism")\i0\par

Deze code doet het volgende:\par
\bullet Identificeert waarden die te klein zijn (kleiner dan 0,1% maar niet nul)\par
\bullet Behoudt precies 3 nulwaarden voor realisme, willekeurig geselecteerd\par
\bullet Vervangt alle andere nulwaarden door kleine maar realistische waarden tussen 0,1% en 1%\par
\bullet Vermenigvuldigt extreem kleine waarden (kleiner dan 0,1% maar niet nul) met 100 om ze realistischer te maken\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Klantretourpercentage-waarden als volgt aangepast:\par

\bullet 76 extreem kleine waarden zijn aangepast naar meer realistische percentages\par
\bullet Precies 3 nulwaarden zijn behouden voor realisme\par
\bullet De overige nulwaarden zijn vervangen door kleine maar realistische waarden tussen 0,1% en 1%\par

\b Statistieken van de aangepaste Klantretourpercentage-kolom:\b0\par
\bullet Minimum: 0,0% (3 waarden)\par
\bullet Maximum: 9,0%\par
\bullet Gemiddelde: 1,06%\par
\bullet Mediaan: 1,01%\par
\bullet 25e percentiel: 0,69%\par
\bullet 75e percentiel: 1,34%\par

\b Verdeling van de kleinste waarden:\b0\par
\bullet 0,0%: 3 waarden\par
\bullet 0,1%: 14 waarden\par
\bullet 0,11%: 19 waarden\par
\bullet 0,12%: 17 waarden\par
\bullet 0,13%: 16 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Klantretourpercentage-waarden zijn veel realistischer dan voorheen:\par

\bullet \b Behoud van enkele nullen:\b0 Er zijn precies 3 nulwaarden behouden, zoals gevraagd.\par
\bullet \b Meer realistische kleine waarden:\b0 De kleinste niet-nul waarde is nu 0,1%, wat veel realistischer is dan de eerdere extreem kleine waarden.\par
\bullet \b Betere verdeling:\b0 De waarden zijn nu beter verdeeld, met een gemiddelde van ongeveer 1,06% en een maximum van 9%.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De specifieke aanpassing voor de Klantretourpercentage-kolom heeft succesvol de waarden realistischer gemaakt, met behoud van enkele nullen voor realisme. De aangepaste waarden geven een veel betere weergave van wat men zou verwachten in een echte bedrijfssituatie, waar klantretours meestal een klein percentage vormen, maar zelden helemaal nul zijn voor alle producten.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op deze kolom betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de Klantretourpercentage-kolom.\i0\fs22\par
}
