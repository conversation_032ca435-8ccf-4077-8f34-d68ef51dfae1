{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management - New Order Screen", "AppName": "EuroCaps New Order", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(243, 242, 241, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366}, "Screens": [{"Name": "NewOrderScreen", "Template": "BlankScreen", "Fill": "RGBA(243, 242, 241, 1)", "Controls": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 0, "Y": 0, "Width": 1366, "Height": 60, "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "Page<PERSON><PERSON>le", "ControlType": "Label", "Text": "Create New Order", "X": 280, "Y": 80, "Width": 300, "Height": 40, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "OrderNumberLabel", "ControlType": "Label", "Text": "Order #NEW-001", "X": 1000, "Y": 80, "Width": 200, "Height": 40, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 16, "Color": "RGBA(96, 94, 92, 1)", "Align": "Align.Right"}, {"Name": "Step1Container", "ControlType": "Rectangle", "X": 280, "Y": 140, "Width": 1000, "Height": 150, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "Step1Title", "ControlType": "Label", "Text": "STEP 1: ORDER INFORMATION", "X": 300, "Y": 150, "Width": 300, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "CustomerComboBox", "ControlType": "ComboBox", "Items": "Customers", "SearchFields": "[\"CompanyName\", \"ContactPerson\"]", "DisplayFields": "[\"CompanyName\"]", "X": 320, "Y": 180, "Width": 250, "Height": 35, "HintText": "🔍 Select Customer", "OnChange": "Set(Selected<PERSON>ust<PERSON>, CustomerComboBox.Selected)"}, {"Name": "SelectedCustomerLabel", "ControlType": "Label", "Text": "If(IsBlank(SelectedCustomer), \"\", \"Selected: \" & SelectedCustomer.CompanyName & \" (\" & SelectedCustomer.ContactPerson & \")\")", "X": 590, "Y": 180, "Width": 400, "Height": 35, "Font": "Font.'Segoe UI'", "Size": 12, "Color": "RGBA(16, 124, 16, 1)"}, {"Name": "OrderDatePicker", "ControlType": "DatePicker", "X": 320, "Y": 225, "Width": 150, "Height": 35, "DefaultDate": "Today()", "Format": "DateTimeFormat.ShortDate"}, {"Name": "DeliveryDatePicker", "ControlType": "DatePicker", "X": 490, "Y": 225, "Width": 150, "Height": 35, "DefaultDate": "DateAdd(Today(), 7, Days)", "Format": "DateTimeFormat.ShortDate"}, {"Name": "PriorityDropdown", "ControlType": "Dropdown", "Items": "[\"Low\", \"Normal\", \"High\", \"Urgent\"]", "X": 660, "Y": 225, "Width": 100, "Height": 35, "Default": "Normal"}, {"Name": "NotesTextInput", "ControlType": "TextInput", "X": 780, "Y": 225, "Width": 200, "Height": 35, "Mode": "TextMode.MultiLine", "HintText": "Order notes..."}, {"Name": "Step2Container", "ControlType": "Rectangle", "X": 280, "Y": 310, "Width": 1000, "Height": 200, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "Step2Title", "ControlType": "Label", "Text": "STEP 2: ORDER ITEMS", "X": 300, "Y": 320, "Width": 300, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "OrderItemsGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "X": 300, "Y": 350, "Width": 800, "Height": 120, "Items": "CurrentOrderItems", "TemplatePadding": 5, "TemplateSize": 40, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1}, {"Name": "AddProductsButton", "ControlType": "<PERSON><PERSON>", "Text": "+ ADD MORE PRODUCTS", "X": 1120, "Y": 350, "Width": 150, "Height": 40, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(ProductSelectionScreen, ScreenTransition.Fade, {OrderMode: true})"}, {"Name": "Step3Container", "ControlType": "Rectangle", "X": 280, "Y": 530, "Width": 1000, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "Step3Title", "ControlType": "Label", "Text": "STEP 3: ORDER SUMMARY", "X": 300, "Y": 540, "Width": 300, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "ItemsCountLabel", "ControlType": "Label", "Text": "\"Total Items: \" & CountRows(CurrentOrderItems) & \" products\"", "X": 320, "Y": 570, "Width": 200, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "Quantity<PERSON><PERSON><PERSON>", "ControlType": "Label", "Text": "\"Total Quantity: \" & Sum(CurrentOrderItems, Quantity) & \" units\"", "X": 540, "Y": 570, "Width": 200, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "SubtotalLabel", "ControlType": "Label", "Text": "\"Subtotal: €\" & Text(Sum(CurrentOrderItems, Total), \"0.00\")", "X": 760, "Y": 570, "Width": 150, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "VATLabel", "ControlType": "Label", "Text": "\"VAT (21%): €\" & Text(Sum(CurrentOrderItems, Total) * 0.21, \"0.00\")", "X": 760, "Y": 590, "Width": 150, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 12}, {"Name": "TotalLabel", "ControlType": "Label", "Text": "\"Total: €\" & Text(Sum(CurrentOrderItems, Total) * 1.21, \"0.00\")", "X": 760, "Y": 610, "Width": 150, "Height": 25, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "SaveDraftButton", "ControlType": "<PERSON><PERSON>", "Text": "💾 SAVE DRAFT", "X": 280, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "SaveOrderAsDraft()"}, {"Name": "SubmitOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "📋 SUBMIT ORDER", "X": 450, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "SubmitNewOrder()"}, {"Name": "CancelButton", "ControlType": "<PERSON><PERSON>", "Text": "❌ CANCEL", "X": 620, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(164, 38, 44, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(DashboardScreen, ScreenTransition.Fade)"}]}], "DataSources": [{"Name": "Customers", "Type": "Excel", "ConnectionString": "Customers.xlsx", "Table": "Customers"}, {"Name": "Products", "Type": "Excel", "ConnectionString": "Products.xlsx", "Table": "Products"}, {"Name": "Orders", "Type": "Excel", "ConnectionString": "Orders.xlsx", "Table": "Orders"}, {"Name": "OrderItems", "Type": "Excel", "ConnectionString": "OrderItems.xlsx", "Table": "OrderItems"}, {"Name": "CurrentOrderItems", "Type": "Collection", "DefaultValue": "[]"}], "Variables": [{"Name": "SelectedCustomer", "Type": "Record", "DefaultValue": "Blank()"}, {"Name": "NewOrderID", "Type": "Text", "DefaultValue": "\"NEW-001\""}]}