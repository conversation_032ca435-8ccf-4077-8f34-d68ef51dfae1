{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Management Samenvatting\par
Analyse Productieproces Americaps Koffiecapsules\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Deze samenvatting presenteert de belangrijkste bevindingen en aanbevelingen uit de analyse van het productieproces van Americaps koffiecapsules, gebaseerd op de Kikker dataset.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Datakwaliteit en Opschoning\b0\fs22\par
De originele <PERSON> dataset bevatte 8.000 rijen en 37 kolommen, maar vertoonde verschillende datakwaliteitsproblemen die de betrouwbaarheid van de analyse konden beïnvloeden:\par
\bullet \b Ontbrekende waarden:\b0 10 kolommen bevatten missende data, waaronder Registratiedatum (5,59%), Leveranciersbeoordeling (5,04%) en CO2-Footprint (4,95%).\par
\bullet \b Onrealistische waarden:\b0 Negatieve waarden in beoordelingskolommen, onmogelijke datums zoals "31-02-2025 25:61:61", en extreme cyclustijden van "9999 hours".\par
\bullet \b Inconsistenties:\b0 Verschillende datumformaten, inconsistente apparaatnamen, en variërende eenheden.\par
\bullet \b Duplicaten:\b0 2.680 dubbele batchnummers met verschillende andere waarden.\par

\b Opschoningsaanpak:\b0\par
\bullet Ontbrekende waarden opgevuld met mediaan (numeriek) of modus (categorisch)\par
\bullet Onrealistische waarden gecorrigeerd (negatieve waarden omgezet, onmogelijke datums hersteld)\par
\bullet Inconsistenties gestandaardiseerd naar consistente notatie en formaat\par
\bullet Dubbele batchnummers voorzien van unieke identifiers\par

\b Resultaat:\b0 Een opgeschoonde dataset (8.000 rijen, 38 kolommen) zonder missende waarden, onrealistische gegevens of inconsistenties, klaar voor betrouwbare analyse.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Belangrijkste Bevindingen\b0\fs22\par
De analyse van de opgeschoonde dataset heeft de volgende kernbevindingen opgeleverd:\par

\b Kwaliteit:\b0\par
\bullet Gemiddeld defectpercentage: 2,03%\par
\bullet Packager 1 heeft het hoogste defectpercentage (2,05%)\par
\bullet Pareto-analyse toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de twee slechtst presterende machines\par

\b Efficiëntie:\b0\par
\bullet Gemiddelde cyclustijd: 4,12 uur\par
\bullet Packager 4 en Packager 1 hebben de langste cyclustijden (4,13 uur)\par
\bullet Gemiddelde benuttingsgraad: 74,93%\par
\bullet Packager 1 heeft de laagste benuttingsgraad (74,59%)\par

\b Duurzaamheid:\b0\par
\bullet De categorie 'Onbekend' heeft een extreem hoog energieverbruik (24.835,5 kWh)\par
\bullet Packager 3 heeft de laagste duurzaamheidsscore (61,1)\par

\pard\sa200\sl276\slmult1\b\fs26 3. Aanbevelingen volgens Kwaliteitsmanagementmethoden\b0\fs22\par

\b Six Sigma:\b0\par
\bullet Implementeer Statistical Process Control (SPC) bij Packager 1 en Packager 5\par
\bullet Voer een DMAIC-project uit specifiek gericht op Packager 1\par
\bullet Implementeer een Poka-Yoke systeem voor foutpreventie\par
\b Verwacht resultaat:\b0 Reductie van defectpercentage met 25% binnen 6 maanden\par

\b Lean:\b0\par
\bullet Voer Value Stream Mapping uit voor Packager 4 en Packager 1\par
\bullet Implementeer SMED om omsteltijden te verkorten\par
\bullet Standaardiseer werkprocedures en implementeer visueel management\par
\b Verwacht resultaat:\b0 Reductie van cyclustijd met 10% en omsteltijden met 30%\par

\b TOC (Theory of Constraints):\b0\par
\bullet Implementeer Drum-Buffer-Rope planning met Packager 4 als 'drum'\par
\bullet Verhoog de capaciteit van Packager 4 door gerichte investeringen\par
\bullet Herverdeel taken tussen machines om bottlenecks te ontlasten\par
\b Verwacht resultaat:\b0 Verhoogde doorstroming en benuttingsgraad van Packager 1 tot >80%\par

\b Kaizen:\b0\par
\bullet Start Kaizen-events gericht op energieverbruik\par
\bullet Implementeer een suggestiesysteem voor medewerkers\par
\bullet Voer 5S in om verspilling te elimineren\par
\b Verwacht resultaat:\b0 Energiereductie met 20-30% en verhoogde duurzaamheidsscore\par

\b TQM (Total Quality Management):\b0\par
\bullet Implementeer een integraal kwaliteitsmanagementsysteem\par
\bullet Ontwikkel een kwaliteitsdashboard voor real-time inzicht\par
\bullet Organiseer cross-functionele kwaliteitscirkels\par
\b Verwacht resultaat:\b0 Integrale kwaliteitsverbetering en 15% lagere kwaliteitskosten\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verwachte Impact op Bedrijfsresultaten\b0\fs22\par
De implementatie van deze aanbevelingen zal naar verwachting de volgende impact hebben:\par

\b Kwaliteitsverbetering:\b0\par
\bullet Reductie van defectpercentage met 25%\par
\bullet Verhoogde klanttevredenheid met 10%\par
\bullet Minder klantretouren en klachten\par

\b Efficiëntieverbetering:\b0\par
\bullet Verhoogde productiecapaciteit met 10%\par
\bullet Reductie van cyclustijd met 10%\par
\bullet Verhoogde benuttingsgraad van machines met 5-10%\par

\b Kostenbesparing:\b0\par
\bullet Reductie van afval en herbewerking met 15-20%\par
\bullet Energiebesparing van 20-30%\par
\bullet Lagere voorraadkosten door kortere doorlooptijden\par
\bullet Reductie van totale kwaliteitskosten met 15%\par

\pard\sa200\sl276\slmult1\b\fs26 5. Implementatieplan\b0\fs22\par
Om deze verbeteringen effectief te implementeren, wordt het volgende stappenplan voorgesteld:\par

\b Korte termijn (0-3 maanden):\b0\par
\bullet Start met Six Sigma DMAIC-project voor Packager 1\par
\bullet Implementeer Statistical Process Control bij Packager 1 en Packager 5\par
\bullet Voer Value Stream Mapping uit voor Packager 4 en Packager 1\par

\b Middellange termijn (3-6 maanden):\b0\par
\bullet Implementeer SMED voor Packager 4 en Packager 1\par
\bullet Start Kaizen-events gericht op energieverbruik\par
\bullet Implementeer Drum-Buffer-Rope planning\par

\b Lange termijn (6-12 maanden):\b0\par
\bullet Implementeer het integrale kwaliteitsmanagementsysteem\par
\bullet Ontwikkel de kwaliteitscultuur door training en communicatie\par
\bullet Evalueer resultaten en stel verbeterplannen bij\par

\pard\sa200\sl276\slmult1\i\fs20 Deze samenvatting is gebaseerd op een uitgebreide analyse van de Kikker dataset, waarbij gebruik is gemaakt van Python voor data-opschoning en -analyse, en Excel voor visualisatie. De volledige details zijn te vinden in de bijbehorende documenten en het Excel-bestand met visualisaties.\i0\fs22\par
}
