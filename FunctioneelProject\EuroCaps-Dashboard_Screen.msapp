{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management Dashboard", "AppName": "EuroCaps Dashboard", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(243, 242, 241, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366, "EnableInstrumentation": false}, "Header": {"DocVersion": "1.331", "MinVersionToLoad": "1.331"}, "PublishInfo": {"AppName": "EuroCaps Dashboard", "BackgroundColor": "RGBA(243, 242, 241, 1)", "IconColor": "RGBA(0, 120, 212, 1)", "IconName": "ic_fluent_chart_multiple_24_regular", "ScreenHeight": 768, "ScreenWidth": 1366}, "Screens": [{"Name": "DashboardScreen", "Template": "BlankScreen", "Fill": "RGBA(243, 242, 241, 1)", "Controls": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 0, "Y": 0, "Width": 1366, "Height": 60, "Fill": "RGBA(0, 120, 212, 1)", "BorderThickness": 0}, {"Name": "MenuIcon", "ControlType": "Icon", "Icon": "Icon.HamburgerMenu", "X": 20, "Y": 15, "Width": 30, "Height": 30, "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Set(MenuVisible, !MenuVisible)"}, {"Name": "AppTitle", "ControlType": "Label", "Text": "EuroCaps Order Management", "X": 70, "Y": 15, "Width": 300, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 16, "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "NotificationIcon", "ControlType": "Icon", "Icon": "Icon.Bell", "X": 1250, "Y": 15, "Width": 30, "Height": 30, "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(NotificationsScreen, ScreenTransition.Fade)"}, {"Name": "UserDropdown", "ControlType": "Dropdown", "Items": "[\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Logout\"]", "X": 1100, "Y": 15, "Width": 120, "Height": 30, "Default": "<PERSON>", "OnChange": "Switch(UserDropdown.Selected.Value, \"Profile\", Navigate(ProfileScreen), \"Settings\", Navigate(SettingsScreen), \"Logout\", Exit())"}, {"Name": "WelcomeLabel", "ControlType": "Label", "Text": "Dashboard - Welcome Operations Manager", "X": 280, "Y": 80, "Width": 400, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 18, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "DateFilterComboBox", "ControlType": "ComboBox", "Items": "[\"Today\", \"Last 7 Days\", \"Last 30 Days\", \"Last 90 Days\", \"This Month\", \"Last Month\"]", "X": 280, "Y": 120, "Width": 150, "Height": 35, "Default": "Last 30 Days", "HintText": "Select date range", "OnChange": "Set(DateF<PERSON>er, DateFilterComboBox.Selected.Value); Set(FilteredOrders, Filter(Orders, Switch(DateFilter, \"Today\", OrderDate = Today(), \"Last 7 Days\", OrderDate >= DateAdd(Today(), -7, Days), \"Last 30 Days\", OrderDate >= DateAdd(Today(), -30, Days), \"Last 90 Days\", OrderDate >= DateAdd(Today(), -90, Days), \"This Month\", Month(OrderDate) = Month(Today()) && Year(OrderDate) = Year(Today()), \"Last Month\", Month(OrderDate) = Month(DateAdd(Today(), -1, Months)) && Year(OrderDate) = Year(Today()))))"}, {"Name": "CustomerFilterDropdown", "ControlType": "Dropdown", "Items": "[\"All Customers\"].Con<PERSON>(Distinct(Orders, Customer))", "X": 450, "Y": 120, "Width": 150, "Height": 35, "Default": "All Customers", "OnChange": "Set(CustomerFilter, CustomerFilterDropdown.Selected.Value)"}, {"Name": "RefreshButton", "ControlType": "<PERSON><PERSON>", "Text": "🔄 Refresh", "X": 620, "Y": 120, "Width": 100, "Height": 35, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Refresh(Orders); Refresh(Customers); Refresh(Products); Notify(\"Dashboard refreshed\", NotificationType.Success)"}, {"Name": "TotalOrdersKPI", "ControlType": "<PERSON><PERSON>", "X": 280, "Y": 180, "Width": 200, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(0, 120, 212, 1)", "BorderThickness": 2, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8, "OnSelect": "Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: \"All\"})", "HoverFill": "RGBA(240, 248, 255, 1)"}, {"Name": "TotalOrdersIcon", "ControlType": "Icon", "Icon": "Icon.BarChart4", "X": 300, "Y": 200, "Width": 30, "Height": 30, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "TotalOrdersLabel", "ControlType": "Label", "Text": "TOTAL ORDERS", "X": 340, "Y": 200, "Width": 120, "Height": 20, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 12, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "TotalOrdersValue", "ControlType": "Label", "Text": "CountRows(FilteredOrders)", "X": 300, "Y": 230, "Width": 160, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(0, 120, 212, 1)", "Align": "Align.Center"}, {"Name": "TotalOrdersTrend", "ControlType": "Label", "Text": "\"+12.5% ↗️\"", "X": 300, "Y": 265, "Width": 160, "Height": 20, "Font": "Font.'Segoe UI'", "Size": 10, "Color": "RGBA(16, 124, 16, 1)", "Align": "Align.Center"}, {"Name": "ActiveOrdersKPI", "ControlType": "<PERSON><PERSON>", "X": 500, "Y": 180, "Width": 200, "Height": 120, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(255, 152, 0, 1)", "BorderThickness": 2, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8, "OnSelect": "Navigate(OrderHistoryScreen, ScreenTransition.Fade, {Filter: \"Active\"})", "HoverFill": "RGBA(255, 248, 240, 1)"}, {"Name": "ActiveOrdersIcon", "ControlType": "Icon", "Icon": "Icon.Lightning", "X": 520, "Y": 200, "Width": 30, "Height": 30, "Color": "RGBA(255, 152, 0, 1)"}, {"Name": "ActiveOrdersLabel", "ControlType": "Label", "Text": "ACTIVE ORDERS", "X": 560, "Y": 200, "Width": 120, "Height": 20, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 12, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "ActiveOrdersValue", "ControlType": "Label", "Text": "CountRows(Filter(FilteredOrders, Status in [\"New\", \"Processing\"]))", "X": 520, "Y": 230, "Width": 160, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(255, 152, 0, 1)", "Align": "Align.Center"}, {"Name": "RecentOrdersGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "X": 280, "Y": 400, "Width": 800, "Height": 250, "Items": "FirstN(SortByColumns(Filter(FilteredOrders, StatusFilter = \"All Status\" || Status = StatusFilter), \"OrderDate\", Descending), 5)", "TemplatePadding": 5, "TemplateSize": 45, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "OnSelect": "Navigate(OrderDetailScreen, ScreenTransition.Fade, {OrderID: ThisItem.OrderID})"}, {"Name": "NewOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "🛒 NEW ORDER", "X": 280, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5, "OnSelect": "Navigate(NewOrderScreen, ScreenTransition.Fade)"}, {"Name": "CustomersButton", "ControlType": "<PERSON><PERSON>", "Text": "👥 CUSTOMERS", "X": 450, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5, "OnSelect": "Navigate(CustomerListScreen, ScreenTransition.Fade)"}, {"Name": "ProductsButton", "ControlType": "<PERSON><PERSON>", "Text": "📦 PRODUCTS", "X": 620, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(164, 38, 44, 1)", "Color": "RGBA(255, 255, 255, 1)", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5, "OnSelect": "Navigate(ProductCatalogScreen, ScreenTransition.Fade)"}, {"Name": "OrderHistoryButton", "ControlType": "<PERSON><PERSON>", "Text": "📋 HISTORY", "X": 790, "Y": 680, "Width": 150, "Height": 50, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 14, "RadiusTopLeft": 5, "RadiusTopRight": 5, "RadiusBottomLeft": 5, "RadiusBottomRight": 5, "OnSelect": "Navigate(OrderHistoryScreen, ScreenTransition.Fade)"}]}], "DataSources": [{"Name": "Orders", "Type": "Excel", "ConnectionString": "Orders.xlsx", "Table": "Orders"}, {"Name": "Customers", "Type": "Excel", "ConnectionString": "Customers.xlsx", "Table": "Customers"}, {"Name": "Products", "Type": "Excel", "ConnectionString": "Products.xlsx", "Table": "Products"}], "Variables": [{"Name": "DateFilter", "Type": "Text", "DefaultValue": "Last 30 Days"}, {"Name": "CustomerFilter", "Type": "Text", "DefaultValue": "All Customers"}, {"Name": "StatusFilter", "Type": "Text", "DefaultValue": "All Status"}, {"Name": "FilteredOrders", "Type": "Table", "DefaultValue": "Orders"}, {"Name": "MenuVisible", "Type": "Boolean", "DefaultValue": false}]}