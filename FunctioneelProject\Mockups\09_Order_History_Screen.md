# Order History Screen - EuroCaps Order Management App

## Screen Layout (Improved based on Use Cases & PowerApps)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management        [🔔] [User ▼] [⚙ Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Order History                     [📤 EXPORT] [🔄] |
|           |                                                    |
| 🏠 Dashboard | [🔍 Search Orders...              ] [Search]    |
| 👥 Customers |                                                |
| 📦 Products  | FILTERS & ACTIONS:                             |
| 📋 Orders    | [📅 Last 30 Days ▼] [📊 All Status ▼] [👥 All ▼] |
| 📊 Reports   | [🔄 Refresh] [📤 Export] [🗑️ Clear Filters]     |
|              |                                                |
| ⚙ Settings   | ORDER STATISTICS                               |
| 🚪 Logout     | Total: 42 | New: 5 | Processing: 8 | Complete: 29|
|              |                                                |
|              | +--------------------------------------------+ |
|              | | Order #  | Customer   | Date   | Total | Status|
|              | |--------------------------------------------| |
|              | | ORD-1089 | Coffee W.  | 15/01  | €125  | 🟠 New|
|              | | ORD-1088 | Bean L.    | 14/01  | €89   | 🟠 New|
|              | | ORD-1087 | Café E.    | 14/01  | €156  | 🔵 Proc|
|              | | ORD-1086 | Morning B. | 13/01  | €95   | 🟣 Ship|
|              | | ORD-1085 | Coffee W.  | 12/01  | €210  | 🔵 Proc|
|              | | ORD-1084 | Daily Cup  | 10/01  | €78   | 🟢 Delv|
|              | | ORD-1083 | Coffee C.  | 09/01  | €134  | 🟢 Delv|
|              | | ORD-1082 | Espresso E.| 08/01  | €167  | 🟢 Delv|
|              | | ORD-1081 | Fresh G.   | 07/01  | €45   | 🔴 Canc|
|              | | ORD-1080 | Java J.    | 05/01  | €198  | 🟢 Delv|
|              | +--------------------------------------------+ |
|              |                                                |
|              | [◀ Previous] Page 1 of 5 [Next ▶]             |
|              | Showing 1-10 of 42 orders                     |
|              |                                                |
|              | QUICK ACTIONS                                  |
|              | [+ NEW ORDER] [📊 REPORTS] [📧 NOTIFICATIONS]  |
|              |                                                |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Table: White (#ffffff)
- Status indicators:
  - New: Orange (#ff9800)
  - Processing: Blue (#4a6fa5)
  - Shipped: Purple (#9c27b0)
  - Delivered: Green (#4caf50)
  - Cancelled: Red (#f44336)
- Action buttons:
  - View: Blue (#4a6fa5)
  - Edit: Orange (#ff9800)
- Export button: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Filter labels: Arial, 12pt, Bold
- Table headers: Arial, 12pt, Bold
- Table content: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Orders" highlighted
   - Icons for each menu item

3. **Search and Filter Section**
   - Search input with search icon
   - Date range filter dropdown
   - Status filter dropdown
   - Customer filter dropdown (searchable)

4. **Orders Table**
   - Sortable columns
   - Status indicated by color and text
   - Action buttons in last column:
     - View (eye icon)
     - Edit (pencil icon) - only for orders in "New" status

5. **Pagination**
   - Previous/Next buttons
   - Page indicator
   - Items per page selector (optional)

6. **Export Button**
   - "Export to Excel" button for data export

## Interactions

1. **Search Functionality**
   - Real-time filtering as user types
   - Search across order number, customer, and notes

2. **Filtering and Sorting**
   - Date filter with preset ranges and custom option
   - Status filter to show specific order statuses
   - Customer filter to show orders for specific customers
   - Sort by clicking column headers

3. **Order Actions**
   - View: Navigate to Order Details screen
   - Edit: Navigate to Edit Order screen (only for "New" orders)

4. **Pagination**
   - Previous/Next buttons navigate between pages
   - Disable Previous on first page
   - Disable Next on last page

5. **Export Functionality**
   - "Export to Excel" generates Excel file with current filtered orders
   - Download prompt appears automatically

## Conditional Elements

1. **Status-dependent Actions**
   - Edit button only appears for orders in "New" status
   - Status colors provide visual cues about order state

2. **Role-based Permissions**
   - Export functionality may be restricted by role
   - Filter options may vary by role

## Accessibility Considerations
- Clear visual hierarchy
- Color is not the only indicator of status
- Sufficient contrast for all text elements
- Keyboard navigation for table interaction

## Notes for Implementation
- Consider adding bulk actions (export selected, change status)
- Add date range picker for custom date filtering
- For prototype: Use mock data for order history
- Implement client-side pagination for the prototype
- Add tooltips for status indicators and action buttons
