import pandas as pd

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon statistieken van Duurzaamheid Score
print('Duurzaamheid Score statistieken:')
print(df['Duurzaamheid Score'].describe())

# Toon de verdeling van Duurzaamheid Score (frequentietabel)
print('\nFrequentietabel van Duurzaamheid Score:')
print(df['Duurzaamheid Score'].value_counts().sort_index())

# Toon de unieke waarden van Duurzaamheid Score
print('\nUnieke waarden van Duurzaamheid Score:')
print(sorted(df['Duurzaamheid Score'].unique()))

# Controleer op lege waarden
print('\nAantal lege waarden in Duurzaamheid Score:')
print(df['Duurzaamheid Score'].isna().sum())
print(df['Duurzaamheid Score'].isnull().sum())
