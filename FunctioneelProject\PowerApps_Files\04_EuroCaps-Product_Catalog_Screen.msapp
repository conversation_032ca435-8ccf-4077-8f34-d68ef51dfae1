{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management - Product Catalog Screen", "AppName": "EuroCaps Product Catalog", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(243, 242, 241, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366}, "Screens": [{"Name": "ProductCatalogScreen", "Template": "BlankScreen", "Fill": "RGBA(243, 242, 241, 1)", "Controls": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 0, "Y": 0, "Width": 1366, "Height": 60, "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "Page<PERSON><PERSON>le", "ControlType": "Label", "Text": "Product Catalog", "X": 280, "Y": 80, "Width": 250, "Height": 40, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "NewProductButton", "ControlType": "<PERSON><PERSON>", "Text": "➕ NEW PRODUCT", "X": 1100, "Y": 80, "Width": 180, "Height": 40, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)", "Visible": "User().Role = \"Admin\"", "OnSelect": "Navigate(NewProductScreen, ScreenTransition.Fade)"}, {"Name": "SearchTextInput", "ControlType": "TextInput", "X": 280, "Y": 140, "Width": 300, "Height": 35, "HintText": "Search products...", "OnChange": "Set(SearchText, SearchTextInput.Text)"}, {"Name": "CategoryDropdown", "ControlType": "Dropdown", "Items": "[\"All Categories\", \"Espresso\", \"Lungo\", \"<PERSON><PERSON>ret<PERSON>\", \"Flavored\", \"Decaf\"]", "X": 280, "Y": 190, "Width": 120, "Height": 35, "Default": "All Categories", "OnChange": "Set(CategoryFilter, CategoryDropdown.Selected.Value)"}, {"Name": "PriceRangeDropdown", "ControlType": "Dropdown", "Items": "[\"All Prices\", \"€0-10\", \"€10-15\", \"€15-20\", \"€20+\"]", "X": 420, "Y": 190, "Width": 120, "Height": 35, "Default": "All Prices", "OnChange": "Set(<PERSON><PERSON><PERSON><PERSON>, PriceRangeDropdown.Selected.Value)"}, {"Name": "StockDropdown", "ControlType": "Dropdown", "Items": "[\"All Stock\", \"In Stock (>10)\", \"Low Stock (1-10)\", \"Out of Stock (0)\"]", "X": 560, "Y": 190, "Width": 140, "Height": 35, "Default": "All Stock", "OnChange": "Set(<PERSON><PERSON><PERSON><PERSON>, StockDropdown.Selected.Value)"}, {"Name": "ViewTypeDropdown", "ControlType": "Dropdown", "Items": "[\"Grid View\", \"List View\", \"Table View\"]", "X": 720, "Y": 190, "Width": 100, "Height": 35, "Default": "Grid View", "OnChange": "Set(ViewType, ViewTypeDropdown.Selected.Value)"}, {"Name": "ItemsPerPageDropdown", "ControlType": "Dropdown", "Items": "[4, 8, 12, 16, 24]", "X": 840, "Y": 190, "Width": 80, "Height": 35, "Default": 8, "OnChange": "Set(ItemsPerPage, ItemsPerPageDropdown.Selected.Value)"}, {"Name": "ProductStatsContainer1", "ControlType": "Rectangle", "X": 280, "Y": 250, "Width": 150, "Height": 80, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(0, 120, 212, 1)", "BorderThickness": 2, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "TotalProductsLabel", "ControlType": "Label", "Text": "TOTAL PRODUCTS", "X": 290, "Y": 260, "Width": 130, "Height": 20, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 10, "Color": "RGBA(68, 68, 68, 1)", "Align": "Align.Center"}, {"Name": "TotalProductsValue", "ControlType": "Label", "Text": "CountRows(Products)", "X": 290, "Y": 280, "Width": 130, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 20, "Color": "RGBA(0, 120, 212, 1)", "Align": "Align.Center"}, {"Name": "InStockContainer", "ControlType": "Rectangle", "X": 450, "Y": 250, "Width": 150, "Height": 80, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(16, 124, 16, 1)", "BorderThickness": 2, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "InStockValue", "ControlType": "Label", "Text": "CountRows(Filter(Products, Stock > 10))", "X": 460, "Y": 280, "Width": 130, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 20, "Color": "RGBA(16, 124, 16, 1)", "Align": "Align.Center"}, {"Name": "ProductGallery", "ControlType": "Gallery", "Layout": "Layout.FlexibleHeight", "X": 280, "Y": 350, "Width": 1000, "Height": 300, "Items": "Filter(Products, (SearchText = \"\" || ProductName in SearchText || Description in SearchText || SKU in SearchText) && (CategoryFilter = \"All Categories\" || Category = CategoryFilter) && (PriceFilter = \"All Prices\" || (PriceFilter = \"€0-10\" && Price <= 10) || (PriceFilter = \"€10-15\" && Price > 10 && Price <= 15) || (PriceFilter = \"€15-20\" && Price > 15 && Price <= 20) || (PriceFilter = \"€20+\" && Price > 20)) && (StockFilter = \"All Stock\" || (StockFilter = \"In Stock (>10)\" && Stock > 10) || (StockFilter = \"Low Stock (1-10)\" && Stock > 0 && Stock <= 10) || (StockFilter = \"Out of Stock (0)\" && Stock = 0)))", "TemplatePadding": 10, "TemplateSize": 200, "WrapCount": 4, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "OnSelect": "Navigate(ProductDetailScreen, ScreenTransition.Fade, {SelectedProduct: ThisItem})"}]}], "DataSources": [{"Name": "Products", "Type": "Excel", "ConnectionString": "Products.xlsx", "Table": "Products"}, {"Name": "CurrentOrder", "Type": "Collection", "DefaultValue": "[]"}], "Variables": [{"Name": "SearchText", "Type": "Text", "DefaultValue": ""}, {"Name": "CategoryFilter", "Type": "Text", "DefaultValue": "All Categories"}, {"Name": "PriceFilter", "Type": "Text", "DefaultValue": "All Prices"}, {"Name": "StockFilter", "Type": "Text", "DefaultValue": "All Stock"}, {"Name": "ViewType", "Type": "Text", "DefaultValue": "Grid View"}, {"Name": "ItemsPerPage", "Type": "Number", "DefaultValue": 8}]}