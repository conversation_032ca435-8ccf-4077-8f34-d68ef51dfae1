{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 Koppeling Verbetermogelijkheden aan Kwaliteitsmanagementmethoden\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Op basis van de analyse van de <PERSON> dataset zijn verschillende verbetermogelijkheden geïdentificeerd. In dit document worden deze verbetermogelijkheden gekoppeld aan de verschillende kwaliteitsmanagementmethoden (Six Sigma, Lean, TOC, Kaizen, TQM) en wordt uitgelegd hoe deze verbeteringen bijdragen aan minder defecten, hogere efficiëntie en lagere kosten.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Six Sigma: Verminderen van defecten\b0\fs22\par
\b Geïdentificeerd probleem:\b0 Packager 1 heeft het hoogste defectpercentage (2,05%), gevolgd door Packager 5 (2,04%).\par
\b Aanbevelingen:\b0\par
\bullet \b Implementeer Statistical Process Control (SPC)\b0 bij Packager 1 en Packager 5 om variatie in het proces te monitoren en te reduceren. Stel controlegrenzen in en reageer direct op afwijkingen.\par
\bullet \b Voer een DMAIC-project uit\b0 (Define, Measure, Analyze, Improve, Control) specifiek gericht op Packager 1 om de oorzaken van defecten systematisch te identificeren en aan te pakken.\par
\bullet \b Implementeer een Poka-Yoke systeem\b0 (foutpreventie) bij Packager 1 om menselijke fouten te voorkomen die leiden tot defecten.\par
\b Verwachte resultaten:\b0\par
\bullet Reductie van het defectpercentage bij Packager 1 van 2,05% naar <1,5%\par
\bullet Vermindering van variatie in het productieproces\par
\bullet Kostenbesparing door minder afval en herbewerking (geschat op 15-20%)\par
\b Meetbare doelstellingen:\b0\par
\bullet Defectpercentage verlagen met minimaal 25% binnen 6 maanden\par
\bullet Sigma-niveau verhogen van huidige niveau naar minimaal 4σ\par
\bullet Procesprestatieindex (Cpk) verhogen tot >1,33\par

\pard\sa200\sl276\slmult1\b\fs26 2. Lean: Verkorten van doorlooptijden\b0\fs22\par
\b Geïdentificeerd probleem:\b0 Packager 4 en Packager 1 hebben de langste cyclustijden (4,13 uur).\par
\b Aanbevelingen:\b0\par
\bullet \b Voer Value Stream Mapping uit\b0 om niet-waardetoevoegende activiteiten (muda) in het proces van Packager 4 en Packager 1 te identificeren en te elimineren.\par
\bullet \b Implementeer SMED (Single-Minute Exchange of Die)\b0 om omsteltijden tussen verschillende productvarianten te verkorten.\par
\bullet \b Standaardiseer werkprocedures\b0 voor operators van Packager 4 en Packager 1 om variatie in uitvoering te verminderen.\par
\bullet \b Implementeer visueel management\b0 (Andon-systeem) om problemen direct zichtbaar te maken en snelle respons mogelijk te maken.\par
\b Verwachte resultaten:\b0\par
\bullet Reductie van cyclustijd met minimaal 10% (van 4,13 naar 3,72 uur)\par
\bullet Vermindering van omsteltijden met 30-50%\par
\bullet Verhoogde productiecapaciteit zonder extra investeringen\par
\bullet Lagere voorraadniveaus door kortere doorlooptijden\par
\b Meetbare doelstellingen:\b0\par
\bullet Cyclustijd verlagen met minimaal 10% binnen 3 maanden\par
\bullet Omsteltijden reduceren met 30% binnen 6 maanden\par
\bullet Overall Equipment Effectiveness (OEE) verhogen met 5 procentpunten\par

\pard\sa200\sl276\slmult1\b\fs26 3. TOC: Aanpakken van bottlenecks\b0\fs22\par
\b Geïdentificeerd probleem:\b0 Packager 4 is een bottleneck met de langste cyclustijd (4,13 uur) en Packager 1 heeft de laagste benuttingsgraad (74,59%).\par
\b Aanbevelingen:\b0\par
\bullet \b Implementeer Drum-Buffer-Rope planning\b0 waarbij Packager 4 (de bottleneck) het ritme van de productie bepaalt (drum), een buffer voor de bottleneck wordt geplaatst, en een signaleringssysteem (rope) zorgt dat materiaal op het juiste moment wordt vrijgegeven.\par
\bullet \b Verhoog de capaciteit van Packager 4\b0 door gerichte investeringen of procesoptimalisatie.\par
\bullet \b Herverdeel taken\b0 tussen Packager 4 en minder belaste machines om de bottleneck te ontlasten.\par
\bullet \b Implementeer preventief onderhoud\b0 voor Packager 4 om ongeplande stilstand te minimaliseren.\par
\b Verwachte resultaten:\b0\par
\bullet Verhoogde doorstroming in het gehele productieproces\par
\bullet Betere benutting van Packager 1 (van 74,59% naar >80%)\par
\bullet Reductie van Work-In-Progress (WIP) met 15-20%\par
\bullet Kortere levertijden door verbeterde flow\par
\b Meetbare doelstellingen:\b0\par
\bullet Benuttingsgraad van Packager 1 verhogen tot minimaal 80% binnen 4 maanden\par
\bullet Throughput (aantal geproduceerde eenheden per tijdseenheid) verhogen met 10%\par
\bullet WIP-niveaus verlagen met 15% binnen 6 maanden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Kaizen: Continue verbetering\b0\fs22\par
\b Geïdentificeerd probleem:\b0 Extreem hoog energieverbruik bij 'Onbekend' machines (24.835,5 kWh) en lage duurzaamheidsscore bij Packager 3 (61,1).\par
\b Aanbevelingen:\b0\par
\bullet \b Start Kaizen-events\b0 gericht op energieverbruik, waarbij multidisciplinaire teams korte, intensieve verbeterprojecten uitvoeren.\par
\bullet \b Implementeer een suggestiesysteem\b0 waarbij operators ideeën kunnen aandragen voor energiebesparing en duurzaamheidsverbetering.\par
\bullet \b Voer dagelijkse stand-up meetings\b0 in waarbij teams korte verbeteracties bespreken en implementeren.\par
\bullet \b Implementeer 5S\b0 (Sorteren, Schikken, Schoonmaken, Standaardiseren, Standhouden) om verspilling te elimineren en efficiëntie te verhogen.\par
\b Verwachte resultaten:\b0\par
\bullet Reductie van energieverbruik bij 'Onbekend' machines met 20-30%\par
\bullet Verhoging van duurzaamheidsscore bij Packager 3 (van 61,1 naar >70)\par
\bullet Kostenbesparing door lager energieverbruik\par
\bullet Verhoogde betrokkenheid van medewerkers bij verbeterinitiatieven\par
\b Meetbare doelstellingen:\b0\par
\bullet Energieverbruik verlagen met 20% binnen 12 maanden\par
\bullet Duurzaamheidsscore verhogen tot minimaal 70 binnen 9 maanden\par
\bullet Implementeren van minimaal 50 verbeterideeën van medewerkers per jaar\par

\pard\sa200\sl276\slmult1\b\fs26 5. TQM: Organisatiebrede kwaliteitsaanpak\b0\fs22\par
\b Geïdentificeerd probleem:\b0 Geen sterke correlaties tussen kwaliteitsaspecten, wat wijst op een gebrek aan integrale benadering.\par
\b Aanbevelingen:\b0\par
\bullet \b Implementeer een integraal kwaliteitsmanagementsysteem\b0 dat alle afdelingen en processen omvat, van inkoop tot after-sales service.\par
\bullet \b Ontwikkel een kwaliteitsdashboard\b0 dat real-time inzicht geeft in kritische kwaliteitsindicatoren voor alle belanghebbenden.\par
\bullet \b Organiseer cross-functionele kwaliteitscirkels\b0 waarbij medewerkers uit verschillende afdelingen samenwerken aan kwaliteitsverbetering.\par
\bullet \b Implementeer leverancierskwaliteitsmanagement\b0 om kwaliteitsproblemen bij de bron aan te pakken.\par
\bullet \b Ontwikkel een kwaliteitscultuur\b0 door training, communicatie en het vieren van successen.\par
\b Verwachte resultaten:\b0\par
\bullet Integrale verbetering van kwaliteit in alle processen\par
\bullet Betere samenwerking tussen afdelingen\par
\bullet Verhoogde klanttevredenheid door consistentere kwaliteit\par
\bullet Lagere totale kwaliteitskosten (preventie, inspectie, interne en externe faalkosten)\par
\b Meetbare doelstellingen:\b0\par
\bullet Totale kwaliteitskosten verlagen met 15% binnen 12 maanden\par
\bullet Klanttevredenheid verhogen met 10% binnen 12 maanden\par
\bullet 100% van de medewerkers trainen in kwaliteitsprincipes binnen 6 maanden\par

\pard\sa200\sl276\slmult1\b\fs26 Implementatieplan\b0\fs22\par
Om deze verbeteringen effectief te implementeren, wordt het volgende stappenplan voorgesteld:\par
\b 1. Korte termijn (0-3 maanden):\b0\par
\bullet Start met Six Sigma DMAIC-project voor Packager 1 om defecten te reduceren\par
\bullet Implementeer Statistical Process Control bij Packager 1 en Packager 5\par
\bullet Voer Value Stream Mapping uit voor Packager 4 en Packager 1\par
\bullet Start met 5S-implementatie in de gehele productieomgeving\par
\b 2. Middellange termijn (3-6 maanden):\b0\par
\bullet Implementeer SMED voor Packager 4 en Packager 1\par
\bullet Start Kaizen-events gericht op energieverbruik\par
\bullet Implementeer Drum-Buffer-Rope planning\par
\bullet Ontwikkel en implementeer het kwaliteitsdashboard\par
\b 3. Lange termijn (6-12 maanden):\b0\par
\bullet Implementeer het integrale kwaliteitsmanagementsysteem\par
\bullet Ontwikkel de kwaliteitscultuur door training en communicatie\par
\bullet Implementeer leverancierskwaliteitsmanagement\par
\bullet Evalueer resultaten en stel verbeterplannen bij\par

\pard\sa200\sl276\slmult1\b\fs26 Verwachte impact op bedrijfsresultaten\b0\fs22\par
De implementatie van deze verbeteringen zal naar verwachting de volgende impact hebben op de bedrijfsresultaten:\par
\b 1. Kwaliteitsverbetering:\b0\par
\bullet Reductie van defectpercentage met 25%\par
\bullet Verhoogde klanttevredenheid met 10%\par
\bullet Minder klantretouren en klachten\par
\b 2. Efficiëntieverbetering:\b0\par
\bullet Verhoogde productiecapaciteit met 10%\par
\bullet Reductie van cyclustijd met 10%\par
\bullet Verhoogde benuttingsgraad van machines met 5-10%\par
\b 3. Kostenbesparing:\b0\par
\bullet Reductie van afval en herbewerking met 15-20%\par
\bullet Energiebesparing van 20-30%\par
\bullet Lagere voorraadkosten door kortere doorlooptijden\par
\bullet Reductie van totale kwaliteitskosten met 15%\par

\pard\sa200\sl276\slmult1\i\fs20 Dit document biedt een koppeling tussen de geïdentificeerde verbetermogelijkheden en verschillende kwaliteitsmanagementmethoden. Door deze aanbevelingen te implementeren, kan het bedrijf significante verbeteringen realiseren in kwaliteit, efficiëntie en kosten.\i0\fs22\par
}
