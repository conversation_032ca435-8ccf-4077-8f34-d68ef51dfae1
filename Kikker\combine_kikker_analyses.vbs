Option Explicit

' <PERSON><PERSON><PERSON> <PERSON><PERSON>kker_cleaned.csv en analyse-bestanden te combineren in één Excel-bestand

Sub Main()
    Dim excel, wb, ws, fso
    Dim mainFile, analyseFiles, i, fileName
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Set wb = excel.Workbooks.Add
    Set ws = wb.Sheets(1)
    ws.Name = "Kikker_cleaned"
    
    ' Importeer Kikker_cleaned.csv
    mainFile = "Kikker_cleaned.csv"
    If fso.FileExists(mainFile) Then
        WScript.Echo "Importeren van " & mainFile & "..."
        ImportCSV mainFile, ws
    Else
        WScript.Echo "Het bestand " & mainFile & " bestaat niet."
    End If
    
    ' Definieer de analyse-bestanden
    analyseFiles = Array("Lean_Analyse_Clean.csv", "Kaizen_Analyse_Clean.csv", "TOC_Analyse_Clean.csv")
    
    ' Importeer elk analyse-bestand
    For i = 0 To UBound(analyseFiles)
        fileName = analyseFiles(i)
        
        If fso.FileExists(fileName) Then
            WScript.Echo "Importeren van " & fileName & "..."
            
            ' Voeg een nieuw werkblad toe
            wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
            Set ws = wb.Sheets(wb.Sheets.Count)
            ws.Name = Replace(fileName, ".csv", "")
            
            ' Importeer de CSV-data
            ImportCSV fileName, ws
        Else
            WScript.Echo "Het bestand " & fileName & " bestaat niet."
        End If
    Next
    
    ' Sla het Excel-bestand op
    wb.SaveAs "Kikker_Analyses_Gecombineerd.xlsx"
    
    WScript.Echo "Bestand opgeslagen als Kikker_Analyses_Gecombineerd.xlsx"
End Sub

' Functie om een CSV-bestand te importeren in een werkblad
Sub ImportCSV(csvFile, ws)
    Dim fso, file, line, row, col, values, separator
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(csvFile, 1)
    
    row = 1
    
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        If Trim(line) <> "" Then
            ' Bepaal het scheidingsteken
            If InStr(line, vbTab) > 0 Then
                separator = vbTab
            Else
                separator = ","
            End If
            
            ' Split de regel
            values = Split(line, separator)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                ws.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
    
    ' Pas de kolombreedtes aan
    ws.Columns.AutoFit
End Sub

' Start het script
Call Main()
