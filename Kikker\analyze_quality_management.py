import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from clean_kikker_data import clean_kikker_data

def analyze_six_sigma(df):
    """
    Six Sigma analyse: focus op defectpercentage per batch
    """
    print("\n=== SIX SIGMA ANALYSE ===")
    print("Onderzoek: Analyseer het defectpercentage per batch")

    # Defectpercentage per batch analyseren
    if 'Batchnr' in df.columns and 'Defectpercentage' in df.columns:
        # Groepeer per batch en bereken gemiddelde defectpercentage
        batch_defects = df.groupby('Batchnr')['Defectpercentage'].mean().sort_values(ascending=False)

        print(f"\nTop 10 batches met hoogste defectpercentage:")
        for batch, defect_rate in batch_defects.head(10).items():
            print(f"- Batch {batch}: {defect_rate:.2%}")

        # Bereken proces capability voor gewichtscontrole
        if 'Gewichtscontrole' in df.columns:
            weight = df['Gewichtscontrole'].dropna()

            # Stel specificatiegrenzen in (aanname: target ± 0.1 kg)
            target = weight.mean()
            lsl = target - 0.1  # Lower specification limit
            usl = target + 0.1  # Upper specification limit

            # Bereken Cp en Cpk
            sigma = weight.std()
            cp = (usl - lsl) / (6 * sigma) if sigma > 0 else 0
            cpu = (usl - target) / (3 * sigma) if sigma > 0 else 0
            cpl = (target - lsl) / (3 * sigma) if sigma > 0 else 0
            cpk = min(cpu, cpl)

            print(f"\nProces capability analyse voor gewichtscontrole:")
            print(f"- Target gewicht: {target:.2f} kg")
            print(f"- Specificatiegrenzen: {lsl:.2f} kg tot {usl:.2f} kg")
            print(f"- Process capability (Cp): {cp:.2f}")
            print(f"- Process capability index (Cpk): {cpk:.2f}")

    # Advies
    print("\nAdvies Six Sigma:")
    print("Focus op het reduceren van variatie en het minimaliseren van defecten door:")
    print("1. Implementeer Statistical Process Control (SPC) voor gewichtscontrole")
    print("2. Voer een DMAIC-verbetercyclus uit voor batches met hoog defectpercentage")
    print("3. Standaardiseer processen om variatie te verminderen")
    print("4. Train operators in Six Sigma methodologie")

def analyze_lean(df):
    """
    Lean analyse: focus op verspilling in procestijd en voorraadniveaus
    """
    print("\n=== LEAN ANALYSE ===")
    print("Onderzoek: Breng verspilling in kaart door ProcessTime en Voorraadniveaus te analyseren")

    # Analyseer cyclustijd
    if 'Cyclustijd' in df.columns:
        cycle_time = df['Cyclustijd'].dropna()

        print(f"\nCyclustijd statistieken:")
        print(f"- Gemiddelde: {cycle_time.mean():.2f} uur")
        print(f"- Mediaan: {cycle_time.median():.2f} uur")
        print(f"- Standaarddeviatie: {cycle_time.std():.2f} uur")

    # Analyseer benuttingsgraad
    if 'Benuttingsgraad' in df.columns:
        utilization = df['Benuttingsgraad'].dropna()

        print(f"\nBenuttingsgraad statistieken:")
        print(f"- Gemiddelde: {utilization.mean():.2%}")
        print(f"- Mediaan: {utilization.median():.2%}")

        # Analyseer benuttingsgraad per machine
        if 'PackagingApparaat' in df.columns:
            util_by_machine = df.groupby('PackagingApparaat')['Benuttingsgraad'].mean().sort_values()

            print(f"\nBenuttingsgraad per verpakkingsmachine:")
            for machine, util in util_by_machine.items():
                print(f"- {machine}: {util:.2%}")

    # Analyseer voorraadniveaus
    if 'Voorraadniveaus' in df.columns:
        # Voorraadniveaus zijn al numeriek door het opschoonscript
        inventory = df['Voorraadniveaus'].dropna()

        print(f"\nVoorraadniveaus statistieken:")
        print(f"- Gemiddelde: {inventory.mean():.0f} eenheden")
        print(f"- Mediaan: {inventory.median():.0f} eenheden")

    # Advies
    print("\nAdvies Lean:")
    print("Verminder verspilling door:")
    print("1. Implementeer Value Stream Mapping om niet-waarde toevoegende activiteiten te identificeren")
    print("2. Optimaliseer voorraadniveaus met Just-in-Time principes")
    print("3. Verbeter machines met lage benuttingsgraad")
    print("4. Standaardiseer werkprocedures om cyclustijd te verkorten")
    print("5. Implementeer 5S methodologie op de werkvloer")

def analyze_toc(df):
    """
    Theory of Constraints analyse: focus op knelpunten in het proces
    """
    print("\n=== THEORY OF CONSTRAINTS ANALYSE ===")
    print("Onderzoek: Identificeer knelpunten door Cyclustijd, Benuttingsgraad en Energieverbruik te vergelijken")

    # Bereken procestijden voor verschillende processtappen
    process_times = {}

    # Bereken grinding tijd - gebruik een veiligere methode voor datetime berekeningen
    if 'GrindingDatumTijdStart' in df.columns and 'GrindingDatumTijdEind' in df.columns:
        # Filter rijen waar beide datums geldig zijn
        valid_dates = df['GrindingDatumTijdStart'].notna() & df['GrindingDatumTijdEind'].notna()
        if valid_dates.any():
            # Bereken tijdverschil in uren
            df.loc[valid_dates, 'GrindingTijd'] = (
                (df.loc[valid_dates, 'GrindingDatumTijdEind'] -
                 df.loc[valid_dates, 'GrindingDatumTijdStart']).dt.total_seconds() / 3600
            )
            process_times['Grinding'] = df['GrindingTijd'].dropna()

    # Bereken filling tijd
    if 'FillingDatumTijdStart' in df.columns and 'FillingDatumTijdEind' in df.columns:
        # Filter rijen waar beide datums geldig zijn
        valid_dates = df['FillingDatumTijdStart'].notna() & df['FillingDatumTijdEind'].notna()
        if valid_dates.any():
            # Bereken tijdverschil in uren
            df.loc[valid_dates, 'FillingTijd'] = (
                (df.loc[valid_dates, 'FillingDatumTijdEind'] -
                 df.loc[valid_dates, 'FillingDatumTijdStart']).dt.total_seconds() / 3600
            )
            process_times['Filling'] = df['FillingTijd'].dropna()

    # Bereken packaging tijd
    if 'PackagingDatumTijdStart' in df.columns and 'PackagingDatumTijdEind' in df.columns:
        # Filter rijen waar beide datums geldig zijn
        valid_dates = df['PackagingDatumTijdStart'].notna() & df['PackagingDatumTijdEind'].notna()
        if valid_dates.any():
            # Bereken tijdverschil in uren
            df.loc[valid_dates, 'PackagingTijd'] = (
                (df.loc[valid_dates, 'PackagingDatumTijdEind'] -
                 df.loc[valid_dates, 'PackagingDatumTijdStart']).dt.total_seconds() / 3600
            )
            process_times['Packaging'] = df['PackagingTijd'].dropna()

    # Identificeer bottleneck
    if process_times:
        process_means = {process: times.mean() for process, times in process_times.items()}
        bottleneck = max(process_means.items(), key=lambda x: x[1])

        print("\nProcestijd analyse:")
        for process, times in process_times.items():
            print(f"- {process}: {times.mean():.2f} uur (n={len(times)})")

        print(f"\nBottleneck identificatie:")
        print(f"- Bottleneck proces: {bottleneck[0]}")
        print(f"- Gemiddelde procestijd: {bottleneck[1]:.2f} uur")

    # Analyseer energieverbruik per machine
    if 'Energieverbruik' in df.columns and 'PackagingApparaat' in df.columns:
        energy_by_machine = df.groupby('PackagingApparaat')['Energieverbruik'].mean().sort_values(ascending=False)

        print(f"\nEnergieverbruik per verpakkingsmachine:")
        for machine, energy in energy_by_machine.items():
            if not pd.isna(energy):
                print(f"- {machine}: {energy:.2f} kWh")

    # Advies
    print("\nAdvies Theory of Constraints:")
    print("Richt verbeteringen op knelpunten in het productieproces:")
    if process_times:
        print(f"1. Optimaliseer het {bottleneck[0]} proces (huidige bottleneck)")
    print("2. Implementeer Drum-Buffer-Rope planning om de bottleneck te beheren")
    print("3. Verminder energieverbruik bij machines met hoog verbruik")
    print("4. Verhoog capaciteit van de bottleneck door extra resources of verbeterde apparatuur")
    print("5. Zorg voor voldoende buffer voor de bottleneck om stilstand te voorkomen")

def analyze_kaizen(df):
    """
    Kaizen analyse: focus op continue verbetering van klanttevredenheid
    """
    print("\n=== KAIZEN ANALYSE ===")
    print("Onderzoek: Evalueer Klanttevredenheid en Klantretourpercentage en stel continue verbeteringen voor")

    # Analyseer klanttevredenheid
    if 'Klanttevredenheid' in df.columns:
        satisfaction = df['Klanttevredenheid'].dropna()

        print(f"\nKlanttevredenheid statistieken:")
        print(f"- Gemiddelde: {satisfaction.mean():.2f}/5")
        print(f"- Mediaan: {satisfaction.median():.2f}/5")

    # Analyseer klantretourpercentage
    if 'Klantretourpercentage' in df.columns:
        returns = df['Klantretourpercentage'].dropna()

        print(f"\nKlantretourpercentage statistieken:")
        print(f"- Gemiddelde: {returns.mean():.2%}")
        print(f"- Mediaan: {returns.median():.2%}")

    # Analyseer panel test resultaten
    if 'Panel Test' in df.columns:
        panel_results = df['Panel Test'].value_counts(normalize=True)

        print(f"\nPanel Test resultaten:")
        for result, percentage in panel_results.items():
            print(f"- {result}: {percentage:.2%}")

    # Analyseer klanttevredenheid per koffieboon type
    if 'Klanttevredenheid' in df.columns and 'Koffieboon' in df.columns:
        satisfaction_by_bean = df.groupby('Koffieboon')['Klanttevredenheid'].mean().sort_values()

        print(f"\nKlanttevredenheid per koffieboon type:")
        for bean, score in satisfaction_by_bean.items():
            print(f"- {bean}: {score:.2f}/5")

    # Analyseer klantretourpercentage per koffieboon type
    if 'Klantretourpercentage' in df.columns and 'Koffieboon' in df.columns:
        returns_by_bean = df.groupby('Koffieboon')['Klantretourpercentage'].mean().sort_values(ascending=False)

        print(f"\nKlantretourpercentage per koffieboon type:")
        for bean, rate in returns_by_bean.items():
            print(f"- {bean}: {rate:.2%}")

    # Advies
    print("\nAdvies Kaizen:")
    print("Organiseer kleine, frequente verbeterinitiatieven gericht op klanttevredenheid:")
    print("1. Start Kaizen-events voor producten met hoog retourpercentage")
    print("2. Implementeer PDCA-cycli (Plan-Do-Check-Act) voor continue verbetering")
    print("3. Betrek medewerkers bij het identificeren en oplossen van kwaliteitsproblemen")
    print("4. Visualiseer kwaliteitsmetrieken op de werkvloer")
    print("5. Implementeer een suggestiesysteem voor medewerkers")

def main():
    # Opschoon de data
    df = clean_kikker_data()

    if df is not None:
        # Voer analyses uit voor elke kwaliteitsmanagementmethode
        analyze_six_sigma(df)
        analyze_lean(df)
        analyze_toc(df)
        analyze_kaizen(df)

        print("\n=== SAMENVATTING ===")
        print("Voor elke kwaliteitsmanagementmethode is één onderzoeksonderwerp geanalyseerd:")
        print("1. Six Sigma: Defectpercentage per batch")
        print("2. Lean: Verspilling in procestijd en voorraadniveaus")
        print("3. TOC: Knelpunten in cyclustijd, benuttingsgraad en energieverbruik")
        print("4. Kaizen: Klanttevredenheid en klantretourpercentage")

        print("\nDeze analyses bieden inzicht in verbetermogelijkheden voor het productieproces.")

if __name__ == "__main__":
    main()
