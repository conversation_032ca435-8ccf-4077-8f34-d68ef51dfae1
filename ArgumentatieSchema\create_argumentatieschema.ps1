# PowerShell script om de argumenten uit het Vragen_en_Argumentatieschema_Bol_Data_Coaching.docx document
# te halen en in de ONV - Argumentatieschema Template (1).docx template te plaatsen

# Word COM object aanmaken
$word = New-Object -ComObject Word.Application
$word.Visible = $true

# Pad naar de bestanden
$sourcePath = Join-Path $PSScriptRoot "Vragen_en_Argumentatieschema_Bol_Data_Coaching.docx"
$templatePath = Join-Path $PSScriptRoot "ONV - Argumentatieschema Template (1).docx"
$outputPath = Join-Path $PSScriptRoot "Argumentatieschema_Bol_Data_Coaching_Nieuw.docx"

# Bronbestand openen om de argumenten te extraheren
Write-Host "Bronbestand openen: $sourcePath"
$sourceDoc = $word.Documents.Open($sourcePath)
$sourceText = $sourceDoc.Content.Text
$sourceDoc.Close()

# Template openen en opslaan als nieuw bestand
Write-Host "Template openen: $templatePath"
$templateDoc = $word.Documents.Open($templatePath)
# Sla het document op met een andere naam
$outputPath = Join-Path $PSScriptRoot "Argumentatieschema_Bol_Data_Coaching_Nieuw.docx"
$templateDoc.SaveAs2($outputPath)

# Argumenten extraheren uit de brontekst
$arguments = @()
$lines = $sourceText -split "`r`n"
$currentArgument = $null
$inArgument = $false

foreach ($line in $lines) {
    if ($line -match "^\d+\.\s+Stelling:") {
        if ($currentArgument) {
            $arguments += $currentArgument
        }
        $currentArgument = @{
            "Stelling" = $line -replace "^\d+\.\s+Stelling:\s+", ""
            "Argument" = ""
            "Onderbouwing" = ""
            "Tegenargument" = ""
            "Weerlegging" = ""
        }
        $inArgument = $true
    }
    elseif ($inArgument) {
        if ($line -match "Argument:\s+(.+)") {
            $currentArgument["Argument"] = $matches[1]
        }
        elseif ($line -match "Onderbouwing:\s+(.+)") {
            $currentArgument["Onderbouwing"] = $matches[1]
        }
        elseif ($line -match "Tegenargument:\s+(.+)") {
            $currentArgument["Tegenargument"] = $matches[1]
        }
        elseif ($line -match "Weerlegging:\s+(.+)") {
            $currentArgument["Weerlegging"] = $matches[1]
        }
    }
}

if ($currentArgument) {
    $arguments += $currentArgument
}

# Argumenten in de template plaatsen
$selection = $word.Selection

# Zoek naar de juiste plaats in de template om de argumenten toe te voegen
$selection.HomeKey(6) # Ga naar het begin van het document

# Voeg de hoofdvraag toe
$hoofdvraag = ($lines | Where-Object { $_ -match "Hoofdvraag:" }) -replace "Hoofdvraag:\s+", ""
if ($hoofdvraag) {
    $selection.TypeText("Hoofdvraag: $hoofdvraag`n`n")
}

# Voeg de argumenten toe
foreach ($arg in $arguments) {
    $selection.TypeText("Stelling: " + $arg["Stelling"] + "`n`n")
    $selection.TypeText("Argument: " + $arg["Argument"] + "`n`n")
    $selection.TypeText("Onderbouwing: " + $arg["Onderbouwing"] + "`n`n")
    $selection.TypeText("Tegenargument: " + $arg["Tegenargument"] + "`n`n")
    $selection.TypeText("Weerlegging: " + $arg["Weerlegging"] + "`n`n")
    $selection.TypeText("`n")
}

# Conclusie toevoegen (indien aanwezig)
$conclusie = ""
$inConclusie = $false
foreach ($line in $lines) {
    if ($line -match "Conclusie:") {
        $inConclusie = $true
        continue
    }
    if ($inConclusie -and $line.Trim() -ne "") {
        $conclusie += $line + " "
    }
}

if ($conclusie) {
    $selection.TypeText("Conclusie:`n" + $conclusie + "`n")
}

# Document opslaan en sluiten
$templateDoc.Save()
$templateDoc.Close()
$word.Quit()

# COM objecten vrijgeven
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($templateDoc) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Argumentatieschema is succesvol aangemaakt: $outputPath"
