<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-15T10:00:00.000Z" agent="5.0" etag="abc123" version="22.1.16" type="device" pages="10">
  <diagram id="page1" name="UC1 - Login Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- System boundary -->
        <mxCell id="system1" value="EuroCaps Order Management App" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="275" y="50" width="200" height="150" as="geometry" />
        </mxCell>
        
        <!-- Actors -->
        <mxCell id="actor1_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="125" y="70" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor1_2" value="Production Employee" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="125" y="150" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor1_3" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="525" y="70" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor1_4" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="525" y="150" width="30" height="60" as="geometry" />
        </mxCell>
        
        <!-- Use Cases -->
        <mxCell id="uc1_1" value="Login" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="315" y="100" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Associations -->
        <mxCell id="assoc1_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_1" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc1_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_2" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc1_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_3" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc1_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor1_4" target="uc1_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  
  <diagram id="page2" name="UC2 - Dashboard Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- System boundary -->
        <mxCell id="system2" value="Dashboard Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>
        
        <!-- Actors -->
        <mxCell id="actor2_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor2_2" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor2_3" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="510" width="30" height="60" as="geometry" />
        </mxCell>
        
        <!-- Use Cases -->
        <mxCell id="uc2_1" value="View Dashboard" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc2_2" value="Monitor KPIs" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc2_3" value="View Order Overview" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc2_4" value="Navigate to Modules" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Associations -->
        <mxCell id="assoc2_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_1" target="uc2_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc2_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_1" target="uc2_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc2_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_2" target="uc2_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc2_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor2_3" target="uc2_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  
  <diagram id="page3" name="UC3 - Customer List Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- System boundary -->
        <mxCell id="system3" value="Customer Management Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>
        
        <!-- Actors -->
        <mxCell id="actor3_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="actor3_2" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>
        
        <!-- Use Cases -->
        <mxCell id="uc3_1" value="View Customers" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_2" value="Search Customer" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_3" value="View Customer Details" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="uc3_4" value="Select Customer" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Associations -->
        <mxCell id="assoc3_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_1" target="uc3_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_1" target="uc3_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_2" target="uc3_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor3_2" target="uc3_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page4" name="UC4 - Product Catalog Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system4" value="Product Catalog Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor4_1" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor4_2" value="Production Employee" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc4_1" value="View Products" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_2" value="Search Product" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_3" value="View Product Details" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc4_4" value="Select Product" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc4_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_1" target="uc4_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_1" target="uc4_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_2" target="uc4_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc4_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor4_2" target="uc4_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page5" name="UC5 - New Order Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system5" value="Order Creation Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor5_1" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor5_2" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc5_1" value="Create New Order" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_2" value="Select Customer" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_3" value="Add Products" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc5_4" value="Save Order" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc5_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_1" target="uc5_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_1" target="uc5_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_2" target="uc5_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc5_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor5_2" target="uc5_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page6" name="UC6 - Order Detail Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system6" value="Order Detail Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor6_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor6_2" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc6_1" value="View Order Details" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_2" value="Modify Order" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_3" value="View Order Status" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc6_4" value="View Order Items" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc6_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_1" target="uc6_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_1" target="uc6_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_2" target="uc6_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc6_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor6_2" target="uc6_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page7" name="UC7 - Order Item Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system7" value="Order Item Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor7_1" value="Production Employee" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor7_2" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc7_1" value="View Order Items" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_2" value="Modify Item Quantity" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_3" value="Add Item" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc7_4" value="Remove Item" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc7_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_1" target="uc7_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_1" target="uc7_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_2" target="uc7_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc7_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor7_2" target="uc7_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page8" name="UC8 - Order History Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system8" value="Order History Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor8_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor8_2" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc8_1" value="View Order History" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_2" value="Filter Orders" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_3" value="Search Order" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc8_4" value="Open Order Details" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc8_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_1" target="uc8_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_1" target="uc8_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_2" target="uc8_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc8_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor8_2" target="uc8_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page9" name="UC9 - Order Confirmation Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system9" value="Order Confirmation Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor9_1" value="Purchasing Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor9_2" value="Logistics Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc9_1" value="Confirm Order" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_2" value="Send Confirmation" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_3" value="Update Order Status" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc9_4" value="View Confirmation Details" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc9_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_1" target="uc9_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_1" target="uc9_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_2" target="uc9_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor9_2" target="uc9_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>

  <diagram id="page10" name="UC10 - Settings Screen">
    <mxGraphModel dx="1158" dy="776" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- System boundary -->
        <mxCell id="system10" value="Settings Module" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="400" height="250" as="geometry" />
        </mxCell>

        <!-- Actors -->
        <mxCell id="actor10_1" value="Operations Manager" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="470" width="30" height="60" as="geometry" />
        </mxCell>

        <mxCell id="actor10_2" value="Production Employee" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="550" width="30" height="60" as="geometry" />
        </mxCell>

        <!-- Use Cases -->
        <mxCell id="uc10_1" value="View Settings" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_2" value="Modify User Profile" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_3" value="Change App Settings" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="uc10_4" value="Logout" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="520" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- Associations -->
        <mxCell id="assoc10_1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_1" target="uc10_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_1" target="uc10_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_3" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_2" target="uc10_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10_4" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="actor10_2" target="uc10_4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
