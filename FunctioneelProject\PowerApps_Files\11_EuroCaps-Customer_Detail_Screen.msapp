{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Customer Detail", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "CustomerDetailScreen", "Controls": [{"Name": "CustomerInfoContainer", "ControlType": "Rectangle", "Fill": "RGBA(255, 255, 255, 1)"}, {"Name": "CustomerOrderHistoryGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "Filter(Orders, CustomerID = SelectedCustomer.CustomerID)"}, {"Name": "EditCustomerButton", "ControlType": "<PERSON><PERSON>", "Text": "📝 EDIT", "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "NewOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "🛒 NEW ORDER", "Fill": "RGBA(16, 124, 16, 1)", "OnSelect": "Navigate(NewOrderScreen, ScreenTransition.Fade, {CustomerID: SelectedCustomer.CustomerID})"}]}], "DataSources": [{"Name": "Customers", "Type": "Excel"}, {"Name": "Orders", "Type": "Excel"}]}