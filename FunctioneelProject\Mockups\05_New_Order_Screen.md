# New Order Screen - EuroCaps Order Management App

## Screen Layout (PowerApps Components)

```
+---------------------------------------------------------------+
| [☰] EuroCaps Order Management        [🔔] [User ▼] [⚙ Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Create New Order                    Order #NEW-001 |
|           |                                                    |
| 🏠 Dashboard | STEP 1: ORDER INFORMATION                       |
| 👥 Customers | Rectangle1 (Form Container)                     |
| 📦 Products  | ┌────────────────────────────────────────────┐   |
| 📋 Orders    | │ ComboBox1: [🔍 Select Customer ▼]         │   |
| 📊 Reports   | │ Label1: Selected: Bean Lovers (<PERSON>)│   |
|              | │                                            │   |
| ⚙ Settings   | │ DatePicker1: [📅 16/01/2025] (Today)       │   |
| 🚪 Logout     | │ DatePicker2: [📅 23/01/2025] (+7 days)     │   |
|              | │                                            │   |
|              | │ Dropdown1: [Normal ▼] Label2: [Draft]     │   |
|              | │                                            │   |
|              | │ TextInput1: [Notes text area]              │   |
|              | └────────────────────────────────────────────┘   |
|              |                                                |
|              | STEP 2: ORDER ITEMS                           |
|              | Rectangle2 (Items Container)                   |
|              | ┌────────────────────────────────────────────┐   |
|              | │ Gallery1 (Vertical Gallery - Order Items)  │   |
|              | │ ┌────────────────────────────────────────┐ │   |
|              | │ │ Label3: Espresso | Label4: 2 | €25.00 │ │   |
|              | │ │ Label5: Lungo    | Label6: 1 | €14.00 │ │   |
|              | │ │ Icon1: ❌ (Remove)                     │ │   |
|              | │ └────────────────────────────────────────┘ │   |
|              | │                                            │   |
|              | │ Button1: [+ ADD MORE PRODUCTS]             │   |
|              | └────────────────────────────────────────────┘   |
|              |                                                |
|              | STEP 3: ORDER SUMMARY                         |
|              | Rectangle3 (Summary Container)                 |
|              | ┌────────────────────────────────────────────┐   |
|              | │ Label7: Total Items: 2 products            │   |
|              | │ Label8: Total Quantity: 3 units            │   |
|              | │ Label9: Subtotal: €39.00                   │   |
|              | │ Label10: VAT (21%): €8.19                  │   |
|              | │ Label11: Total: €47.19                     │   |
|              | └────────────────────────────────────────────┘   |
|              |                                                |
|              | Container1 (Action Buttons)                   |
|              | [Button2: 💾 SAVE DRAFT] [Button3: 📋 SUBMIT] |
|              | [Button4: ❌ CANCEL]                           |
|              |                                                |
+---------------------------------------------------------------+
```

## PowerApps Components Specification

### **PowerApps Controls Used:**

1. **Form Controls:**
   - **Customer Selection ComboBox**: `ComboBox1`
     - Items: `Customers`
     - SearchFields: `["CompanyName", "ContactPerson"]`
     - DisplayFields: `["CompanyName"]`
     - OnChange: Update selected customer info

2. **Date Picker Controls:**
   - **Order Date Picker**: `DatePicker1`
     - DefaultDate: `Today()`
     - Format: `DateTimeFormat.ShortDate`
   - **Delivery Date Picker**: `DatePicker2`
     - DefaultDate: `DateAdd(Today(), 7, Days)`
     - Min: `DateAdd(Today(), 3, Days)` (business rule)

3. **Dropdown Controls:**
   - **Priority Dropdown**: `Dropdown1`
     - Items: `["Low", "Normal", "High", "Urgent"]`
     - Default: "Normal"

4. **Text Input Controls:**
   - **Notes Text Input**: `TextInput1`
     - Mode: `TextMode.MultiLine`
     - HintText: "Enter order notes..."

5. **Gallery Control for Order Items:**
   - **Order Items Gallery**: `Gallery1` (Vertical Gallery)
     - Items: `CurrentOrderItems` (Collection)
     - Template Height: 60
     - Template contains:
       - **Product Name Label**: `Label3` - Text: `ThisItem.ProductName`
       - **Quantity Label**: `Label4` - Text: `ThisItem.Quantity`
       - **Price Label**: `Label5` - Text: `"€" & Text(ThisItem.Total, "0.00")`
       - **Remove Icon**: `Icon1` - Icon: `Icon.Trash`, OnSelect: Remove item

6. **Button Controls:**
   - **Add Products Button**: `Button1`
     - OnSelect: `Navigate(ProductSelectionScreen, ScreenTransition.Fade, {OrderMode: true})`
   - **Save Draft Button**: `Button2`
     - OnSelect: Save order with status "Draft"
   - **Submit Order Button**: `Button3`
     - OnSelect: Validate and submit order
   - **Cancel Button**: `Button4`
     - OnSelect: `Navigate(DashboardScreen, ScreenTransition.Fade)`

7. **Label Controls for Summary:**
   - **Items Count Label**: `Label7` - Text: `CountRows(CurrentOrderItems) & " products"`
   - **Quantity Sum Label**: `Label8` - Text: `Sum(CurrentOrderItems, Quantity) & " units"`
   - **Subtotal Label**: `Label9` - Text: `"€" & Text(Sum(CurrentOrderItems, Total), "0.00")`
   - **VAT Label**: `Label10` - Text: `"€" & Text(Sum(CurrentOrderItems, Total) * 0.21, "0.00")`
   - **Total Label**: `Label11` - Text: `"€" & Text(Sum(CurrentOrderItems, Total) * 1.21, "0.00")`

8. **Rectangle Controls for Visual Grouping:**
   - **Form Container**: `Rectangle1` - Groups order information
   - **Items Container**: `Rectangle2` - Groups order items section
   - **Summary Container**: `Rectangle3` - Groups order summary

### **Data Connections:**
- **Customers Data Source**: Excel connection to Customers.xlsx
- **Products Data Source**: Excel connection to Products.xlsx
- **Orders Data Source**: Excel connection to Orders.xlsx
- **CurrentOrderItems Collection**: Local collection for order items

### **Key Formulas:**

#### **Customer Selection Validation:**
```powerapps
If(IsBlank(ComboBox1.Selected),
   Notify("Please select a customer", NotificationType.Error),
   // Continue
)
```

#### **Order Items Collection Management:**
```powerapps
// Add item to order
Collect(CurrentOrderItems, {
    ProductID: SelectedProduct.ProductID,
    ProductName: SelectedProduct.ProductName,
    Quantity: SelectedQuantity,
    UnitPrice: SelectedProduct.Price,
    Total: SelectedProduct.Price * SelectedQuantity
})

// Remove item from order
RemoveIf(CurrentOrderItems, ProductID = ThisItem.ProductID)
```

#### **Order Submission:**
```powerapps
// Create new order
Patch(Orders, Defaults(Orders), {
    CustomerID: ComboBox1.Selected.CustomerID,
    OrderDate: DatePicker1.SelectedDate,
    DeliveryDate: DatePicker2.SelectedDate,
    Priority: Dropdown1.Selected.Value,
    Status: "New",
    Notes: TextInput1.Text,
    Subtotal: Sum(CurrentOrderItems, Total),
    VAT: Sum(CurrentOrderItems, Total) * 0.21,
    Total: Sum(CurrentOrderItems, Total) * 1.21,
    CreatedBy: User().Email
});

// Add order items
ForAll(CurrentOrderItems,
    Patch(OrderItems, Defaults(OrderItems), {
        OrderID: Last(Orders).OrderID,
        ProductID: ProductID,
        Quantity: Quantity,
        UnitPrice: UnitPrice,
        Total: Total
    })
)
```

### **Navigation & Context:**
- **Product Selection**: Pass order context to product selection screen
- **Form Validation**: Validate all required fields before submission
- **Success Navigation**: Navigate to Order Detail screen after successful creation

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Form sections: White (#ffffff)
- Primary button: Green (#4caf50)
- Secondary button: Blue (#4a6fa5)
- Cancel button: Gray (#9e9e9e)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Form labels: Arial, 12pt, Bold
- Form inputs: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Icons for each menu item

3. **Order Information Section**
   - Customer dropdown (searchable)
   - Order date picker (defaults to today)
   - Requested delivery date picker (defaults to today + 7 days)
   - Notes text area (optional)

4. **Order Items Section**
   - Initially empty with message
   - "Add Products" button
   - Once items added:
     - Table with columns: Product, Type, Size, Quantity, Price, Total, Actions
     - Remove button for each item
     - Quantity adjustment controls

5. **Order Summary Section**
   - Total items count
   - Total quantity
   - Once items added:
     - Subtotal
     - Any applicable discounts
     - Total price

6. **Action Buttons**
   - "Save as Draft" (secondary)
   - "Submit Order" (primary)
   - "Cancel" (neutral)

## Interactions

1. **Customer Selection**
   - Dropdown with search functionality
   - Shows recent customers at top
   - Option to create new customer

2. **Date Selection**
   - Calendar popup for date selection
   - Validation to ensure delivery date is after order date
   - Minimum delivery time enforcement (e.g., +3 days)

3. **Add Products**
   - Click "Add Products" to navigate to product selection screen
   - Return to this screen with selected products added to order
   - Alternative: Open product selection in modal/panel

4. **Item Management**
   - Adjust quantities with +/- buttons or direct input
   - Remove items with delete button
   - Real-time update of order summary on changes

5. **Form Actions**
   - "Save as Draft": Saves order with "draft" status
   - "Submit Order": Validates form and creates order with "new" status
   - "Cancel": Prompts confirmation if changes made, then returns to previous screen

## Validation Rules

1. **Required Fields**
   - Customer
   - Order date
   - Delivery date
   - At least one order item

2. **Business Rules**
   - Delivery date must be at least 3 business days after order date
   - Quantities must be positive integers
   - Customer must have valid status (active, approved)

## Accessibility Considerations
- Clear form structure with logical tab order
- Visible labels for all form fields
- Error messages are clear and descriptive
- Sufficient contrast for all text elements

## Notes for Implementation
- Consider adding customer quick info display after selection
- Add product search within the order screen
- For prototype: Use mock data for customers and products
- Implement basic validation for demonstration purposes
