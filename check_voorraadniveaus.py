import pandas as pd

# Laad de opgeschoonde dataset
df = pd.read_csv('Kikker_cleaned.csv')

# Toon statistieken van Voorraadniveaus
print('Voorraadniveaus statistieken:')
print(df['Voorraadniveaus'].describe())

# Toon de verdeling van Voorraadniveaus (top 20 hoogste waarden)
print('\nTop 20 hoogste Voorraadniveaus:')
print(df['Voorraadniveaus'].sort_values(ascending=False).head(20))

# Toon de verdeling van Voorraadniveaus (frequentietabel van hoogste waarden)
print('\nFrequentietabel van hoogste Voorraadniveaus:')
print(df['Voorraadniveaus'].value_counts().sort_index().tail(20))
