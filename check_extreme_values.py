import pandas as pd
import numpy as np

# Load the cleaned dataset
print("Loading cleaned data...")
df = pd.read_csv('Kikker_cleaned.csv')
print(f"Loaded data with {df.shape[0]} rows and {df.shape[1]} columns.")

# Function to identify extreme values using IQR method
def identify_extreme_values(series, column_name):
    # Try to convert to numeric
    try:
        numeric_series = pd.to_numeric(series, errors='coerce')
        
        # Skip if too many NaN values
        if numeric_series.isna().sum() > 0.5 * len(numeric_series):
            return
        
        # Calculate Q1, Q3 and IQR
        Q1 = numeric_series.quantile(0.25)
        Q3 = numeric_series.quantile(0.75)
        IQR = Q3 - Q1
        
        # Define bounds for extreme values (using 3*IQR for very extreme values)
        lower_bound = Q1 - 3 * IQR
        upper_bound = Q3 + 3 * IQR
        
        # Count extreme values
        extreme_low = (numeric_series < lower_bound).sum()
        extreme_high = (numeric_series > upper_bound).sum()
        
        # Print results if extreme values found
        if extreme_low > 0 or extreme_high > 0:
            print(f"\nColumn: {column_name}")
            print(f"  Range: {numeric_series.min()} to {numeric_series.max()}")
            print(f"  IQR bounds: {lower_bound} to {upper_bound}")
            if extreme_low > 0:
                print(f"  {extreme_low} values below {lower_bound}")
                print(f"  Examples: {numeric_series[numeric_series < lower_bound].head(3).tolist()}")
            if extreme_high > 0:
                print(f"  {extreme_high} values above {upper_bound}")
                print(f"  Examples: {numeric_series[numeric_series > upper_bound].head(3).tolist()}")
            
            # Print percentiles to understand distribution
            print(f"  Percentiles: 1%={numeric_series.quantile(0.01):.2f}, " +
                  f"5%={numeric_series.quantile(0.05):.2f}, " +
                  f"50%={numeric_series.quantile(0.5):.2f}, " +
                  f"95%={numeric_series.quantile(0.95):.2f}, " +
                  f"99%={numeric_series.quantile(0.99):.2f}")
    except:
        pass

# Check all columns for extreme values
print("\nChecking for extreme values in all columns...")
for col in df.columns:
    identify_extreme_values(df[col], col)

print("\nCheck complete.")
