{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Reports & Analytics", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "ReportsAnalyticsScreen", "Controls": [{"Name": "PeriodFilterDropdown", "ControlType": "Dropdown", "Items": "[\"Last 30 Days\", \"Last 90 Days\", \"This Month\", \"This Quarter\", \"This Year\"]"}, {"Name": "KPIContainer1", "ControlType": "Rectangle", "Fill": "RGBA(255, 255, 255, 1)"}, {"Name": "RevenueLabel", "ControlType": "Label", "Text": "\"€\" & Text(Sum(FilteredOrders, Total), \"0,000.00\")", "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(0, 120, 212, 1)"}, {"Name": "SalesChart", "ControlType": "Chart", "ChartType": "ChartType.Line", "Items": "GroupBy(FilteredOrders, \"Month\", \"Revenue\")"}, {"Name": "TopProductsGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "FirstN(SortByColumns(GroupBy(OrderItems, \"ProductID\", \"TotalSold\"), \"TotalSold\", Descending), 5)"}, {"Name": "ExportReportButton", "ControlType": "<PERSON><PERSON>", "Text": "📤 Export Report", "Fill": "RGBA(16, 124, 16, 1)"}]}], "DataSources": [{"Name": "Orders", "Type": "Excel"}, {"Name": "OrderItems", "Type": "Excel"}, {"Name": "Products", "Type": "Excel"}]}