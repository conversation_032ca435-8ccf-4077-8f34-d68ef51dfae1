{"FormatVersion": "0.24", "Properties": {"AppCreationSource": "AppFromScratch", "AppDescription": "EuroCaps Order Management - Customer List Screen", "AppName": "EuroCaps Customer List", "Author": "EuroCaps Development Team", "BackgroundColor": "RGBA(243, 242, 241, 1)", "DocumentLayoutHeight": 768, "DocumentLayoutWidth": 1366}, "Screens": [{"Name": "CustomerListScreen", "Template": "BlankScreen", "Fill": "RGBA(243, 242, 241, 1)", "Controls": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ControlType": "Rectangle", "X": 0, "Y": 0, "Width": 1366, "Height": 60, "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "Page<PERSON><PERSON>le", "ControlType": "Label", "Text": "Customers", "X": 280, "Y": 80, "Width": 200, "Height": 40, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 24, "Color": "RGBA(68, 68, 68, 1)"}, {"Name": "NewCustomerButton", "ControlType": "<PERSON><PERSON>", "Text": "➕ NEW CUSTOMER", "X": 1100, "Y": 80, "Width": 180, "Height": 40, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Navigate(NewCustomerScreen, ScreenTransition.Fade)"}, {"Name": "SearchTextInput", "ControlType": "TextInput", "X": 280, "Y": 140, "Width": 300, "Height": 35, "HintText": "Search customers...", "OnChange": "Set(SearchText, SearchTextInput.Text)"}, {"Name": "SearchButton", "ControlType": "<PERSON><PERSON>", "Text": "🔍", "X": 590, "Y": 140, "Width": 50, "Height": 35, "Fill": "RGBA(0, 120, 212, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "CustomerTypeDropdown", "ControlType": "Dropdown", "Items": "[\"All Types\", \"Premium\", \"Standard\", \"VIP\"]", "X": 280, "Y": 190, "Width": 120, "Height": 35, "Default": "All Types", "OnChange": "Set(TypeFilter, CustomerTypeDropdown.Selected.Value)"}, {"Name": "StatusDropdown", "ControlType": "Dropdown", "Items": "[\"All Status\", \"Active\", \"Inactive\", \"Pending\"]", "X": 420, "Y": 190, "Width": 120, "Height": 35, "Default": "All Status", "OnChange": "Set(<PERSON><PERSON><PERSON><PERSON>, StatusDropdown.Selected.Value)"}, {"Name": "SortDropdown", "ControlType": "Dropdown", "Items": "[\"Name A-Z\", \"Name Z-A\", \"Registration Date\", \"Customer Type\"]", "X": 560, "Y": 190, "Width": 140, "Height": 35, "Default": "Name A-Z", "OnChange": "Set(<PERSON><PERSON><PERSON><PERSON><PERSON>, SortDropdown.Selected.Value)"}, {"Name": "ExportButton", "ControlType": "<PERSON><PERSON>", "Text": "📤 Export", "X": 720, "Y": 190, "Width": 80, "Height": 35, "Fill": "RGBA(16, 124, 16, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "RefreshButton", "ControlType": "<PERSON><PERSON>", "Text": "🔄", "X": 820, "Y": 190, "Width": 50, "Height": 35, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Refresh(Customers)"}, {"Name": "ClearFiltersButton", "ControlType": "<PERSON><PERSON>", "Text": "🗑️ Clear", "X": 890, "Y": 190, "Width": 80, "Height": 35, "Fill": "RGBA(164, 38, 44, 1)", "Color": "RGBA(255, 255, 255, 1)", "OnSelect": "Reset(SearchTextInput); Reset(CustomerTypeDropdown); Reset(StatusDropdown); Reset(SortDropdown)"}, {"Name": "StatsContainer1", "ControlType": "Rectangle", "X": 280, "Y": 250, "Width": 150, "Height": 80, "Fill": "RGBA(255, 255, 255, 1)", "BorderColor": "RGBA(0, 120, 212, 1)", "BorderThickness": 2, "RadiusTopLeft": 8, "RadiusTopRight": 8, "RadiusBottomLeft": 8, "RadiusBottomRight": 8}, {"Name": "TotalCustomersLabel", "ControlType": "Label", "Text": "TOTAL CUSTOMERS", "X": 290, "Y": 260, "Width": 130, "Height": 20, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 10, "Color": "RGBA(68, 68, 68, 1)", "Align": "Align.Center"}, {"Name": "TotalCustomersValue", "ControlType": "Label", "Text": "CountRows(Customers)", "X": 290, "Y": 280, "Width": 130, "Height": 30, "Font": "Font.'Segoe UI'", "FontWeight": "FontWeight.Bold", "Size": 20, "Color": "RGBA(0, 120, 212, 1)", "Align": "Align.Center"}, {"Name": "CustomerGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "X": 280, "Y": 350, "Width": 1000, "Height": 300, "Items": "SortByColumns(Filter(Customers, (SearchText = \"\" || CompanyName in SearchText || ContactPerson in SearchText) && (TypeFilter = \"All Types\" || CustomerType = TypeFilter) && (StatusFilter = \"All Status\" || Status = StatusFilter)), Switch(SortOrder, \"Name A-Z\", \"CompanyName\", Ascending, \"Name Z-A\", \"CompanyName\", Descending, \"Registration Date\", \"RegistrationDate\", Descending, \"Customer Type\", \"CustomerType\", Ascending))", "TemplatePadding": 5, "TemplateSize": 60, "BorderColor": "RGBA(200, 200, 200, 1)", "BorderThickness": 1, "OnSelect": "Navigate(CustomerDetailScreen, ScreenTransition.Fade, {SelectedCustomer: ThisItem})"}, {"Name": "PaginationContainer", "ControlType": "Container", "X": 280, "Y": 670, "Width": 1000, "Height": 50, "LayoutDirection": "LayoutDirection.Horizontal", "LayoutJustifyContent": "LayoutJustifyContent.SpaceBetween", "LayoutAlignItems": "LayoutAlignItems.Center"}, {"Name": "PreviousButton", "ControlType": "<PERSON><PERSON>", "Text": "◀ Previous", "X": 280, "Y": 680, "Width": 100, "Height": 35, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)"}, {"Name": "PageInfo", "ControlType": "Label", "Text": "Page 1 of 3", "X": 600, "Y": 680, "Width": 100, "Height": 35, "Align": "Align.Center"}, {"Name": "NextButton", "ControlType": "<PERSON><PERSON>", "Text": "Next ▶", "X": 1180, "Y": 680, "Width": 100, "Height": 35, "Fill": "RGBA(96, 94, 92, 1)", "Color": "RGBA(255, 255, 255, 1)"}]}], "DataSources": [{"Name": "Customers", "Type": "Excel", "ConnectionString": "Customers.xlsx", "Table": "Customers"}], "Variables": [{"Name": "SearchText", "Type": "Text", "DefaultValue": ""}, {"Name": "TypeFilter", "Type": "Text", "DefaultValue": "All Types"}, {"Name": "StatusFilter", "Type": "Text", "DefaultValue": "All Status"}, {"Name": "SortOrder", "Type": "Text", "DefaultValue": "Name A-Z"}]}