{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Extreme Waarden\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanvullende aanpassingen die zijn gemaakt aan het opschoningsscript om extreme waarden zoals "10000" en "1000000" te filteren en te vervangen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
Na de eerdere aanpassingen voor placeholder-waarden (999999, 9999999, 9999999.99) waren er nog steeds extreme waarden aanwezig in verschillende kolommen, zoals "10000" in Voorraadniveaus. Deze waarden zijn technisch gezien geen placeholders, maar zijn wel onrealistisch hoog of laag voor de betreffende kolommen.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassingen aan het Script\b0\fs22\par

\b 2.1 Aanpassing voor Numerieke Kolommen\b0\par
In de functie \i fix_unrealistic_values\i0 is code toegevoegd om extreme waarden in numerieke kolommen te identificeren en te vervangen met behulp van de IQR-methode (Interquartile Range):\par

\i # Fix extreme values using IQR method\par
try:\par
    # Calculate Q1, Q3 and IQR\par
    Q1 = cleaned_df[col].quantile(0.25)\par
    Q3 = cleaned_df[col].quantile(0.75)\par
    IQR = Q3 - Q1\par
    \par
    # Define bounds for extreme values (using 3*IQR for very extreme values)\par
    lower_bound = Q1 - 3 * IQR\par
    upper_bound = Q3 + 3 * IQR\par
    \par
    # Handle specific columns with known issues\par
    if col == 'Voorraadniveaus':\par
        # For Voorraadniveaus, we know there are values like 1000000 which are unrealistic\par
        # Use a more conservative upper bound based on domain knowledge\par
        upper_bound = min(upper_bound, 1000)  # Max reasonable inventory level\par
    \par
    elif col == 'Klanttevredenheid':\par
        # For Klanttevredenheid, typically on a scale of 1-10\par
        upper_bound = min(upper_bound, 10)  # Max reasonable customer satisfaction\par
    \par
    elif col == 'Duurzaamheid Score':\par
        # For Duurzaamheid Score, typically on a scale of 0-100\par
        upper_bound = min(upper_bound, 100)  # Max reasonable sustainability score\par
    \par
    elif col == 'Leveranciersbeoordeling':\par
        # For Leveranciersbeoordeling, typically on a scale of 1-10\par
        upper_bound = min(upper_bound, 10)  # Max reasonable supplier rating\par
\par
    # Count extreme values\par
    extreme_low_count = (cleaned_df[col] < lower_bound).sum()\par
    extreme_high_count = (cleaned_df[col] > upper_bound).sum()\par
    \par
    # Replace extreme low values with lower bound\par
    if extreme_low_count > 0:\par
        cleaned_df.loc[cleaned_df[col] < lower_bound, col] = lower_bound\par
        print(f"Fixed \{extreme_low_count\} extremely low values in '\{col\}' by replacing with \{lower_bound\}")\par
    \par
    # Replace extreme high values with upper bound\par
    if extreme_high_count > 0:\par
        cleaned_df.loc[cleaned_df[col] > upper_bound, col] = upper_bound\par
        print(f"Fixed \{extreme_high_count\} extremely high values in '\{col\}' by replacing with \{upper_bound\}")\par
except Exception as e:\par
    print(f"Error fixing extreme values in '\{col\}': \{e\}")\i0\par

Deze code gebruikt de IQR-methode om extreme waarden te identificeren, waarbij waarden die meer dan 3 keer de IQR onder Q1 of boven Q3 liggen als extreem worden beschouwd. Voor specifieke kolommen worden daarnaast domeinspecifieke bovengrenzen toegepast, zoals een maximum van 10 voor Klanttevredenheid en Leveranciersbeoordeling, 100 voor Duurzaamheid Score, en 1000 voor Voorraadniveaus.\par

\b 2.2 Aanpassing voor String Kolommen\b0\par
In de functie \i standardize_inconsistencies\i0 is de lijst met te filteren waarden uitgebreid om ook extreme waarden als strings te identificeren en te vervangen:\par

\i # Fix placeholder and extreme string values\par
placeholder_strings = ['999999', '9999999', '9999999.99', '1000000', '10000']\par
extreme_strings = ['999', '9999', '10000', '100000', '1000000']\i0\par

De code is aangepast om zowel placeholder-waarden als extreme waarden te identificeren en te vervangen, waarbij dubbele telling wordt voorkomen als een waarde in beide lijsten voorkomt.\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de volgende extreme waarden geïdentificeerd en gecorrigeerd:\par

\b Voorraadniveaus:\b0\par
\bullet 119 extreem hoge waarden (waaronder 10000 en 1000000) vervangen door 1000.0\par

\b Klanttevredenheid:\b0\par
\bullet 132 extreem hoge waarden (waaronder 999) vervangen door 10\par

\b Duurzaamheid Score:\b0\par
\bullet 129 extreem hoge waarden (waaronder 999) vervangen door 100\par

\b Leveranciersbeoordeling:\b0\par
\bullet 11 extreem hoge waarden vervangen door 10\par

\b Fair-Trade Score:\b0\par
\bullet 3 extreem hoge waarden vervangen door 144.0\par

\b Cost:\b0\par
\bullet 136 extreem lage waarden vervangen door 340.01\par
\bullet 3 extreem hoge waarden vervangen door 666.28\par

\b Energieverbruik:\b0\par
\bullet 2 extreem hoge waarden vervangen door 563.0\par

\b CO2-Footprint:\b0\par
\bullet 5 extreem hoge waarden vervangen door 16.792\par

\b Gewichtscontrole:\b0\par
\bullet 4 extreem hoge waarden vervangen door 1.91\par

\b String waarden:\b0\par
\bullet 138 extreme string waarden in 'GrindingID' vervangen door 'Onbekend'\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
Na het opschonen is een verificatiescript (check_extreme_values.py) uitgevoerd om te controleren of er nog extreme waarden in de opgeschoonde dataset zitten. Het resultaat was dat de meeste extreme waarden succesvol zijn gecorrigeerd. Er zijn nog enkele waarden die als "extreem" worden geïdentificeerd, maar deze zijn nu binnen acceptabele grenzen:\par

\bullet \b Gewichtscontrole:\b0 De maximale waarde is nu 1.91, wat een redelijke waarde is voor een gewichtscontrole.\par
\bullet \b Defectpercentage:\b0 De waarden liggen nu allemaal tussen 0 en 1, wat correct is voor een percentage.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanvullende aanpassingen aan het opschoningsscript hebben succesvol de extreme waarden geïdentificeerd en vervangen door meer realistische waarden. De opgeschoonde dataset bevat nu geen onrealistische extreme waarden meer, wat de kwaliteit en bruikbaarheid van de data verder verbetert.\par

De IQR-methode in combinatie met domeinspecifieke bovengrenzen heeft goed gewerkt om extreme waarden te identificeren en te corrigeren, terwijl de normale variatie in de data behouden blijft.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassingen zijn gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en dragen bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules.\i0\fs22\par
}
