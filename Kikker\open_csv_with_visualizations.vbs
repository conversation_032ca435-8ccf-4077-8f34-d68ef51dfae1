Option Explicit

' Script om CSV-bestanden te openen en visualisaties weer te geven in Excel
' Dit script opent de CSV-bestanden en voegt de bijbehorende visualisaties toe

' Constanten
Const xlDelimited = 1
Const xlTextQualifierDoubleQuote = 1
Const xlWindows = 2
Const xlNormal = -4143
Const xlCenter = -4108
Const xlBottom = -4107

' Hoofdfunctie
Sub Main()
    Dim fso, excel, wb, ws, visualizationsFolder
    Dim csvFiles, csvFile, fileName, analysisType
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of de map "visualisaties" bestaat
    visualizationsFolder = fso.BuildPath(fso.GetAbsolutePathName("."), "visualisaties")
    If Not fso.FolderExists(visualizationsFolder) Then
        WScript.Echo "De map 'visualisaties' bestaat niet. Voer eerst het script create_management_visualizations.py uit."
        WScript.Quit
    End If
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Zoek alle CSV-bestanden met "_Analyse_Clean" in de naam
    Set csvFiles = fso.GetFolder(".").Files
    
    ' Verwerk elk CSV-bestand
    For Each csvFile In csvFiles
        fileName = fso.GetFileName(csvFile)
        
        ' Controleer of het een analyse CSV-bestand is
        If InStr(fileName, "_Analyse_Clean.csv") > 0 Then
            ' Bepaal het type analyse
            If InStr(fileName, "Kaizen") > 0 Then
                analysisType = "kaizen"
            ElseIf InStr(fileName, "Lean") > 0 Then
                analysisType = "lean"
            ElseIf InStr(fileName, "SixSigma") > 0 Then
                analysisType = "sixsigma"
            ElseIf InStr(fileName, "TOC") > 0 Then
                analysisType = "toc"
            Else
                analysisType = ""
            End If
            
            ' Als het een bekend analysetype is, verwerk het bestand
            If analysisType <> "" Then
                WScript.Echo "Verwerken van " & fileName & "..."
                
                ' Open het CSV-bestand in Excel
                Set wb = excel.Workbooks.Open(csvFile.Path)
                Set ws = wb.Sheets(1)
                
                ' Formateer het werkblad
                FormatWorksheet ws, fileName
                
                ' Voeg visualisaties toe
                AddVisualizations ws, visualizationsFolder, analysisType
                
                ' Sla het bestand op als Excel-bestand
                Dim excelFileName
                excelFileName = Replace(csvFile.Path, ".csv", "_with_visuals.xlsx")
                wb.SaveAs excelFileName, 51 ' 51 = xlsx-formaat
                
                WScript.Echo "Bestand opgeslagen als " & excelFileName
            End If
        End If
    Next
    
    ' Sluit Excel
    excel.Quit
    
    WScript.Echo "Alle bestanden zijn verwerkt."
End Sub

' Functie om een werkblad te formatteren
Sub FormatWorksheet(ws, title)
    ' Stel de titel in
    ws.Cells(1, 1).Value = title
    ws.Cells(1, 1).Font.Size = 16
    ws.Cells(1, 1).Font.Bold = True
    ws.Range(ws.Cells(1, 1), ws.Cells(1, 4)).Merge
    ws.Cells(1, 1).HorizontalAlignment = xlCenter
    
    ' Stel de kolombreedtes in
    ws.Columns(1).ColumnWidth = 30
    ws.Columns(2).ColumnWidth = 20
    
    ' Voeg een lege rij toe voor de visualisaties
    Dim lastRow
    lastRow = ws.UsedRange.Rows.Count
    ws.Cells(lastRow + 2, 1).Value = "Visualisaties"
    ws.Cells(lastRow + 2, 1).Font.Size = 14
    ws.Cells(lastRow + 2, 1).Font.Bold = True
End Sub

' Functie om visualisaties toe te voegen aan een werkblad
Sub AddVisualizations(ws, visualizationsFolder, analysisType)
    Dim fso, visualizationFiles, visualizationFile, fileName
    Dim lastRow, imgTop, imgLeft, imgWidth, imgHeight
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Zoek de laatste rij in het werkblad
    lastRow = ws.UsedRange.Rows.Count + 3 ' Voeg wat ruimte toe
    
    ' Stel de afmetingen van de afbeeldingen in
    imgWidth = 400
    imgHeight = 300
    imgLeft = 50
    
    ' Zoek alle visualisatiebestanden voor dit analysetype
    Set visualizationFiles = fso.GetFolder(visualizationsFolder).Files
    
    ' Verwerk elke visualisatie
    For Each visualizationFile In visualizationFiles
        fileName = fso.GetFileName(visualizationFile)
        
        ' Controleer of de visualisatie bij dit analysetype hoort
        If InStr(fileName, analysisType & "_") = 1 Then
            ' Voeg een titel toe voor de visualisatie
            Dim visualTitle
            visualTitle = GetVisualizationTitle(fileName)
            ws.Cells(lastRow, 1).Value = visualTitle
            ws.Cells(lastRow, 1).Font.Bold = True
            lastRow = lastRow + 1
            
            ' Bereken de positie voor de afbeelding
            imgTop = ws.Cells(lastRow, 1).Top
            
            ' Voeg de afbeelding toe
            ws.Shapes.AddPicture visualizationFile.Path, False, True, imgLeft, imgTop, imgWidth, imgHeight
            
            ' Verhoog de rij voor de volgende visualisatie
            lastRow = lastRow + imgHeight / ws.Cells(1, 1).Height + 2
        End If
    Next
End Sub

' Functie om een titel te genereren voor een visualisatie
Function GetVisualizationTitle(fileName)
    Dim title
    
    ' Verwijder de extensie
    title = Replace(fileName, ".png", "")
    
    ' Verwijder het voorvoegsel (analysetype_)
    If InStr(title, "_") > 0 Then
        title = Mid(title, InStr(title, "_") + 1)
    End If
    
    ' Vervang underscores door spaties
    title = Replace(title, "_", " ")
    
    ' Maak de eerste letter van elk woord hoofdletter
    Dim words, i, result
    words = Split(title, " ")
    result = ""
    
    For i = 0 To UBound(words)
        If Len(words(i)) > 0 Then
            result = result & UCase(Left(words(i), 1)) & Mid(words(i), 2) & " "
        End If
    Next
    
    ' Verwijder eventuele extra spaties aan het einde
    result = Trim(result)
    
    ' Voeg specifieke titels toe op basis van de bestandsnaam
    If InStr(fileName, "panel_test_pie") > 0 Then
        result = "Panel Test Resultaten"
    ElseIf InStr(fileName, "klanttevredenheid_bar") > 0 Then
        result = "Klanttevredenheid per Koffieboon Type"
    ElseIf InStr(fileName, "klantretour_bar") > 0 Then
        result = "Klantretourpercentage per Koffieboon Type"
    ElseIf InStr(fileName, "benutting_bar") > 0 Then
        result = "Benuttingsgraad per Verpakkingsmachine"
    ElseIf InStr(fileName, "voorraad_bar") > 0 Then
        result = "Voorraadniveaus Statistieken"
    ElseIf InStr(fileName, "batch_defect_bar") > 0 Then
        result = "Top 10 Batches met Hoogste Defectpercentage"
    ElseIf InStr(fileName, "capability_bar") > 0 Then
        result = "Process Capability Analyse"
    ElseIf InStr(fileName, "procestijd_bar") > 0 Then
        result = "Procestijd Analyse"
    ElseIf InStr(fileName, "energie_bar") > 0 Then
        result = "Energieverbruik per Verpakkingsmachine"
    End If
    
    GetVisualizationTitle = result
End Function

' Start het script
Call Main()
