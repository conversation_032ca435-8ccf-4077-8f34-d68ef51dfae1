{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Order Items", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "OrderItemsScreen", "Controls": [{"Name": "ProductGallery", "ControlType": "Gallery", "Layout": "Layout.FlexibleHeight", "Items": "Filter(Products, Stock > 0)", "WrapCount": 4, "TemplateSize": 200}, {"Name": "CurrentItemsGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "CurrentOrderItems"}, {"Name": "AddToOrderButton", "ControlType": "<PERSON><PERSON>", "Text": "🛒 Add", "OnSelect": "Collect(CurrentOrderItems, {ProductID: ThisItem.ProductID, Quantity: QuantityDropdown.Selected.Value})"}]}], "DataSources": [{"Name": "Products", "Type": "Excel"}, {"Name": "CurrentOrderItems", "Type": "Collection"}]}