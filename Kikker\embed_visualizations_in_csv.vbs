Option Explicit

' Script om visualisaties direct in CSV-bestanden te integreren
' Dit script maakt nieuwe CSV-bestanden met geïntegreerde visualisaties

' Hoofdfunctie
Sub Main()
    Dim fso, visualizationsFolder
    Dim csvFiles, csvFile, fileName, analysisType
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of de map "visualisaties" bestaat
    visualizationsFolder = fso.BuildPath(fso.GetAbsolutePathName("."), "visualisaties")
    If Not fso.FolderExists(visualizationsFolder) Then
        WScript.Echo "De map 'visualisaties' bestaat niet. Voer eerst het script create_management_visualizations.py uit."
        WScript.Quit
    End If
    
    ' Zoek alle CSV-bestanden met "_Analyse_Clean" in de naam
    Set csvFiles = fso.GetFolder(".").Files
    
    ' Verwerk elk CSV-bestand
    For Each csvFile In csvFiles
        fileName = fso.GetFileName(csvFile)
        
        ' Controleer of het een analyse CSV-bestand is
        If InStr(fileName, "_Analyse_Clean.csv") > 0 Then
            ' Bepaal het type analyse
            If InStr(fileName, "Kaizen") > 0 Then
                analysisType = "kaizen"
            ElseIf InStr(fileName, "Lean") > 0 Then
                analysisType = "lean"
            ElseIf InStr(fileName, "SixSigma") > 0 Then
                analysisType = "sixsigma"
            ElseIf InStr(fileName, "TOC") > 0 Then
                analysisType = "toc"
            Else
                analysisType = ""
            End If
            
            ' Als het een bekend analysetype is, verwerk het bestand
            If analysisType <> "" Then
                WScript.Echo "Verwerken van " & fileName & "..."
                
                ' Lees de inhoud van het CSV-bestand
                Dim content
                content = ReadFile(csvFile.Path)
                
                ' Voeg visualisaties toe aan de inhoud
                content = AddVisualizationsToContent(content, visualizationsFolder, analysisType)
                
                ' Schrijf de nieuwe inhoud naar een nieuw bestand
                Dim newFileName
                newFileName = Replace(csvFile.Path, ".csv", "_with_visuals.csv")
                WriteFile newFileName, content
                
                WScript.Echo "Bestand opgeslagen als " & newFileName
            End If
        End If
    Next
    
    WScript.Echo "Alle bestanden zijn verwerkt."
End Sub

' Functie om een bestand te lezen
Function ReadFile(filePath)
    Dim fso, file, content
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    content = file.ReadAll
    file.Close
    
    ReadFile = content
End Function

' Functie om een bestand te schrijven
Sub WriteFile(filePath, content)
    Dim fso, file
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.CreateTextFile(filePath, True) ' True = Overschrijven als het bestand al bestaat
    file.Write content
    file.Close
End Sub

' Functie om visualisaties toe te voegen aan de inhoud
Function AddVisualizationsToContent(content, visualizationsFolder, analysisType)
    Dim fso, visualizationFiles, visualizationFile, fileName
    Dim result, visualizationsSection
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Controleer of de inhoud al een visualisatiesectie heeft
    If InStr(content, "Visualisaties:") > 0 Then
        ' Verwijder de bestaande visualisatiesectie
        content = Left(content, InStr(content, "Visualisaties:") - 1)
    End If
    
    ' Voeg een lege regel toe als die er nog niet is
    If Right(content, 2) <> vbCrLf & vbCrLf Then
        If Right(content, 1) <> vbCrLf Then
            content = content & vbCrLf
        End If
        content = content & vbCrLf
    End If
    
    ' Begin de visualisatiesectie
    visualizationsSection = "Visualisaties:" & vbCrLf
    
    ' Zoek alle visualisatiebestanden voor dit analysetype
    Set visualizationFiles = fso.GetFolder(visualizationsFolder).Files
    
    ' Verwerk elke visualisatie
    For Each visualizationFile In visualizationFiles
        fileName = fso.GetFileName(visualizationFile)
        
        ' Controleer of de visualisatie bij dit analysetype hoort
        If InStr(fileName, analysisType & "_") = 1 Then
            ' Voeg een regel toe voor de visualisatie
            Dim visualTitle
            visualTitle = GetVisualizationTitle(fileName)
            visualizationsSection = visualizationsSection & "- " & visualTitle & ": " & visualizationFile.Path & vbCrLf
        End If
    Next
    
    ' Voeg de visualisatiesectie toe aan de inhoud
    result = content & visualizationsSection
    
    AddVisualizationsToContent = result
End Function

' Functie om een titel te genereren voor een visualisatie
Function GetVisualizationTitle(fileName)
    Dim title
    
    ' Verwijder de extensie
    title = Replace(fileName, ".png", "")
    
    ' Verwijder het voorvoegsel (analysetype_)
    If InStr(title, "_") > 0 Then
        title = Mid(title, InStr(title, "_") + 1)
    End If
    
    ' Vervang underscores door spaties
    title = Replace(title, "_", " ")
    
    ' Maak de eerste letter van elk woord hoofdletter
    Dim words, i, result
    words = Split(title, " ")
    result = ""
    
    For i = 0 To UBound(words)
        If Len(words(i)) > 0 Then
            result = result & UCase(Left(words(i), 1)) & Mid(words(i), 2) & " "
        End If
    Next
    
    ' Verwijder eventuele extra spaties aan het einde
    result = Trim(result)
    
    ' Voeg specifieke titels toe op basis van de bestandsnaam
    If InStr(fileName, "panel_test_pie") > 0 Then
        result = "Panel Test Resultaten"
    ElseIf InStr(fileName, "klanttevredenheid_bar") > 0 Then
        result = "Klanttevredenheid per Koffieboon Type"
    ElseIf InStr(fileName, "klantretour_bar") > 0 Then
        result = "Klantretourpercentage per Koffieboon Type"
    ElseIf InStr(fileName, "benutting_bar") > 0 Then
        result = "Benuttingsgraad per Verpakkingsmachine"
    ElseIf InStr(fileName, "voorraad_bar") > 0 Then
        result = "Voorraadniveaus Statistieken"
    ElseIf InStr(fileName, "batch_defect_bar") > 0 Then
        result = "Top 10 Batches met Hoogste Defectpercentage"
    ElseIf InStr(fileName, "capability_bar") > 0 Then
        result = "Process Capability Analyse"
    ElseIf InStr(fileName, "procestijd_bar") > 0 Then
        result = "Procestijd Analyse"
    ElseIf InStr(fileName, "energie_bar") > 0 Then
        result = "Energieverbruik per Verpakkingsmachine"
    End If
    
    GetVisualizationTitle = result
End Function

' Start het script
Call Main()
