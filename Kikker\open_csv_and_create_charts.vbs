Option Explicit

' Script om CSV-bestanden te openen in Excel en direct grafieken te maken
' Dit script gebruikt alleen VBS en Excel, geen Python

' Constanten
Const xlDelimited = 1
Const xlTextQualifierDoubleQuote = 1
Const xlWindows = 2
Const xlNormal = -4143
Const xlCenter = -4108
Const xlBottom = -4107

' Grafiektypen
Const xlColumnClustered = 51 ' Gegroepeerde kolomgrafiek
Const xlPie = 5 ' Cirkeldiagram
Const xlLine = 4 ' Lijngrafiek
Const xlBarClustered = 57 ' Gegroepeerde staafgrafiek

' Hoofdfunctie
Sub Main()
    Dim fso, excel, csvFiles, csvFile, fileName, analysisType
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Zoek alle CSV-bestanden met "_Analyse_Clean" in de naam
    Set csvFiles = fso.GetFolder(".").Files
    
    ' Verwerk elk CSV-bestand
    For Each csvFile In csvFiles
        fileName = fso.GetFileName(csvFile)
        
        ' Controleer of het een analyse CSV-bestand is
        If InStr(fileName, "_Analyse_Clean.csv") > 0 Then
            ' Bepaal het type analyse
            If InStr(fileName, "Kaizen") > 0 Then
                analysisType = "kaizen"
            ElseIf InStr(fileName, "Lean") > 0 Then
                analysisType = "lean"
            ElseIf InStr(fileName, "SixSigma") > 0 Then
                analysisType = "sixsigma"
            ElseIf InStr(fileName, "TOC") > 0 Then
                analysisType = "toc"
            Else
                analysisType = ""
            End If
            
            ' Als het een bekend analysetype is, verwerk het bestand
            If analysisType <> "" Then
                WScript.Echo "Verwerken van " & fileName & "..."
                
                ' Open het CSV-bestand in Excel en maak grafieken
                OpenCSVAndCreateCharts excel, csvFile.Path, analysisType
            End If
        End If
    Next
    
    WScript.Echo "Alle bestanden zijn verwerkt. Excel blijft open zodat u de grafieken kunt bekijken."
End Sub

' Functie om een CSV-bestand te openen in Excel en grafieken te maken
Sub OpenCSVAndCreateCharts(excel, csvFilePath, analysisType)
    Dim wb, ws, fso, separator, dataRange
    Dim lastRow, lastCol, chartTitle, chartSheet
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Bepaal het scheidingsteken (tab of komma)
    separator = DetermineDelimiter(csvFilePath)
    
    ' Open het CSV-bestand in Excel
    Set wb = excel.Workbooks.Open(csvFilePath)
    Set ws = wb.Sheets(1)
    
    ' Voeg een nieuw werkblad toe voor de grafieken
    wb.Sheets.Add After:=ws
    Set chartSheet = wb.Sheets(2)
    chartSheet.Name = "Grafieken"
    
    ' Maak grafieken op basis van het analysetype
    Select Case analysisType
        Case "kaizen"
            ' Maak een cirkeldiagram voor Panel Test Resultaten
            Dim panelTestRange
            panelTestRange = FindDataRange(ws, "Panel Test", "Voldoet")
            
            If Not panelTestRange Is Nothing Then
                chartTitle = "Panel Test Resultaten"
                CreatePieChart chartSheet, panelTestRange, chartTitle, 10, 10
            End If
            
            ' Maak een staafdiagram voor Klanttevredenheid per Koffieboon Type
            Dim klanttevredenheidRange
            klanttevredenheidRange = FindDataRange(ws, "Klanttevredenheid", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klanttevredenheidRange Is Nothing Then
                chartTitle = "Klanttevredenheid per Koffieboon Type"
                CreateBarChart chartSheet, klanttevredenheidRange, chartTitle, 10, 320
            End If
            
            ' Maak een staafdiagram voor Klantretourpercentage per Koffieboon Type
            Dim klantretourRange
            klantretourRange = FindDataRange(ws, "Klantretourpercentage", "Excelsa|Arabica|Robusta|Liberica")
            
            If Not klantretourRange Is Nothing Then
                chartTitle = "Klantretourpercentage per Koffieboon Type"
                CreateBarChart chartSheet, klantretourRange, chartTitle, 10, 630
            End If
            
        Case "lean"
            ' Maak een staafdiagram voor Benuttingsgraad per Verpakkingsmachine
            Dim benuttingRange
            benuttingRange = FindDataRange(ws, "Benuttingsgraad", "Packager|Onbekend")
            
            If Not benuttingRange Is Nothing Then
                chartTitle = "Benuttingsgraad per Verpakkingsmachine"
                CreateBarChart chartSheet, benuttingRange, chartTitle, 10, 10
            End If
            
            ' Maak een staafdiagram voor Voorraadniveaus
            Dim voorraadRange
            voorraadRange = FindDataRange(ws, "Voorraadniveaus", "Gemiddelde|Mediaan")
            
            If Not voorraadRange Is Nothing Then
                chartTitle = "Voorraadniveaus Statistieken"
                CreateBarChart chartSheet, voorraadRange, chartTitle, 10, 320
            End If
            
        Case "sixsigma"
            ' Maak een staafdiagram voor Top 10 batches met hoogste defectpercentage
            Dim batchRange
            batchRange = FindDataRange(ws, "batch", "Batch")
            
            If Not batchRange Is Nothing Then
                chartTitle = "Top 10 Batches met Hoogste Defectpercentage"
                CreateBarChart chartSheet, batchRange, chartTitle, 10, 10
            End If
            
            ' Maak een staafdiagram voor Process Capability
            Dim capabilityRange
            capabilityRange = FindDataRange(ws, "capability", "Cp|Cpk")
            
            If Not capabilityRange Is Nothing Then
                chartTitle = "Process Capability Analyse"
                CreateBarChart chartSheet, capabilityRange, chartTitle, 10, 320
            End If
            
        Case "toc"
            ' Maak een staafdiagram voor Procestijd Analyse
            Dim procestijdRange
            procestijdRange = FindDataRange(ws, "Procestijd", "Grinding|Filling|Packaging")
            
            If Not procestijdRange Is Nothing Then
                chartTitle = "Procestijd Analyse"
                CreateBarChart chartSheet, procestijdRange, chartTitle, 10, 10
            End If
            
            ' Maak een staafdiagram voor Energieverbruik per Verpakkingsmachine
            Dim energieRange
            energieRange = FindDataRange(ws, "Energieverbruik", "Packager|Onbekend")
            
            If Not energieRange Is Nothing Then
                chartTitle = "Energieverbruik per Verpakkingsmachine"
                CreateBarChart chartSheet, energieRange, chartTitle, 10, 320
            End If
    End Select
    
    ' Activeer het grafiekenwerkblad
    chartSheet.Activate
    
    ' Sla het werkboek op als Excel-bestand
    Dim excelFileName
    excelFileName = Replace(csvFilePath, ".csv", "_with_charts.xlsx")
    wb.SaveAs excelFileName, 51 ' 51 = xlsx-formaat
    
    WScript.Echo "Bestand opgeslagen als " & excelFileName
End Sub

' Functie om het scheidingsteken van een CSV-bestand te bepalen
Function DetermineDelimiter(filePath)
    Dim fso, file, line, delimiter
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    
    ' Lees de eerste regel
    If Not file.AtEndOfStream Then
        line = file.ReadLine
        
        ' Controleer welk scheidingsteken wordt gebruikt
        If InStr(line, vbTab) > 0 Then
            delimiter = vbTab
        ElseIf InStr(line, ",") > 0 Then
            delimiter = ","
        Else
            delimiter = "," ' Standaard
        End If
    Else
        delimiter = "," ' Standaard
    End If
    
    file.Close
    
    DetermineDelimiter = delimiter
End Function

' Functie om een databereik te vinden op basis van zoektermen
Function FindDataRange(ws, sectionKeyword, dataKeywords)
    Dim row, lastRow, startRow, endRow, col, dataFound
    Dim keywordArray, keyword, i, cellValue
    
    lastRow = ws.UsedRange.Rows.Count
    dataFound = False
    startRow = 0
    endRow = 0
    
    ' Split de dataKeywords op |
    keywordArray = Split(dataKeywords, "|")
    
    ' Zoek naar de sectie
    For row = 1 To lastRow
        cellValue = ws.Cells(row, 1).Value
        
        ' Controleer of de cel de sectie-keyword bevat
        If Not IsEmpty(cellValue) And InStr(1, cellValue, sectionKeyword, vbTextCompare) > 0 Then
            ' Zoek naar de data binnen deze sectie
            startRow = row
            
            ' Zoek naar het einde van de sectie (lege regel of nieuwe sectie)
            For i = row + 1 To lastRow
                If IsEmpty(ws.Cells(i, 1).Value) Or Trim(ws.Cells(i, 1).Value) = "" Then
                    endRow = i - 1
                    Exit For
                End If
                
                ' Controleer of deze rij data bevat die we zoeken
                For Each keyword In keywordArray
                    If InStr(1, ws.Cells(i, 1).Value, keyword, vbTextCompare) > 0 Then
                        dataFound = True
                        Exit For
                    End If
                Next
            Next
            
            ' Als we het einde van het werkblad hebben bereikt
            If endRow = 0 And i > lastRow Then
                endRow = lastRow
            End If
            
            ' Als we data hebben gevonden, stop met zoeken
            If dataFound Then
                Exit For
            Else
                ' Reset en zoek verder
                startRow = 0
                endRow = 0
            End If
        End If
    Next
    
    ' Als we data hebben gevonden, maak een bereik
    If dataFound And startRow > 0 And endRow > 0 Then
        ' Bepaal het aantal kolommen
        Dim lastCol
        lastCol = 2 ' Standaard 2 kolommen (categorie en waarde)
        
        ' Maak het bereik
        Set FindDataRange = ws.Range(ws.Cells(startRow, 1), ws.Cells(endRow, lastCol))
    Else
        Set FindDataRange = Nothing
    End If
End Function

' Functie om een cirkeldiagram te maken
Sub CreatePieChart(ws, dataRange, chartTitle, left, top)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlPie)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 2 ' Layout met percentages
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = True
        .Legend.Position = xlBottom
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = top
    chartObj.Left = left
End Sub

' Functie om een staafdiagram te maken
Sub CreateBarChart(ws, dataRange, chartTitle, left, top)
    Dim chart, chartObj
    
    ' Maak een nieuw grafiekobject
    Set chartObj = ws.Shapes.AddChart2(227, xlColumnClustered)
    Set chart = chartObj.Chart
    
    ' Stel de databron in
    chart.SetSourceData dataRange
    
    ' Stel de grafiekopties in
    With chart
        .HasTitle = True
        .ChartTitle.Text = chartTitle
        .ApplyLayout 1 ' Standaard layout
        .ChartStyle = 2 ' Stijl met schaduw
        .HasLegend = False
        
        ' Stel de as-opties in
        .Axes(1).HasTitle = True ' X-as
        .Axes(1).AxisTitle.Text = "Categorie"
        .Axes(2).HasTitle = True ' Y-as
        .Axes(2).AxisTitle.Text = "Waarde"
        
        ' Voeg datawaarden toe
        .SeriesCollection(1).HasDataLabels = True
        .SeriesCollection(1).DataLabels.ShowValue = True
    End With
    
    ' Stel de grootte en positie in
    chartObj.Width = 500
    chartObj.Height = 300
    chartObj.Top = top
    chartObj.Left = left
End Sub

' Start het script
Call Main()
