"""
Americaps Coffee Capsule Production - Data Error Identification

This script systematically identifies common data problems in the Kikker.csv dataset:
1. Missing values
2. Unrealistic values (negative numbers, impossible dates)
3. Typos and inconsistencies
4. Duplicates
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.float_format', '{:.2f}'.format)

def load_data(file_path):
    """
    Load the CSV file and return a pandas DataFrame
    """
    print(f"Loading data from {file_path}...")
    try:
        # Try to load the data with automatic encoding detection
        df = pd.read_csv(file_path)
        print(f"Successfully loaded data with {df.shape[0]} rows and {df.shape[1]} columns.")
        return df
    except UnicodeDecodeError:
        # If automatic encoding fails, try with different encodings
        for encoding in ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"Successfully loaded data with encoding {encoding}.")
                print(f"Data shape: {df.shape[0]} rows and {df.shape[1]} columns.")
                return df
            except UnicodeDecodeError:
                continue
        print("Failed to load the data with common encodings.")
        return None

def identify_missing_values(df):
    """
    Identify and report missing values in the dataset
    """
    print("\n=== 1. MISSING VALUES ===")

    # Check for missing values
    missing_values = df.isnull().sum()
    missing_percent = (df.isnull().sum() / len(df)) * 100
    missing_data = pd.DataFrame({'Missing Values': missing_values,
                                'Percentage': missing_percent})

    # Filter to show only columns with missing values
    missing_data = missing_data[missing_data['Missing Values'] > 0].sort_values('Missing Values', ascending=False)

    if missing_data.empty:
        print("No missing values found in the dataset.")
    else:
        print("Found missing values in the following columns:")
        print(missing_data)

        # Show examples of rows with missing values for top 3 columns
        for column in missing_data.index[:3]:
            print(f"\nExample rows with missing values in '{column}':")
            missing_rows = df[df[column].isnull()].head(3)
            print(missing_rows[['PackagingApparaat', 'Koffieboon', 'Registratiedatum', column]].to_string())

    return missing_data

def identify_unrealistic_values(df):
    """
    Identify unrealistic values in the dataset
    """
    print("\n=== 2. UNREALISTIC VALUES ===")

    unrealistic_values = {}

    # Check for negative values in numeric columns
    numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
    for col in numeric_cols:
        if (df[col] < 0).any():
            negative_count = (df[col] < 0).sum()
            unrealistic_values[f"Negative values in {col}"] = negative_count
            print(f"Found {negative_count} negative values in column '{col}'")
            print("Examples:")
            print(df[df[col] < 0][[col]].head(3).to_string())

    # Check for impossible dates
    date_columns = [col for col in df.columns if 'Datum' in col or 'datum' in col or 'Date' in col or 'date' in col]

    for col in date_columns:
        # First check for obviously wrong date formats
        if df[col].dtype == 'object':
            # Look for patterns like "31-02-2025" (impossible day/month combination)
            impossible_dates = df[df[col].str.contains(r'31-02|30-02|31-04|31-06|31-09|31-11', na=False)]
            if not impossible_dates.empty:
                unrealistic_values[f"Impossible dates in {col}"] = len(impossible_dates)
                print(f"Found {len(impossible_dates)} impossible dates in column '{col}'")
                print("Examples:")
                print(impossible_dates[[col]].head(3).to_string())

            # Look for dates in the far future (e.g., year > 2030)
            future_dates = df[df[col].str.contains(r'20[3-9][0-9]|2[1-9][0-9]{2}', na=False)]
            if not future_dates.empty:
                unrealistic_values[f"Future dates in {col}"] = len(future_dates)
                print(f"Found {len(future_dates)} far future dates in column '{col}'")
                print("Examples:")
                print(future_dates[[col]].head(3).to_string())

            # Look for invalid time values (hours > 23, minutes/seconds > 59)
            invalid_times = df[df[col].str.contains(r'2[4-9]:|[3-9][0-9]:|:[6-9][0-9]', na=False)]
            if not invalid_times.empty:
                unrealistic_values[f"Invalid times in {col}"] = len(invalid_times)
                print(f"Found {len(invalid_times)} invalid time values in column '{col}'")
                print("Examples:")
                print(invalid_times[[col]].head(3).to_string())

    # Check for unrealistic percentages (> 100%)
    percentage_cols = [col for col in df.columns if df[col].dtype == 'object' and
                      df[col].str.contains('%', na=False).any()]

    for col in percentage_cols:
        # Extract numeric values from percentage strings
        df[f'{col}_numeric'] = df[col].str.replace('%', '').astype(float)

        # Check for percentages > 100%
        high_percentages = df[df[f'{col}_numeric'] > 100]
        if not high_percentages.empty:
            unrealistic_values[f"Percentages > 100% in {col}"] = len(high_percentages)
            print(f"Found {len(high_percentages)} values > 100% in column '{col}'")
            print("Examples:")
            print(high_percentages[[col]].head(3).to_string())

        # Remove temporary column
        df.drop(f'{col}_numeric', axis=1, inplace=True)

    # Check for unrealistic values in specific columns
    if 'Cyclustijd' in df.columns:
        # Check for cycle times that are too long (e.g., > 24 hours)
        if df['Cyclustijd'].dtype == 'object':
            # Extract numeric values if in format like "X.XX uur"
            df['Cyclustijd_numeric'] = df['Cyclustijd'].str.extract(r'(\d+\.?\d*)').astype(float)
            long_cycles = df[df['Cyclustijd_numeric'] > 24]
            if not long_cycles.empty:
                unrealistic_values["Cycle times > 24 hours"] = len(long_cycles)
                print(f"Found {len(long_cycles)} cycle times > 24 hours")
                print("Examples:")
                print(long_cycles[['Cyclustijd']].head(3).to_string())
            df.drop('Cyclustijd_numeric', axis=1, inplace=True)

    if 'Klanttevredenheid' in df.columns:
        # Assuming customer satisfaction is on a scale of 1-10
        if df['Klanttevredenheid'].dtype != 'object':
            out_of_range = df[(df['Klanttevredenheid'] > 10) | (df['Klanttevredenheid'] < 0)]
            if not out_of_range.empty:
                unrealistic_values["Customer satisfaction out of range"] = len(out_of_range)
                print(f"Found {len(out_of_range)} customer satisfaction values out of expected range (0-10)")
                print("Examples:")
                print(out_of_range[['Klanttevredenheid']].head(3).to_string())

    if len(unrealistic_values) == 0:
        print("No unrealistic values found in the dataset.")

    return unrealistic_values

def identify_typos_and_inconsistencies(df):
    """
    Identify typos and inconsistencies in the dataset
    """
    print("\n=== 3. TYPOS AND INCONSISTENCIES ===")

    inconsistencies = {}

    # Check categorical columns for inconsistencies
    categorical_cols = df.select_dtypes(include=['object']).columns

    for col in categorical_cols:
        # Skip columns that are likely to contain unique values or long text
        if col in ['Opmerkingen', 'Batchnr'] or df[col].nunique() > df.shape[0] * 0.5:
            continue

        # Get value counts
        value_counts = df[col].value_counts()

        # Check for similar values that might be typos
        similar_values = []
        values_list = value_counts.index.tolist()

        # Only check if there are at least 2 values
        if len(values_list) >= 2:
            for i, val1 in enumerate(values_list):
                if isinstance(val1, str) and len(val1) > 3:  # Only check strings with length > 3
                    for j, val2 in enumerate(values_list[i+1:], i+1):
                        if isinstance(val2, str) and len(val2) > 3:
                            # Check for similar strings (case differences, small typos)
                            if val1.lower() == val2.lower() and val1 != val2:
                                similar_values.append((val1, val2, "Case difference"))
                            elif len(val1) > 4 and len(val2) > 4:
                                # Check for small differences (1-2 characters)
                                if val1.lower()[:4] == val2.lower()[:4] and val1 != val2:
                                    similar_values.append((val1, val2, "Possible typo"))

        if similar_values:
            inconsistencies[col] = similar_values
            print(f"\nPossible inconsistencies in column '{col}':")
            for val1, val2, reason in similar_values:
                print(f"  - '{val1}' and '{val2}': {reason}")
                print(f"    '{val1}' appears {value_counts[val1]} times")
                print(f"    '{val2}' appears {value_counts[val2]} times")

    # Check for inconsistent date formats
    date_columns = [col for col in df.columns if 'Datum' in col or 'datum' in col or 'Date' in col or 'date' in col]

    for col in date_columns:
        if df[col].dtype == 'object':
            # Check for different date formats
            formats = []

            # Check for YYYY-MM-DD format
            iso_format = df[df[col].str.contains(r'^\d{4}-\d{2}-\d{2}', na=False)]
            if not iso_format.empty:
                formats.append(("ISO format (YYYY-MM-DD)", len(iso_format)))

            # Check for DD-MM-YYYY format
            euro_format = df[df[col].str.contains(r'^\d{2}-\d{2}-\d{4}', na=False)]
            if not euro_format.empty:
                formats.append(("European format (DD-MM-YYYY)", len(euro_format)))

            # Check for MM/DD/YYYY format
            us_format = df[df[col].str.contains(r'^\d{2}/\d{2}/\d{4}', na=False)]
            if not us_format.empty:
                formats.append(("US format (MM/DD/YYYY)", len(us_format)))

            if len(formats) > 1:
                inconsistencies[f"Date formats in {col}"] = formats
                print(f"\nInconsistent date formats in column '{col}':")
                for format_name, count in formats:
                    print(f"  - {format_name}: {count} rows")

    # Check for inconsistent units
    if 'Cyclustijd' in df.columns and df['Cyclustijd'].dtype == 'object':
        # Check for different time units
        hours = df[df['Cyclustijd'].str.contains('uur|hour', na=False)]
        minutes = df[df['Cyclustijd'].str.contains('min|minute', na=False)]
        seconds = df[df['Cyclustijd'].str.contains('sec|second', na=False)]

        if sum([len(hours), len(minutes), len(seconds)]) > 0 and len(set([len(hours), len(minutes), len(seconds)])) > 1:
            inconsistencies["Time units in Cyclustijd"] = [
                ("hours", len(hours)),
                ("minutes", len(minutes)),
                ("seconds", len(seconds))
            ]
            print("\nInconsistent time units in column 'Cyclustijd':")
            print(f"  - Hours: {len(hours)} rows")
            print(f"  - Minutes: {len(minutes)} rows")
            print(f"  - Seconds: {len(seconds)} rows")

    if len(inconsistencies) == 0:
        print("No obvious typos or inconsistencies found in the dataset.")

    return inconsistencies

def identify_duplicates(df):
    """
    Identify duplicate rows in the dataset
    """
    print("\n=== 4. DUPLICATES ===")

    # Check for exact duplicates
    exact_duplicates = df.duplicated().sum()

    if exact_duplicates > 0:
        print(f"Found {exact_duplicates} exact duplicate rows in the dataset.")
        print("\nExample of duplicate rows:")

        # Get the first few duplicate rows
        duplicate_rows = df[df.duplicated(keep='first')]
        print(duplicate_rows.head(3).to_string())

        # Show the original rows that are duplicated
        first_dup = duplicate_rows.iloc[0]
        original = df[df.duplicated(keep='first') | df.duplicated(keep='last')].head(6)
        print("\nOriginal and duplicate rows side by side:")
        print(original.to_string())
    else:
        print("No exact duplicate rows found in the dataset.")

    # Check for potential near-duplicates (same values in key columns but different in others)
    if 'Batchnr' in df.columns:
        batch_duplicates = df['Batchnr'].duplicated().sum()
        if batch_duplicates > 0:
            print(f"\nFound {batch_duplicates} duplicate batch numbers.")
            print("This might indicate near-duplicate records with the same batch but different other values.")

            # Show examples of rows with duplicate batch numbers
            dup_batches = df[df['Batchnr'].duplicated(keep=False)].sort_values('Batchnr').head(6)
            print("\nExample rows with duplicate batch numbers:")
            print(dup_batches[['Batchnr', 'PackagingApparaat', 'Koffieboon', 'Registratiedatum']].to_string())

    return exact_duplicates

def main():
    """
    Main function to execute the data error identification
    """
    # Load the data
    file_path = "Kikker.csv"
    df = load_data(file_path)

    if df is None:
        print("Failed to load the data. Please check the file path and format.")
        return

    # Identify missing values
    missing_data = identify_missing_values(df)

    # Identify unrealistic values
    unrealistic_values = identify_unrealistic_values(df)

    # Identify typos and inconsistencies
    inconsistencies = identify_typos_and_inconsistencies(df)

    # Identify duplicates
    duplicates = identify_duplicates(df)

    # Summary of findings
    print("\n=== SUMMARY OF DATA ERRORS ===")
    print(f"1. Missing Values: {missing_data.shape[0]} columns with missing data")
    print(f"2. Unrealistic Values: {len(unrealistic_values)} types of unrealistic values found")
    print(f"3. Typos and Inconsistencies: {len(inconsistencies)} types of inconsistencies found")
    print(f"4. Duplicates: {duplicates} exact duplicate rows found")

    print("\nData error identification complete. The findings above can guide your data cleaning process.")

if __name__ == "__main__":
    print("Starting data error identification...")
    main()
