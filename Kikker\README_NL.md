# Americaps CSV Data Cleaner

Deze tool biedt functionaliteit voor het laden, opschonen en analyseren van CSV-gegevens voor Americaps koffiecapsule productiedata.

## Functionaliteiten

- **Gegevens laden**: Laad CSV-bestand<PERSON> met aan<PERSON>bare scheidingstekens en codering
- **G<PERSON>vens opschonen**:
  - <PERSON><PERSON><PERSON><PERSON> met ontbrekende waarden in numerieke en tekstkolommen
  - Converteren van gegevenstypen (datums, percentages, enz.)
  - Verwijderen van dubbele rijen
  - Standaardiseren van tekst (witruimte verwijderen, omzetten naar kleine letters)
  - Om<PERSON><PERSON> met uitschieters in numerieke kolommen
- **Rapportage**: <PERSON>reer gedetailleerde rapporten van het opschoonproces
- **G<PERSON><PERSON>s opslaan**: Sla opgeschoonde gegevens op in CSV-bestanden

## Gebruik

### Gebruik via de commandoregel

```bash
python clean_csv.py invoer.csv -o uitvoer.csv [opties]
```

#### Opties:

- `-o, --output`: Pad naar het uitvoer CSV-bestand (standaard: invoer_cleaned.csv)
- `-d, --delimiter`: CSV-scheidingsteken (standaard: komma)
- `-e, --encoding`: Bestandscodering (standaard: utf-8)
- `--numeric-fill`: Strategie voor het invullen van ontbrekende numerieke waarden (mean, median, mode, zero, none)
- `--text-fill`: Strategie voor het invullen van ontbrekende tekstwaarden (mode, empty, unknown, none)
- `--no-duplicates`: Verwijder geen dubbele rijen
- `--no-trim`: Verwijder geen witruimte uit tekstkolommen
- `--lowercase`: Zet tekstkolommen om naar kleine letters
- `--handle-outliers`: Behandel uitschieters in numerieke kolommen
- `--report`: Pad om het opschoonrapport op te slaan

### Gebruik als Python-module

Je kunt de CSV-cleaner ook als Python-module in je eigen scripts gebruiken:

```python
from csv_cleaner import load_csv, clean_csv_data, save_cleaned_data, generate_cleaning_report

# Laad CSV-bestand
df = load_csv('invoer.csv')

# Stel opschoonopties in
options = {
    'numeric_fill': 'median',
    'text_fill': 'mode',
    'trim_text': True,
    'lowercase_text': False,
    'remove_duplicates': True,
    'handle_outliers': True
}

# Schoon gegevens op
df_cleaned, summary = clean_csv_data(df, options)

# Sla opgeschoonde gegevens op
save_cleaned_data(df_cleaned, 'uitvoer.csv')

# Genereer rapport
generate_cleaning_report(df, df_cleaned, summary, 'rapport.txt')
```

## Voorbeeld

Er is een voorbeeldscript beschikbaar om te laten zien hoe je de CSV-cleaner kunt gebruiken:

```bash
python example_usage.py
```

Dit script:
1. Maakt een voorbeeld CSV-bestand met datakwaliteitsproblemen
2. Laadt het CSV-bestand
3. Schoont de gegevens op
4. Slaat de opgeschoonde gegevens op
5. Genereert een opschoonrapport

## Opschoonproces

De CSV-cleaner voert de volgende stappen uit:

1. **Gegevens laden**: Laad het CSV-bestand in een pandas DataFrame
2. **Kolomtypen identificeren**: Categoriseer kolommen als numeriek, tekst, datetime, enz.
3. **Duplicaten verwijderen**: Verwijder dubbele rijen
4. **Tekst standaardiseren**: Verwijder witruimte en/of zet om naar kleine letters
5. **Gegevenstypen corrigeren**: Zet kolommen om naar de juiste gegevenstypen
6. **Uitschieters behandelen**: Identificeer en behandel uitschieters in numerieke kolommen
7. **Ontbrekende waarden opschonen**: Vul ontbrekende waarden in op basis van gespecificeerde strategieën
8. **Gegevens opslaan**: Sla de opgeschoonde gegevens op in een CSV-bestand
9. **Rapport genereren**: Maak een gedetailleerd rapport van het opschoonproces

## Vereisten

- Python 3.6+
- pandas
- numpy
