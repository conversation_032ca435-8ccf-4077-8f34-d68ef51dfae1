import os
import pandas as pd
import base64
from io import BytesIO
import matplotlib.pyplot as plt
import numpy as np

def read_csv_file(file_path, sep='\t'):
    """Lees een CSV-bestand en geef de inhoud terug als een lijst met regels."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return lines
    except Exception as e:
        print(f"Fout bij lezen van {file_path}: {e}")
        return []

def write_csv_file(file_path, lines):
    """Schrijf een lijst met regels naar een CSV-bestand."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print(f"Bestand succesvol bijgewerkt: {file_path}")
    except Exception as e:
        print(f"Fout bij schrijven naar {file_path}: {e}")

def get_visualization_paths(analysis_type):
    """<PERSON><PERSON> de paden van de visualisaties voor een bepaald analysetype."""
    vis_dir = "visualisaties"
    return [os.path.join(vis_dir, f) for f in os.listdir(vis_dir) if f.startswith(analysis_type.lower())]

def update_kaizen_csv():
    """Werk het Kaizen_Analyse_Clean.csv bestand bij met visualisaties."""
    file_path = "Kaizen_Analyse_Clean.csv"
    lines = read_csv_file(file_path)
    
    if not lines:
        return
    
    # Zoek de visualisaties
    vis_paths = get_visualization_paths("kaizen")
    
    if not vis_paths:
        print(f"Geen visualisaties gevonden voor Kaizen analyse.")
        return
    
    # Voeg een lege regel toe als die er nog niet is
    if lines[-1].strip():
        lines.append("\n")
    
    # Voeg een sectie toe voor visualisaties
    lines.append("\nVisualisaties:\n")
    
    # Beschrijf elke visualisatie
    for path in vis_paths:
        vis_name = os.path.basename(path).replace(".png", "").replace("kaizen_", "")
        if "panel_test" in vis_name:
            lines.append(f"- Panel Test Resultaten: {path}\n")
        elif "klanttevredenheid" in vis_name:
            lines.append(f"- Klanttevredenheid per Koffieboon Type: {path}\n")
        elif "klantretour" in vis_name:
            lines.append(f"- Klantretourpercentage per Koffieboon Type: {path}\n")
    
    # Schrijf het bijgewerkte bestand
    write_csv_file(file_path, lines)

def update_lean_csv():
    """Werk het Lean_Analyse_Clean.csv bestand bij met visualisaties."""
    file_path = "Lean_Analyse_Clean.csv"
    lines = read_csv_file(file_path)
    
    if not lines:
        return
    
    # Zoek de visualisaties
    vis_paths = get_visualization_paths("lean")
    
    if not vis_paths:
        print(f"Geen visualisaties gevonden voor Lean analyse.")
        return
    
    # Voeg een lege regel toe als die er nog niet is
    if lines[-1].strip():
        lines.append("\n")
    
    # Voeg een sectie toe voor visualisaties
    lines.append("\nVisualisaties:\n")
    
    # Beschrijf elke visualisatie
    for path in vis_paths:
        vis_name = os.path.basename(path).replace(".png", "").replace("lean_", "")
        if "benutting" in vis_name:
            lines.append(f"- Benuttingsgraad per Verpakkingsmachine: {path}\n")
        elif "voorraad" in vis_name:
            lines.append(f"- Voorraadniveaus Statistieken: {path}\n")
        elif "cyclustijd" in vis_name:
            lines.append(f"- Cyclustijd Statistieken: {path}\n")
    
    # Schrijf het bijgewerkte bestand
    write_csv_file(file_path, lines)

def update_sixsigma_csv():
    """Werk het SixSigma_Analyse_Clean.csv bestand bij met visualisaties."""
    file_path = "SixSigma_Analyse_Clean.csv"
    lines = read_csv_file(file_path)
    
    if not lines:
        return
    
    # Zoek de visualisaties
    vis_paths = get_visualization_paths("sixsigma")
    
    if not vis_paths:
        print(f"Geen visualisaties gevonden voor Six Sigma analyse.")
        return
    
    # Voeg een lege regel toe als die er nog niet is
    if lines[-1].strip():
        lines.append("\n")
    
    # Voeg een sectie toe voor visualisaties
    lines.append("\nVisualisaties:\n")
    
    # Beschrijf elke visualisatie
    for path in vis_paths:
        vis_name = os.path.basename(path).replace(".png", "").replace("sixsigma_", "")
        if "batch" in vis_name:
            lines.append(f"- Top 10 Batches met Hoogste Defectpercentage: {path}\n")
        elif "capability" in vis_name:
            lines.append(f"- Process Capability Analyse: {path}\n")
    
    # Schrijf het bijgewerkte bestand
    write_csv_file(file_path, lines)

def update_toc_csv():
    """Werk het TOC_Analyse_Clean.csv bestand bij met visualisaties."""
    file_path = "TOC_Analyse_Clean.csv"
    lines = read_csv_file(file_path)
    
    if not lines:
        return
    
    # Zoek de visualisaties
    vis_paths = get_visualization_paths("toc")
    
    if not vis_paths:
        print(f"Geen visualisaties gevonden voor TOC analyse.")
        return
    
    # Voeg een lege regel toe als die er nog niet is
    if lines[-1].strip():
        lines.append("\n")
    
    # Voeg een sectie toe voor visualisaties
    lines.append("\nVisualisaties:\n")
    
    # Beschrijf elke visualisatie
    for path in vis_paths:
        vis_name = os.path.basename(path).replace(".png", "").replace("toc_", "")
        if "procestijd" in vis_name:
            lines.append(f"- Procestijd Analyse: {path}\n")
        elif "energie" in vis_name:
            lines.append(f"- Energieverbruik per Verpakkingsmachine: {path}\n")
    
    # Schrijf het bijgewerkte bestand
    write_csv_file(file_path, lines)

def main():
    """Hoofdfunctie om alle CSV-bestanden bij te werken."""
    print("CSV-bestanden bijwerken met visualisaties...")
    
    # Werk elk CSV-bestand bij
    update_kaizen_csv()
    update_lean_csv()
    update_sixsigma_csv()
    update_toc_csv()
    
    print("Alle CSV-bestanden zijn bijgewerkt.")

if __name__ == "__main__":
    main()
