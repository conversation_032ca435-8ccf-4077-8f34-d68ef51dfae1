Option Explicit

' Script om alle CSV-bestanden te combineren in één Excel-bestand
' Dit script combineert Kikker_cleaned.csv en alle analyse-bestanden in één Excel-bestand

' Hoofdfunctie
Sub Main()
    Dim fso, excel, mainDataFile, analyseFiles, file, fileName
    
    ' Maak een FileSystemObject om bestanden te beheren
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Dim wb
    Set wb = excel.Workbooks.Add
    
    ' Verwijder alle standaard werkbladen behalve het eerste
    While wb.Sheets.Count > 1
        excel.DisplayAlerts = False
        wb.Sheets(2).Delete
        excel.DisplayAlerts = True
    Wend
    
    ' Hernoem het eerste werkblad
    wb.Sheets(1).Name = "Kikker_cleaned"
    
    ' Controleer of het hoofdbestand bestaat
    mainDataFile = "Kikker_cleaned.csv"
    If fso.FileExists(mainDataFile) Then
        WScript.Echo "Importeren van " & mainDataFile & "..."
        ImportCSVToSheet mainDataFile, wb.Sheets(1)
    Else
        WScript.Echo "Het bestand " & mainDataFile & " bestaat niet."
    End If
    
    ' Zoek alle analyse CSV-bestanden
    Dim analyseTypes, i
    analyseTypes = Array("Lean_Analyse_Clean.csv", "Kaizen_Analyse_Clean.csv", "TOC_Analyse_Clean.csv", "SixSigma_Analyse_Clean.csv")
    
    ' Verwerk elk analyse-bestand
    For i = 0 To UBound(analyseTypes)
        fileName = analyseTypes(i)
        
        ' Controleer of het bestand bestaat
        If fso.FileExists(fileName) Then
            WScript.Echo "Verwerken van " & fileName & "..."
            
            ' Voeg een nieuw werkblad toe voor deze analyse
            Dim newSheet
            wb.Sheets.Add After:=wb.Sheets(wb.Sheets.Count)
            Set newSheet = wb.Sheets(wb.Sheets.Count)
            newSheet.Name = Replace(fileName, ".csv", "")
            
            ' Importeer de analyse-data
            ImportCSVToSheet fileName, newSheet
        Else
            WScript.Echo "Het bestand " & fileName & " bestaat niet en wordt overgeslagen."
        End If
    Next
    
    ' Sla het Excel-bestand op
    Dim excelFileName
    excelFileName = "Kikker_Analyses_Gecombineerd.xlsx"
    wb.SaveAs excelFileName, 51 ' 51 = xlsx-formaat
    
    WScript.Echo "Bestand opgeslagen als " & excelFileName
    WScript.Echo "Alle analyses zijn gecombineerd in één Excel-bestand."
End Sub

' Functie om CSV-data te importeren in een werkblad
Sub ImportCSVToSheet(csvFilePath, sheet)
    Dim fso, file, line, row, col, values, separator
    
    ' Maak een FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Bepaal het scheidingsteken (tab of komma)
    separator = DetermineDelimiter(csvFilePath)
    
    ' Open het CSV-bestand
    Set file = fso.OpenTextFile(csvFilePath, 1) ' 1 = ForReading
    
    row = 1
    
    ' Lees elke regel van het CSV-bestand
    While Not file.AtEndOfStream
        line = file.ReadLine
        
        ' Sla lege regels over
        If Trim(line) <> "" Then
            ' Split de regel op het scheidingsteken
            values = Split(line, separator)
            
            ' Schrijf de waarden naar het werkblad
            For col = 0 To UBound(values)
                sheet.Cells(row, col + 1).Value = Trim(values(col))
            Next
            
            row = row + 1
        End If
    Wend
    
    file.Close
    
    ' Pas de kolombreedtes aan
    sheet.Columns.AutoFit
End Sub

' Functie om het scheidingsteken van een CSV-bestand te bepalen
Function DetermineDelimiter(filePath)
    Dim fso, file, line, delimiter
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set file = fso.OpenTextFile(filePath, 1) ' 1 = ForReading
    
    ' Lees de eerste regel
    If Not file.AtEndOfStream Then
        line = file.ReadLine
        
        ' Controleer welk scheidingsteken wordt gebruikt
        If InStr(line, vbTab) > 0 Then
            delimiter = vbTab
        ElseIf InStr(line, ",") > 0 Then
            delimiter = ","
        Else
            delimiter = "," ' Standaard
        End If
    Else
        delimiter = "," ' Standaard
    End If
    
    file.Close
    
    DetermineDelimiter = delimiter
End Function

' Start het script
Call Main()
