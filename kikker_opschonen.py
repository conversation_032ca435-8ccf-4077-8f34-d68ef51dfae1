"""
Kikker.csv Data Opschonen
-------------------------
Dit script schoont de <PERSON>.csv dataset op volgens de volgende stappen:
1. Ontbrekende waarden behandelen
2. Onrealistische waarden corrigeren
3. Typfouten en inconsistenties standaardiseren
4. Duplicaten verwijderen
"""

import pandas as pd
import numpy as np
from datetime import datetime

# 1. Data inladen
print("Stap 1: Data inladen...")
df = pd.read_csv("Kikker.csv")
print(f"Dataset geladen: {df.shape[0]} rijen, {df.shape[1]} kolommen")

# 2. Ontbrekende waarden behandelen
print("\nStap 2: Ontbrekende waarden behandelen...")
# Keuze: Numerieke kolommen - mediaan gebruiken
numerieke_kolommen = ['Cost', 'Voorraadniveaus', 'Benuttingsgraad', 'Leveranciersbeoordeling', 
                      'Klanttevredenheid', 'Gewichtscontrole', 'Duurzaamheid Score', 
                      'CO2-Footprint', 'Fair-Trade Score', 'Defectpercentage', 'Energieverbruik']

for kolom in numerieke_kolommen:
    if kolom in df.columns:
        # Converteer naar numeriek als het nog niet numeriek is
        if df[kolom].dtype == 'object':
            df[kolom] = pd.to_numeric(df[kolom].str.replace('[^0-9.-]', '', regex=True), errors='coerce')
        
        # Vul missende waarden in met mediaan
        missend = df[kolom].isnull().sum()
        if missend > 0:
            mediaan = df[kolom].median()
            df[kolom].fillna(mediaan, inplace=True)
            print(f"  - {kolom}: {missend} missende waarden opgevuld met mediaan ({mediaan:.2f})")

# Keuze: Categorische kolommen - modus gebruiken
categorische_kolommen = ['PackagingApparaat', 'Herkomst', 'Koffieboon', 'Panel Test', 
                         'Roosterprofiel', 'Audit van Leverancier']

for kolom in categorische_kolommen:
    if kolom in df.columns:
        missend = df[kolom].isnull().sum()
        if missend > 0:
            modus = df[kolom].mode()[0]
            df[kolom].fillna(modus, inplace=True)
            print(f"  - {kolom}: {missend} missende waarden opgevuld met modus ('{modus}')")

# Keuze: Datumkolommen - mediaan datum gebruiken
datum_kolommen = ['Registratiedatum']
for kolom in datum_kolommen:
    if kolom in df.columns:
        missend = df[kolom].isnull().sum()
        if missend > 0:
            # Converteer naar datetime
            df[kolom] = pd.to_datetime(df[kolom], errors='coerce')
            mediaan_datum = df[kolom].dropna().median()
            df[kolom].fillna(mediaan_datum, inplace=True)
            print(f"  - {kolom}: {missend} missende waarden opgevuld met mediaan datum ({mediaan_datum.strftime('%Y-%m-%d')})")

# Keuze: Tijdstempelkolommen - standaard tijdstempel gebruiken
tijdstempel_kolommen = ['FillingDatumTijdEind', 'PackagingDatumTijdEind', 'FillingDatumTijdStart',
                        'GrindingDatumTijdEind', 'GrindingDatumTijdStart', 'PackagingDatumTijdStart']
for kolom in tijdstempel_kolommen:
    if kolom in df.columns:
        missend = df[kolom].isnull().sum()
        if missend > 0:
            standaard_tijd = "2022-01-01 12:00:00"
            df[kolom].fillna(standaard_tijd, inplace=True)
            print(f"  - {kolom}: {missend} missende waarden opgevuld met standaard tijd ('{standaard_tijd}')")

# 3. Onrealistische waarden corrigeren
print("\nStap 3: Onrealistische waarden corrigeren...")

# Keuze: Negatieve waarden omzetten naar absolute waarden
for kolom in numerieke_kolommen:
    if kolom in df.columns:
        negatief = (df[kolom] < 0).sum()
        if negatief > 0:
            df.loc[df[kolom] < 0, kolom] = df.loc[df[kolom] < 0, kolom].abs()
            print(f"  - {kolom}: {negatief} negatieve waarden omgezet naar positief")

# Keuze: Onmogelijke datums corrigeren
for kolom in tijdstempel_kolommen:
    if kolom in df.columns:
        # Vervang onmogelijke datums
        onmogelijk = df[df[kolom].str.contains('31-02|30-02|31-04|31-06|31-09|31-11|25:|[3-9][0-9]:|:[6-9][0-9]', na=False)].shape[0]
        if onmogelijk > 0:
            df[kolom] = df[kolom].replace(r'31-02-2025 25:61:61', np.nan, regex=True)
            df[kolom] = df[kolom].replace(r'31-02|30-02|31-04|31-06|31-09|31-11', '01-01', regex=True)
            df[kolom] = df[kolom].replace(r'25:|[3-9][0-9]:', '12:', regex=True)
            df[kolom] = df[kolom].replace(r':[6-9][0-9]', ':00', regex=True)
            print(f"  - {kolom}: {onmogelijk} onmogelijke datums gecorrigeerd")
        
        # Converteer naar datetime
        try:
            df[kolom] = pd.to_datetime(df[kolom], errors='coerce')
            
            # Vervang toekomstige datums
            toekomst = (df[kolom] > pd.Timestamp('2025-01-01')).sum()
            if toekomst > 0:
                df.loc[df[kolom] > pd.Timestamp('2025-01-01'), kolom] = pd.Timestamp('2022-01-01')
                print(f"  - {kolom}: {toekomst} toekomstige datums gecorrigeerd")
            
            # Vul NaT waarden in
            nat = df[kolom].isna().sum()
            if nat > 0:
                df[kolom].fillna(pd.Timestamp('2022-01-01'), inplace=True)
                print(f"  - {kolom}: {nat} ongeldige datums opgevuld")
        except Exception as e:
            print(f"  - Fout bij verwerken van {kolom}: {e}")

# Keuze: Extreme cyclustijden corrigeren
if 'Cyclustijd' in df.columns:
    # Extraheer numerieke waarden
    df['Cyclustijd_num'] = df['Cyclustijd'].str.extract(r'(\d+\.?\d*)').astype(float)
    
    # Corrigeer extreme waarden
    extreem = (df['Cyclustijd_num'] > 24).sum()
    if extreem > 0:
        mediaan = df.loc[df['Cyclustijd_num'] <= 24, 'Cyclustijd_num'].median()
        df.loc[df['Cyclustijd_num'] > 24, 'Cyclustijd'] = f"{mediaan:.2f} uur"
        print(f"  - Cyclustijd: {extreem} extreme waarden (>24 uur) vervangen door mediaan ({mediaan:.2f} uur)")
    
    # Verwijder tijdelijke kolom
    df.drop('Cyclustijd_num', axis=1, inplace=True)

# 4. Typfouten en inconsistenties standaardiseren
print("\nStap 4: Typfouten en inconsistenties standaardiseren...")

# Keuze: PackagingApparaat standaardiseren
if 'PackagingApparaat' in df.columns:
    # Vervang onbekende waarden
    onbekend = df[df['PackagingApparaat'].isin(['###', 'Onbekend apparaat'])].shape[0]
    df['PackagingApparaat'] = df['PackagingApparaat'].replace(['###', 'Onbekend apparaat'], 'Onbekend')
    
    # Standaardiseer naamgeving
    standaard_count = 0
    for i in range(1, 6):
        oude_waarde = f"packager {i}"
        nieuwe_waarde = f"Packager {i}"
        count = (df['PackagingApparaat'] == oude_waarde).sum()
        if count > 0:
            df['PackagingApparaat'] = df['PackagingApparaat'].replace(oude_waarde, nieuwe_waarde)
            standaard_count += count
    
    print(f"  - PackagingApparaat: {onbekend} onbekende waarden gestandaardiseerd")
    if standaard_count > 0:
        print(f"  - PackagingApparaat: {standaard_count} inconsistente namen gestandaardiseerd")

# Keuze: Panel Test standaardiseren
if 'Panel Test' in df.columns:
    mapping = {
        'voldoet': 'Voldoet',
        'Voldoet gedeeltelijk': 'Voldoet gedeeltelijk',
        'voldoet gedeeltelijk': 'Voldoet gedeeltelijk',
        'Voldoet niet': 'Voldoet niet',
        'voldoet niet': 'Voldoet niet'
    }
    
    standaard_count = 0
    for oude_waarde, nieuwe_waarde in mapping.items():
        count = (df['Panel Test'] == oude_waarde).sum()
        if count > 0 and oude_waarde != nieuwe_waarde:
            df['Panel Test'] = df['Panel Test'].replace(oude_waarde, nieuwe_waarde)
            standaard_count += count
    
    if standaard_count > 0:
        print(f"  - Panel Test: {standaard_count} inconsistente waarden gestandaardiseerd")

# Keuze: Eenheden standaardiseren
if 'Cyclustijd' in df.columns:
    # Voeg 'uur' toe waar nodig
    geen_eenheid = 0
    for idx, waarde in enumerate(df['Cyclustijd']):
        if isinstance(waarde, str) and not ('uur' in waarde or 'hour' in waarde):
            df.loc[idx, 'Cyclustijd'] = f"{waarde} uur"
            geen_eenheid += 1
    
    # Vervang 'hour' door 'uur'
    hour_count = (df['Cyclustijd'].str.contains('hour', na=False)).sum()
    df['Cyclustijd'] = df['Cyclustijd'].str.replace('hour', 'uur')
    
    if geen_eenheid > 0:
        print(f"  - Cyclustijd: {geen_eenheid} waarden zonder eenheid gecorrigeerd")
    if hour_count > 0:
        print(f"  - Cyclustijd: {hour_count} waarden met 'hour' gestandaardiseerd naar 'uur'")

# 5. Duplicaten verwijderen
print("\nStap 5: Duplicaten verwijderen...")

# Keuze: Exacte duplicaten verwijderen
duplicaten = df.duplicated().sum()
if duplicaten > 0:
    df.drop_duplicates(inplace=True)
    print(f"  - {duplicaten} exacte duplicaten verwijderd")
else:
    print("  - Geen exacte duplicaten gevonden")

# Keuze: Duplicaten in batchnummers behandelen
if 'Batchnr' in df.columns:
    dubbele_batches = df['Batchnr'].duplicated().sum()
    if dubbele_batches > 0:
        print(f"  - {dubbele_batches} dubbele batchnummers gevonden")
        print("  - Originele batchnummers behouden maar unieke identifiers toegevoegd")
        df['Original_Batchnr'] = df['Batchnr']
        df['Batchnr'] = df['Batchnr'].astype(str) + '_' + df.groupby('Batchnr').cumcount().astype(str)

# 6. Opgeschoonde data opslaan
print("\nStap 6: Opgeschoonde data opslaan...")
df.to_csv("Kikker_opgeschoond.csv", index=False)
print(f"Opgeschoonde dataset opgeslagen: {df.shape[0]} rijen, {df.shape[1]} kolommen")

print("\nData opschoning voltooid!")
print("Toelichting bij gemaakte keuzes:")
print("1. Ontbrekende waarden: Mediaan voor numerieke data, modus voor categorische data")
print("2. Onrealistische waarden: Negatieve waarden omgezet naar positief, onmogelijke datums gecorrigeerd")
print("3. Inconsistenties: Namen en eenheden gestandaardiseerd naar consistente notatie")
print("4. Duplicaten: Exacte duplicaten verwijderd, dubbele batchnummers voorzien van unieke identifiers")
