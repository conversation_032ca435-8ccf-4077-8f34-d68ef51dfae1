# PowerShell script to create Word document
try {
    # Create Word Application
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false

    # Create new document
    $doc = $word.Documents.Add()
    $selection = $word.Selection

    # Set default font
    $selection.Font.Name = "Calibri"

    # Title
    $selection.Font.Size = 18
    $selection.Font.Bold = $true
    $selection.TypeText("Communicatieplan Eurocaps")
    $selection.TypeParagraph()

    $selection.Font.Size = 14
    $selection.Font.Bold = $true
    $selection.TypeText("Veranderstrategie: <PERSON><PERSON>'s 8-Stappen Model")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Metadata
    $selection.Font.Size = 11
    $selection.Font.Bold = $true
    $selection.TypeText("Lesopdracht: ")
    $selection.Font.Bold = $false
    $selection.TypeText("Communicatieplan")
    $selection.TypeParagraph()

    $selection.Font.Bold = $true
    $selection.TypeText("Duur: ")
    $selection.Font.Bold = $false
    $selection.TypeText("45 min - Individueel")
    $selection.TypeParagraph()

    $selection.Font.Bold = $true
    $selection.TypeText("Datum: ")
    $selection.Font.Bold = $false
    $selection.TypeText("[Datum]")
    $selection.TypeParagraph()

    $selection.Font.Bold = $true
    $selection.TypeText("Student: ")
    $selection.Font.Bold = $false
    $selection.TypeText("[Naam]")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Horizontal line
    $selection.Font.Size = 11
    $selection.TypeText("________________________________________________________________________________")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Section 1: Gekozen Veranderstrategie
    $selection.Font.Size = 14
    $selection.Font.Bold = $true
    $selection.TypeText("1. Gekozen Veranderstrategie: Kotter's 8-Stappen Model")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Waarom Kotter?")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("Voor Eurocaps is gekozen voor Kotter's 8-stappen model omdat:")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.TypeText("1. Bewezen effectiviteit: Het model is specifiek ontworpen voor organisatieveranderingen in bedrijfsomgevingen")
    $selection.TypeParagraph()
    $selection.TypeText("2. Structurele aanpak: Past bij de hiërarchische cultuur van Eurocaps")
    $selection.TypeParagraph()
    $selection.TypeText("3. Communicatiefocus: Stap 4 (Communiceer de veranderingsvisie) is een kernonderdeel")
    $selection.TypeParagraph()
    $selection.TypeText("4. Reeds toegepast: Eurocaps heeft al ervaring met dit model in eerdere veranderingsinitiatieven")
    $selection.TypeParagraph()
    $selection.TypeText("5. Geschikt voor complexe veranderingen: Ideaal voor de systeemintegratie en procesoptimalisatie bij Eurocaps")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Alternatieve modellen
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Alternatieve modellen overwogen:")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("• Lewin (3-stappen): Te simplistisch voor de complexe veranderingen bij Eurocaps")
    $selection.TypeParagraph()
    $selection.TypeText("• De Caluwé (Kleuren): Minder gestructureerd, past minder bij de resultaatgerichte cultuur")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Section 2: Communicatieplan Opzet
    $selection.Font.Size = 14
    $selection.Font.Bold = $true
    $selection.TypeText("2. Communicatieplan Opzet")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Veranderingsvisie Eurocaps:")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.Font.Italic = $true
    $selection.TypeText('"Eurocaps streeft naar operationele excellentie door geïntegreerde systemen, geoptimaliseerde processen en een cultuur van continue verbetering, waarbij kwaliteit (''Quality. Every Single Time.'') centraal staat."')
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.Font.Italic = $false
    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Strategische Pijlers:")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("1. Procesoptimalisatie door Six Sigma implementatie")
    $selection.TypeParagraph()
    $selection.TypeText("2. Systeemintegratie door geïntegreerd databasesysteem")
    $selection.TypeParagraph()
    $selection.TypeText("3. Ketenoptimalisatie door verbeterde informatiedeling")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Section 3: Stakeholder Analyse
    $selection.Font.Size = 14
    $selection.Font.Bold = $true
    $selection.TypeText("3. Stakeholder Analyse")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Primaire Stakeholders:")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("1. Topmanagement - Strategische beslissers")
    $selection.TypeParagraph()
    $selection.TypeText("2. IT-team (Erik Dekker + 5 FTE) - Implementatie verantwoordelijken")
    $selection.TypeParagraph()
    $selection.TypeText("3. Productiemedewerkers - Dagelijkse gebruikers nieuwe processen")
    $selection.TypeParagraph()
    $selection.TypeText("4. Kwaliteitsmedewerkers - Six Sigma implementatie")
    $selection.TypeParagraph()
    $selection.TypeText("5. Logistiek medewerkers - Supply chain optimalisatie")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    $selection.Font.Size = 12
    $selection.Font.Bold = $true
    $selection.TypeText("Secundaire Stakeholders:")
    $selection.TypeParagraph()

    $selection.Font.Size = 11
    $selection.Font.Bold = $false
    $selection.TypeText("6. Leveranciers - Externe ketenpartners")
    $selection.TypeParagraph()
    $selection.TypeText("7. Klanten (Lidl, andere supermarkten) - Eindgebruikers verbeteringen")
    $selection.TypeParagraph()
    $selection.TypeText("8. Onderhoudsmedewerkers - Ultimo systeem gebruikers")
    $selection.TypeParagraph()
    $selection.TypeParagraph()

    # Save document
    $docPath = Join-Path $PWD "Communicatieplan_Opdracht\Communicatieplan_Eurocaps.docx"
    $doc.SaveAs($docPath)
    $doc.Close()
    $word.Quit()

    Write-Host "Word document created successfully at: $docPath"
}
catch {
    Write-Host "Error creating Word document: $($_.Exception.Message)"
    if ($word) {
        $word.Quit()
    }
}
