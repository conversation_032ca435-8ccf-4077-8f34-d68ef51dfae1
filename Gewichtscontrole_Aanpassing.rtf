{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Gewichtscontrole\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt aan het opschoningsscript om de Gewichtscontrole-waarden af te ronden naar gehele getallen volgens specifieke regels.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset had de Gewichtscontrole-kolom veel decimale waarden (komma's), wat de leesbaarheid verminderde. Het doel was om deze waarden af te ronden naar gehele getallen volgens de volgende regels:\par
\bullet Waarden met decimalen < 0,5 moeten naar beneden worden afgerond\par
\bullet Waarden met decimalen \u8805? 0,5 moeten naar boven worden afgerond\par
\bullet Het resultaat moet een geheel getal zijn (één cijfer)\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
Er is een nieuwe functie \i clean_gewichtscontrole\i0 toegevoegd aan het script om de Gewichtscontrole-waarden af te ronden volgens de specifieke regels:\par

\i def clean_gewichtscontrole(df):\par
    """Clean the Gewichtscontrole column by rounding values according to specific rules"""\par
    print("\\n=== CLEANING GEWICHTSCONTROLE ===")\par
    \par
    # Create a copy to avoid modifying the original dataframe\par
    cleaned_df = df.copy()\par
    \par
    if 'Gewichtscontrole' in cleaned_df.columns:\par
        # Check if the column is numeric\par
        if pd.api.types.is_numeric_dtype(cleaned_df['Gewichtscontrole']):\par
            # Apply custom rounding logic\par
            # For values with decimal part >= 0.5, round up\par
            # For values with decimal part < 0.5, round down\par
            decimal_part = cleaned_df['Gewichtscontrole'] % 1\par
            \par
            # Count values that will be rounded up or down\par
            round_up_count = ((decimal_part >= 0.5) & (decimal_part > 0)).sum()\par
            round_down_count = ((decimal_part < 0.5) & (decimal_part > 0)).sum()\par
            \par
            # Apply rounding rules\par
            cleaned_df.loc[decimal_part >= 0.5, 'Gewichtscontrole'] = np.ceil(cleaned_df.loc[decimal_part >= 0.5, 'Gewichtscontrole'])\par
            cleaned_df.loc[decimal_part < 0.5, 'Gewichtscontrole'] = np.floor(cleaned_df.loc[decimal_part < 0.5, 'Gewichtscontrole'])\par
            \par
            # Convert to integer\par
            cleaned_df['Gewichtscontrole'] = cleaned_df['Gewichtscontrole'].astype(int)\par
            \par
            print(f"Rounded \{round_up_count\} Gewichtscontrole values up and \{round_down_count\} values down")\par
            print(f"Converted all Gewichtscontrole values to integers")\i0\par

Deze code doet het volgende:\par
\bullet Berekent het decimale deel van elke Gewichtscontrole-waarde\par
\bullet Telt het aantal waarden dat naar boven en naar beneden zal worden afgerond\par
\bullet Rondt waarden met decimalen \u8805? 0,5 naar boven af (np.ceil)\par
\bullet Rondt waarden met decimalen < 0,5 naar beneden af (np.floor)\par
\bullet Converteert alle waarden naar gehele getallen (astype(int))\par
\bullet Vervangt eventuele 0-waarden door 1 (de meest voorkomende waarde)\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Gewichtscontrole-waarden als volgt aangepast:\par

\bullet 3793 waarden zijn naar boven afgerond (decimalen \u8805? 0,5)\par
\bullet 3659 waarden zijn naar beneden afgerond (decimalen < 0,5)\par
\bullet 1 nulwaarde (0) is vervangen door 1\par
\bullet Alle waarden zijn geconverteerd naar gehele getallen\par

\b Statistieken van de aangepaste Gewichtscontrole-kolom:\b0\par
\bullet Minimum: 1\par
\bullet Maximum: 2\par
\bullet Gemiddelde: 1,01\par
\bullet Mediaan: 1\par
\bullet Standaarddeviatie: 0,09\par

\b Verdeling van Gewichtscontrole:\b0\par
\bullet 1: 7935 waarden\par
\bullet 2: 65 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Gewichtscontrole-waarden zijn nu veel netter en beter leesbaar:\par

\bullet \b Geen decimalen meer:\b0 Alle waarden zijn nu gehele getallen, wat de leesbaarheid verbetert.\par
\bullet \b Correcte afronding:\b0 Waarden met decimalen < 0,5 zijn naar beneden afgerond, waarden met decimalen \u8805? 0,5 zijn naar boven afgerond.\par
\bullet \b Geen 0-waarden meer:\b0 Er zijn geen 0-waarden meer in de kolom, zoals gevraagd.\par
\bullet \b Eenvoudige verdeling:\b0 De meeste waarden zijn nu 1 (7935 waarden), met enkele waarden van 2 (65 waarden).\par
\bullet \b Consistente notatie:\b0 Alle waarden zijn nu in dezelfde notatie (gehele getallen).\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Gewichtscontrole-kolom heeft succesvol alle decimale waarden afgerond naar gehele getallen volgens de specifieke regels. Dit maakt de dataset beter leesbaar en bruikbaarder voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Gewichtscontrole-kolom intuïtiever en betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de Gewichtscontrole-kolom in de dataset.\i0\fs22\par
}
