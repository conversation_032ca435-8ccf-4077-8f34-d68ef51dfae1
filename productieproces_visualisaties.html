<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productieproces Analyse - Visualisaties</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .graph-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .graph {
            text-align: center;
            margin: 20px 0;
        }
        .graph img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .findings {
            margin-top: 20px;
        }
        .findings h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        ul {
            padding-left: 20px;
        }
        .conclusion {
            margin-top: 40px;
            padding: 20px;
            background-color: #e8f4f8;
            border-radius: 5px;
        }
        footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.9em;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <h1>Productieproces Analyse - Visualisaties</h1>
    
    <div class="graph-container">
        <h2>1. Pareto Analyse: Defectpercentage per Verpakkingsmachine</h2>
        <div class="graph">
            <img src="visualisaties/pareto_defecten_per_machine.png" alt="Pareto Analyse: Defectpercentage per Verpakkingsmachine">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>Packager 1 heeft het hoogste defectpercentage (2,05%)</li>
                <li>Packager 4 heeft het laagste defectpercentage (2,00%)</li>
                <li>De cumulatieve lijn toont dat ongeveer 50% van alle defecten wordt veroorzaakt door de twee slechtst presterende machines</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Volgens het Pareto-principe (80/20-regel) zou het verbeteren van de top machines het grootste effect hebben</li>
                <li>Focus verbeterinspanningen op Packager 1 en Packager 5 voor maximale impact</li>
                <li>Hoewel de verschillen klein lijken, kan een verbetering van 0,05% bij grote productievolumes significant zijn</li>
            </ul>
        </div>
    </div>
    
    <div class="graph-container">
        <h2>2. Cyclustijd per Verpakkingsmachine</h2>
        <div class="graph">
            <img src="visualisaties/cyclustijd_per_machine.png" alt="Cyclustijd per Verpakkingsmachine">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>Packager 4 heeft de langste cyclustijd (4,13 uur)</li>
                <li>Packager 1 heeft ook een lange cyclustijd (4,13 uur)</li>
                <li>Onbekende machines hebben de kortste cyclustijd (4,04 uur)</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Packager 4 en Packager 1 zijn potentiële bottlenecks in het productieproces</li>
                <li>Zelfs kleine verbeteringen in cyclustijd kunnen de totale productiecapaciteit verhogen</li>
                <li>Onderzoek waarom 'Onbekend' machines efficiënter zijn en pas deze lessen toe op andere machines</li>
            </ul>
        </div>
    </div>
    
    <div class="graph-container">
        <h2>3. Benuttingsgraad per Verpakkingsmachine</h2>
        <div class="graph">
            <img src="visualisaties/benutting_per_machine.png" alt="Benuttingsgraad per Verpakkingsmachine">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>Packager 1 heeft de laagste benuttingsgraad (74,59%)</li>
                <li>Packager 3 heeft de hoogste benuttingsgraad (75,33%)</li>
                <li>Alle machines hebben een benuttingsgraad tussen 74,5% en 75,5%</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Er is ruimte voor verbetering in de benuttingsgraad van alle machines</li>
                <li>Packager 1 verdient speciale aandacht (laagste benuttingsgraad én hoogste defectpercentage)</li>
                <li>Onderzoek waarom Packager 3 een hogere benuttingsgraad heeft en pas deze lessen toe op andere machines</li>
            </ul>
        </div>
    </div>
    
    <div class="graph-container">
        <h2>4. Energieverbruik per Verpakkingsmachine</h2>
        <div class="graph">
            <img src="visualisaties/energie_per_machine.png" alt="Energieverbruik per Verpakkingsmachine">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>De categorie 'Onbekend' heeft een extreem hoog energieverbruik (24.835,5 kWh)</li>
                <li>Packager 4 heeft het laagste energieverbruik onder de bekende machines</li>
                <li>Er zijn grote verschillen in energieverbruik tussen de machines</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Identificeer welke machines onder 'Onbekend' vallen en onderzoek hun hoge energieverbruik</li>
                <li>Leer van de energie-efficiëntie van Packager 4 en pas deze toe op andere machines</li>
                <li>Energieverbruik verminderen kan zowel kosten als milieu-impact reduceren</li>
            </ul>
        </div>
    </div>
    
    <div class="graph-container">
        <h2>5. Duurzaamheidsscore per Verpakkingsmachine</h2>
        <div class="graph">
            <img src="visualisaties/duurzaamheid_per_machine.png" alt="Duurzaamheidsscore per Verpakkingsmachine">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>Packager 3 heeft de laagste duurzaamheidsscore (61,1)</li>
                <li>Packager 5 heeft de hoogste duurzaamheidsscore</li>
                <li>Er zijn significante verschillen in duurzaamheid tussen de machines</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Onderzoek welke factoren bijdragen aan de hogere duurzaamheidsscore van Packager 5</li>
                <li>Implementeer verbeteringen bij Packager 3 om de duurzaamheid te verhogen</li>
                <li>Duurzaamheidsverbeteringen kunnen leiden tot kostenbesparingen en een beter imago</li>
            </ul>
        </div>
    </div>
    
    <div class="graph-container">
        <h2>6. Correlatieheatmap van Kwaliteitsaspecten</h2>
        <div class="graph">
            <img src="visualisaties/correlatie_heatmap.png" alt="Correlatieheatmap">
        </div>
        <div class="findings">
            <h3>Belangrijkste bevindingen:</h3>
            <ul>
                <li>Er zijn geen sterke correlaties (>0,3 of <-0,3) gevonden tussen de kwaliteitsaspecten</li>
                <li>De meeste kwaliteitsaspecten lijken onafhankelijk van elkaar te zijn</li>
                <li>Er is geen duidelijk verband tussen defectpercentage en klanttevredenheid</li>
            </ul>
            <h3>Implicaties:</h3>
            <ul>
                <li>Verbeteringen in één aspect leiden niet automatisch tot verbeteringen in andere aspecten</li>
                <li>Een holistische aanpak is nodig om alle kwaliteitsaspecten te verbeteren</li>
                <li>Verder onderzoek naar onderliggende factoren die meerdere aspecten beïnvloeden is aan te bevelen</li>
            </ul>
        </div>
    </div>
    
    <div class="conclusion">
        <h2>Conclusies op basis van de visualisaties</h2>
        <ul>
            <li><strong>Six Sigma (Defectanalyse):</strong> Packager 1 heeft het hoogste defectpercentage (2,05%) en zou de focus moeten zijn voor kwaliteitsverbetering.</li>
            <li><strong>Lean (Doorlooptijden):</strong> Packager 4 en Packager 1 hebben de langste cyclustijden (4,13 uur) en zijn potentiële bottlenecks.</li>
            <li><strong>TOC (Bottlenecks):</strong> Packager 1 combineert de laagste benuttingsgraad met de hoogste defecten en langste cyclustijd, wat wijst op een kritisch verbeterpunt.</li>
            <li><strong>Kaizen (Verbetermogelijkheden):</strong> Het extreem hoge energieverbruik van 'Onbekend' machines vereist onmiddellijke aandacht.</li>
            <li><strong>TQM (Organisatiebrede aanpak):</strong> Er is geen duidelijke correlatie tussen verschillende kwaliteitsaspecten, wat wijst op de noodzaak van een geïntegreerde aanpak.</li>
        </ul>
    </div>
    
    <footer>
        <p>Deze visualisaties zijn gegenereerd op basis van de opgeschoonde Kikker.csv dataset en bieden inzicht in het productieproces en verbetermogelijkheden.</p>
    </footer>
</body>
</html>
