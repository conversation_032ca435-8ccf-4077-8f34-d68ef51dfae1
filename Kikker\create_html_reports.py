import os
import base64
import pandas as pd
import re

def read_csv_file(file_path):
    """Lees een CSV-bestand en geef de inhoud terug als een lijst met regels."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return lines
    except Exception as e:
        print(f"Fout bij lezen van {file_path}: {e}")
        return []

def image_to_base64(image_path):
    """Converteer een afbeelding naar een base64-gecodeerde string."""
    try:
        with open(image_path, 'rb') as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except Exception as e:
        print(f"Fout bij converteren van afbeelding {image_path}: {e}")
        return ""

def create_html_report(csv_file, title):
    """Maak een HTML-rapport op basis van een CSV-bestand."""
    lines = read_csv_file(csv_file)
    if not lines:
        return
    
    # Maak een HTML-bestand
    html_file = csv_file.replace(".csv", ".html")
    
    # Begin met de HTML-structuur
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #2c3e50; }}
        h2 {{ color: #3498db; margin-top: 30px; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .visualization {{ margin: 20px 0; text-align: center; }}
        .visualization img {{ max-width: 100%; height: auto; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
"""
    
    # Verwerk de CSV-inhoud
    in_table = False
    in_visualizations = False
    table_data = []
    visualizations = []
    
    for line in lines:
        line = line.strip()
        
        # Sla lege regels over
        if not line:
            if in_table and table_data:
                # Sluit de huidige tabel af
                html_content += create_table_html(table_data)
                table_data = []
                in_table = False
            continue
        
        # Controleer of we in de visualisaties sectie zijn
        if line == "Visualisaties:":
            in_visualizations = True
            in_table = False
            if table_data:
                html_content += create_table_html(table_data)
                table_data = []
            html_content += f"<h2>Visualisaties</h2>\n"
            continue
        
        if in_visualizations:
            # Verwerk visualisatie regel
            match = re.search(r'- (.*): (.*)', line)
            if match:
                vis_title = match.group(1)
                vis_path = match.group(2)
                visualizations.append((vis_title, vis_path))
            continue
        
        # Controleer of dit een sectie-header is
        if line.isupper() or (not ':' in line and not '-' in line and not '\t' in line and not ',' in line):
            if in_table and table_data:
                # Sluit de huidige tabel af
                html_content += create_table_html(table_data)
                table_data = []
            
            html_content += f"<h2>{line}</h2>\n"
            in_table = False
            continue
        
        # Controleer of dit een tabel-header is
        if ':' in line and not '-' in line and not in_table:
            if table_data:
                html_content += create_table_html(table_data)
                table_data = []
            
            section_title = line.split(':', 1)[0].strip()
            html_content += f"<h3>{section_title}</h3>\n"
            in_table = True
            continue
        
        # Verwerk tabelregel
        if '\t' in line:
            parts = line.split('\t')
            table_data.append(parts)
        elif ',' in line:
            parts = line.split(',')
            table_data.append(parts)
        elif '-' in line and not line.startswith('#'):
            parts = line.split('-', 1)
            if len(parts) == 2:
                category = parts[0].strip()
                value = parts[1].strip()
                table_data.append([category, value])
    
    # Sluit eventuele openstaande tabel
    if in_table and table_data:
        html_content += create_table_html(table_data)
    
    # Voeg visualisaties toe
    for vis_title, vis_path in visualizations:
        base64_img = image_to_base64(vis_path)
        if base64_img:
            img_type = "png"  # Aanname: alle visualisaties zijn PNG-bestanden
            html_content += f"""
    <div class="visualization">
        <h3>{vis_title}</h3>
        <img src="data:image/{img_type};base64,{base64_img}" alt="{vis_title}">
    </div>
"""
    
    # Sluit de HTML-structuur af
    html_content += """
</body>
</html>
"""
    
    # Schrijf het HTML-bestand
    try:
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML-rapport succesvol gemaakt: {html_file}")
    except Exception as e:
        print(f"Fout bij schrijven van HTML-rapport {html_file}: {e}")

def create_table_html(table_data):
    """Maak HTML-code voor een tabel op basis van tabelgegevens."""
    if not table_data:
        return ""
    
    html = "<table>\n"
    
    # Voeg tabelkop toe als de eerste rij kolomnamen bevat
    if len(table_data) > 0 and "Categorie" in table_data[0][0]:
        html += "  <tr>\n"
        for header in table_data[0]:
            html += f"    <th>{header}</th>\n"
        html += "  </tr>\n"
        start_idx = 1
    else:
        # Voeg standaard kolomnamen toe
        html += "  <tr>\n    <th>Categorie</th>\n    <th>Waarde</th>\n  </tr>\n"
        start_idx = 0
    
    # Voeg tabelrijen toe
    for row in table_data[start_idx:]:
        html += "  <tr>\n"
        for cell in row:
            html += f"    <td>{cell}</td>\n"
        html += "  </tr>\n"
    
    html += "</table>\n"
    return html

def main():
    """Hoofdfunctie om HTML-rapporten te maken voor alle analyses."""
    print("HTML-rapporten maken...")
    
    # Maak HTML-rapporten voor elke analyse
    create_html_report("Kaizen_Analyse_Clean.csv", "Kaizen Analyse")
    create_html_report("Lean_Analyse_Clean.csv", "Lean Analyse")
    create_html_report("SixSigma_Analyse_Clean.csv", "Six Sigma Analyse")
    create_html_report("TOC_Analyse_Clean.csv", "Theory of Constraints Analyse")
    
    print("Alle HTML-rapporten zijn gemaakt.")

if __name__ == "__main__":
    main()
