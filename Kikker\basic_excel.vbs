Option Explicit

' <PERSON><PERSON> eenvoudig script om een Excel-bestand te maken

Sub Main()
    Dim excel, wb, ws
    
    ' Maak een Excel-applicatie
    Set excel = CreateObject("Excel.Application")
    excel.Visible = True
    
    ' Maak een nieuw Excel-bestand
    Set wb = excel.Workbooks.Add
    Set ws = wb.Sheets(1)
    
    ' Voeg wat data toe
    ws.Cells(1, 1).Value = "Test"
    ws.Cells(1, 2).Value = "Data"
    
    ' Sla het Excel-bestand op
    wb.SaveAs "Test.xlsx"
    
    WScript.Echo "Bestand opgeslagen als Test.xlsx"
End Sub

' Start het script
Call Main()
