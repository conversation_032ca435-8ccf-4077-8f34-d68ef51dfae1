<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-15T10:00:00.000Z" agent="5.0" etag="abc123" version="22.1.16" type="device">
  <diagram id="swimlane1" name="EuroCaps Order Management Process Flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Swimlanes -->
        <mxCell id="lane1" value="User (All Roles)" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="20" y="40" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="lane2" value="Operations Manager" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="20" y="160" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="lane3" value="Purchasing Manager" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="20" y="280" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="lane4" value="Production Employee" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="20" y="400" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="lane5" value="Logistics Manager" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="20" y="520" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="lane6" value="System" style="swimlane;html=1;startSize=20;horizontal=0;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="20" y="640" width="1120" height="120" as="geometry" />
        </mxCell>
        
        <!-- Start Event -->
        <mxCell id="start" value="Start" style="ellipse;whiteSpace=wrap;html=1;fillColor=#60a917;strokeColor=#2D7600;fontColor=#ffffff;" vertex="1" parent="lane1">
          <mxGeometry x="40" y="35" width="60" height="50" as="geometry" />
        </mxCell>
        
        <!-- Process Steps -->
        <!-- Login Process -->
        <mxCell id="login" value="Login Screen&#xa;Enter Credentials" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="lane1">
          <mxGeometry x="130" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="authenticate" value="Validate&#xa;Credentials" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="lane6">
          <mxGeometry x="130" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Dashboard -->
        <mxCell id="dashboard" value="View Dashboard&#xa;Monitor KPIs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="lane2">
          <mxGeometry x="260" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Customer Management -->
        <mxCell id="viewCustomers" value="View Customer&#xa;List Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="lane2">
          <mxGeometry x="390" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Product Catalog -->
        <mxCell id="viewProducts" value="View Product&#xa;Catalog Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane3">
          <mxGeometry x="390" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Order Creation -->
        <mxCell id="createOrder" value="Create New&#xa;Order Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane3">
          <mxGeometry x="520" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="selectCustomer" value="Select Customer&#xa;from List" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane3">
          <mxGeometry x="650" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="addProducts" value="Add Products&#xa;to Order" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane3">
          <mxGeometry x="780" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Order Item Management -->
        <mxCell id="manageItems" value="Manage Order&#xa;Items Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="lane4">
          <mxGeometry x="780" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Order Details -->
        <mxCell id="orderDetails" value="View Order&#xa;Details Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane5">
          <mxGeometry x="910" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Order Confirmation -->
        <mxCell id="confirmOrder" value="Order Confirmation&#xa;Screen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane5">
          <mxGeometry x="1040" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- System Processes -->
        <mxCell id="saveOrder" value="Save Order&#xa;to Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="lane6">
          <mxGeometry x="910" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="sendConfirmation" value="Send Order&#xa;Confirmation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="lane6">
          <mxGeometry x="1040" y="30" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Decision Points -->
        <mxCell id="decision1" value="Valid&#xa;Login?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="lane6">
          <mxGeometry x="250" y="25" width="80" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="decision2" value="Order&#xa;Complete?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="lane6">
          <mxGeometry x="850" y="25" width="80" height="70" as="geometry" />
        </mxCell>
        
        <!-- End Event -->
        <mxCell id="end" value="End" style="ellipse;whiteSpace=wrap;html=1;fillColor=#a20025;strokeColor=#6F0000;fontColor=#ffffff;" vertex="1" parent="lane6">
          <mxGeometry x="1070" y="35" width="60" height="50" as="geometry" />
        </mxCell>
        
        <!-- Arrows/Flow -->
        <mxCell id="flow1" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="start" target="login">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow2" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="login" target="authenticate">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow3" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="authenticate" target="decision1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow4" value="Yes" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="decision1" target="dashboard">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow5" value="No" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="decision1" target="login">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow6" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="dashboard" target="viewCustomers">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow7" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="viewCustomers" target="viewProducts">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow8" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="viewProducts" target="createOrder">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow9" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="createOrder" target="selectCustomer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow10" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="selectCustomer" target="addProducts">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow11" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="addProducts" target="manageItems">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow12" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="manageItems" target="decision2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow13" value="Yes" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="decision2" target="orderDetails">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow14" value="No" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="decision2" target="addProducts">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow15" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="orderDetails" target="saveOrder">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow16" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="saveOrder" target="confirmOrder">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow17" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="confirmOrder" target="sendConfirmation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow18" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="sendConfirmation" target="end">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
