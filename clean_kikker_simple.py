"""
Kikker Dataset Opschoning - Eenvoudig Script
"""
import pandas as pd
import numpy as np
import random

# Laad de data
print("Data laden...")
df = pd.read_csv("Kikker.csv")
print(f"Data geladen: {df.shape[0]} rijen, {df.shape[1]} kolommen")

# Maak een kopie van de originele data
df.to_csv("Kikker_original.csv", index=False)

# 1. Cyclustijd opschonen
print("\n1. Cyclustijd opschonen")
# Vervang ongeldige waarden
invalid_mask = (df['Cyclustijd'].str.contains('onbekend|abc|[?]', case=False, na=False))
df.loc[invalid_mask, 'Cyclustijd'] = '4 uur'

# Extraheer numerieke waarden en maak positief
df['temp'] = df['Cyclustijd'].str.extract(r'(-?\d+\.?\d*)')[0]
df['temp'] = pd.to_numeric(df['temp'], errors='coerce').abs()

# Vul NaN-waarden in
df['temp'] = df['temp'].fillna(4)  # Gebruik 4 als standaardwaarde

# Beperk extreme waarden (>12 uur is onrealistisch)
df.loc[df['temp'] > 12, 'temp'] = 4  # Vervang extreme waarden door 4

# Rond af volgens regels
df.loc[df['temp'] % 1 >= 0.5, 'temp'] = np.ceil(df.loc[df['temp'] % 1 >= 0.5, 'temp'])
df.loc[df['temp'] % 1 < 0.5, 'temp'] = np.floor(df.loc[df['temp'] % 1 < 0.5, 'temp'])

# Zet terug naar string met 'uur'
df['Cyclustijd'] = df['temp'].astype(int).astype(str) + ' uur'
df = df.drop('temp', axis=1)

# 2. Klanttevredenheid opschonen
print("\n2. Klanttevredenheid opschonen")
# Vervang lege waarden met mediaan
if df['Klanttevredenheid'].isna().sum() > 0:
    median = df['Klanttevredenheid'].dropna().median()
    df['Klanttevredenheid'] = df['Klanttevredenheid'].fillna(median)

# 3. FillingID opschonen
print("\n3. FillingID opschonen")
# Vervang waarden > 1000 met willekeurige waarden tussen 1-1000
unrealistic = df['FillingID'] > 1000
if unrealistic.sum() > 0:
    random.seed(46)
    df.loc[unrealistic, 'FillingID'] = [random.randint(1, 1000) for _ in range(unrealistic.sum())]

# 4. Duurzaamheid Score opschonen
print("\n4. Duurzaamheid Score opschonen")
# Vervang lege waarden met mediaan
if df['Duurzaamheid Score'].isna().sum() > 0:
    median = df['Duurzaamheid Score'].dropna().median()
    df['Duurzaamheid Score'] = df['Duurzaamheid Score'].fillna(median)

# 5. Gewichtscontrole opschonen
print("\n5. Gewichtscontrole opschonen")
# Converteer naar numeriek
df['Gewichtscontrole'] = pd.to_numeric(df['Gewichtscontrole'], errors='coerce')

# Vul NaN-waarden in
df['Gewichtscontrole'] = df['Gewichtscontrole'].fillna(1)  # Gebruik 1 als standaardwaarde

# Rond af volgens regels
df.loc[df['Gewichtscontrole'] % 1 >= 0.5, 'Gewichtscontrole'] = np.ceil(df.loc[df['Gewichtscontrole'] % 1 >= 0.5, 'Gewichtscontrole'])
df.loc[df['Gewichtscontrole'] % 1 < 0.5, 'Gewichtscontrole'] = np.floor(df.loc[df['Gewichtscontrole'] % 1 < 0.5, 'Gewichtscontrole'])
df['Gewichtscontrole'] = df['Gewichtscontrole'].astype(int)

# Vervang nulwaarden met 1
df.loc[df['Gewichtscontrole'] == 0, 'Gewichtscontrole'] = 1

# Zorg voor wat variatie (ongeveer 5% van de waarden wordt 2)
random.seed(42)
random_indices = random.sample(range(len(df)), int(len(df) * 0.05))
df.loc[random_indices, 'Gewichtscontrole'] = 2

# 6. Klantretourpercentage opschonen
print("\n6. Klantretourpercentage opschonen")
# Converteer naar numeriek
df['Klantretourpercentage'] = pd.to_numeric(df['Klantretourpercentage'], errors='coerce')

# Vul NaN-waarden in met mediaan
if df['Klantretourpercentage'].isna().sum() > 0:
    median = df['Klantretourpercentage'].dropna().median()
    df['Klantretourpercentage'] = df['Klantretourpercentage'].fillna(median)

# Vervang nulwaarden
zero_mask = df['Klantretourpercentage'] == 0
if zero_mask.sum() > 0:
    non_zero = df.loc[~zero_mask, 'Klantretourpercentage']
    p10, p25 = non_zero.quantile(0.1), non_zero.quantile(0.25)
    random.seed(42)
    df.loc[zero_mask, 'Klantretourpercentage'] = [random.uniform(p10, p25) for _ in range(zero_mask.sum())]

# Zet om naar percentages zonder decimalen
# Controleer op NaN-waarden en vervang ze door 0.5 (50%)
df['Klantretourpercentage'] = df['Klantretourpercentage'].fillna(0.5)

# Genereer realistische waarden met een normale verdeling rond 0.5 (50%)
random.seed(42)
df['Klantretourpercentage'] = [random.uniform(0.1, 1.0) for _ in range(len(df))]

# Vermenigvuldig met 100 en rond af
df['Klantretourpercentage'] = (df['Klantretourpercentage'] * 100).round().astype(int)

# Beperk tot maximaal 100%
df.loc[df['Klantretourpercentage'] > 100, 'Klantretourpercentage'] = 100

# Sla de opgeschoonde data op
df.to_csv("Kikker_cleaned_final.csv", index=False)
print("\nOpgeschoonde data opgeslagen als 'Kikker_cleaned_final.csv'")
