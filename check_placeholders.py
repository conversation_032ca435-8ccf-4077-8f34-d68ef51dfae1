import pandas as pd
import numpy as np

# Load the cleaned dataset
print("Loading cleaned data...")
df = pd.read_csv('Kikker_cleaned.csv')
print(f"Loaded data with {df.shape[0]} rows and {df.shape[1]} columns.")

# Check for placeholder values in numeric columns
print("\nChecking for placeholder values in numeric columns...")
placeholder_values = [999999, 9999999, 9999999.99]

for col in df.columns:
    # Try to convert to numeric
    try:
        # Check if column contains numeric data
        numeric_col = pd.to_numeric(df[col], errors='coerce')
        
        # Check for each placeholder value
        for placeholder in placeholder_values:
            count = (numeric_col == placeholder).sum()
            if count > 0:
                print(f"Found {count} instances of {placeholder} in column '{col}'")
    except:
        pass

# Check for placeholder values in string columns
print("\nChecking for placeholder values in string columns...")
placeholder_strings = ['999999', '9999999', '9999999.99']

for col in df.columns:
    if df[col].dtype == 'object':  # Only check string columns
        for placeholder in placeholder_strings:
            count = (df[col] == placeholder).sum()
            if count > 0:
                print(f"Found {count} instances of '{placeholder}' in column '{col}'")

print("\nCheck complete.")
