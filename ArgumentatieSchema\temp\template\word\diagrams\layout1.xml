<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<dgm:layoutDef xmlns:dgm="http://schemas.openxmlformats.org/drawingml/2006/diagram" xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" uniqueId="urn:microsoft.com/office/officeart/2005/8/layout/process4"><dgm:title val=""/><dgm:desc val=""/><dgm:catLst><dgm:cat type="process" pri="16000"/><dgm:cat type="list" pri="20000"/></dgm:catLst><dgm:sampData><dgm:dataModel><dgm:ptLst><dgm:pt modelId="0" type="doc"/><dgm:pt modelId="1"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="11"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="12"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="2"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="21"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="22"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="3"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="31"><dgm:prSet phldr="1"/></dgm:pt><dgm:pt modelId="32"><dgm:prSet phldr="1"/></dgm:pt></dgm:ptLst><dgm:cxnLst><dgm:cxn modelId="4" srcId="0" destId="1" srcOrd="0" destOrd="0"/><dgm:cxn modelId="5" srcId="0" destId="2" srcOrd="1" destOrd="0"/><dgm:cxn modelId="6" srcId="0" destId="3" srcOrd="2" destOrd="0"/><dgm:cxn modelId="13" srcId="1" destId="11" srcOrd="0" destOrd="0"/><dgm:cxn modelId="14" srcId="1" destId="12" srcOrd="1" destOrd="0"/><dgm:cxn modelId="23" srcId="2" destId="21" srcOrd="0" destOrd="0"/><dgm:cxn modelId="24" srcId="2" destId="22" srcOrd="1" destOrd="0"/><dgm:cxn modelId="33" srcId="3" destId="31" srcOrd="0" destOrd="0"/><dgm:cxn modelId="34" srcId="3" destId="32" srcOrd="1" destOrd="0"/></dgm:cxnLst><dgm:bg/><dgm:whole/></dgm:dataModel></dgm:sampData><dgm:styleData><dgm:dataModel><dgm:ptLst><dgm:pt modelId="0" type="doc"/><dgm:pt modelId="1"/><dgm:pt modelId="11"/><dgm:pt modelId="2"/><dgm:pt modelId="21"/></dgm:ptLst><dgm:cxnLst><dgm:cxn modelId="4" srcId="0" destId="1" srcOrd="0" destOrd="0"/><dgm:cxn modelId="5" srcId="0" destId="2" srcOrd="1" destOrd="0"/><dgm:cxn modelId="13" srcId="1" destId="11" srcOrd="0" destOrd="0"/><dgm:cxn modelId="23" srcId="2" destId="21" srcOrd="0" destOrd="0"/></dgm:cxnLst><dgm:bg/><dgm:whole/></dgm:dataModel></dgm:styleData><dgm:clrData><dgm:dataModel><dgm:ptLst><dgm:pt modelId="0" type="doc"/><dgm:pt modelId="1"/><dgm:pt modelId="11"/><dgm:pt modelId="2"/><dgm:pt modelId="21"/><dgm:pt modelId="3"/><dgm:pt modelId="31"/><dgm:pt modelId="4"/><dgm:pt modelId="41"/></dgm:ptLst><dgm:cxnLst><dgm:cxn modelId="5" srcId="0" destId="1" srcOrd="0" destOrd="0"/><dgm:cxn modelId="6" srcId="0" destId="2" srcOrd="1" destOrd="0"/><dgm:cxn modelId="7" srcId="0" destId="3" srcOrd="2" destOrd="0"/><dgm:cxn modelId="8" srcId="0" destId="4" srcOrd="3" destOrd="0"/><dgm:cxn modelId="13" srcId="1" destId="11" srcOrd="0" destOrd="0"/><dgm:cxn modelId="23" srcId="2" destId="21" srcOrd="0" destOrd="0"/><dgm:cxn modelId="33" srcId="3" destId="31" srcOrd="0" destOrd="0"/><dgm:cxn modelId="43" srcId="4" destId="41" srcOrd="0" destOrd="0"/></dgm:cxnLst><dgm:bg/><dgm:whole/></dgm:dataModel></dgm:clrData><dgm:layoutNode name="Name0"><dgm:varLst><dgm:dir/><dgm:animLvl val="lvl"/><dgm:resizeHandles val="exact"/></dgm:varLst><dgm:alg type="lin"><dgm:param type="linDir" val="fromB"/></dgm:alg><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf/><dgm:constrLst><dgm:constr type="h" for="ch" forName="boxAndChildren" refType="h"/><dgm:constr type="h" for="ch" forName="arrowAndChildren" refType="h" refFor="ch" refForName="boxAndChildren" op="equ" fact="1.538"/><dgm:constr type="w" for="ch" forName="arrowAndChildren" refType="w"/><dgm:constr type="w" for="ch" forName="boxAndChildren" refType="w"/><dgm:constr type="h" for="ch" forName="sp" refType="h" fact="-0.015"/><dgm:constr type="primFontSz" for="des" forName="parentTextBox" val="65"/><dgm:constr type="primFontSz" for="des" forName="parentTextArrow" refType="primFontSz" refFor="des" refForName="parentTextBox" op="equ"/><dgm:constr type="primFontSz" for="des" forName="childTextArrow" val="65"/><dgm:constr type="primFontSz" for="des" forName="childTextBox" refType="primFontSz" refFor="des" refForName="childTextArrow" op="equ"/></dgm:constrLst><dgm:ruleLst/><dgm:forEach name="Name1" axis="ch" ptType="node" st="-1" step="-1"><dgm:choose name="Name2"><dgm:if name="Name3" axis="self" ptType="node" func="revPos" op="equ" val="1"><dgm:layoutNode name="boxAndChildren"><dgm:alg type="composite"/><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf/><dgm:choose name="Name4"><dgm:if name="Name5" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:constrLst><dgm:constr type="w" for="ch" forName="parentTextBox" refType="w"/><dgm:constr type="h" for="ch" forName="parentTextBox" refType="h" fact="0.54"/><dgm:constr type="t" for="ch" forName="parentTextBox"/><dgm:constr type="w" for="ch" forName="entireBox" refType="w"/><dgm:constr type="h" for="ch" forName="entireBox" refType="h"/><dgm:constr type="w" for="ch" forName="descendantBox" refType="w"/><dgm:constr type="b" for="ch" forName="descendantBox" refType="h" fact="0.98"/><dgm:constr type="h" for="ch" forName="descendantBox" refType="h" fact="0.46"/></dgm:constrLst></dgm:if><dgm:else name="Name6"><dgm:constrLst><dgm:constr type="w" for="ch" forName="parentTextBox" refType="w"/><dgm:constr type="h" for="ch" forName="parentTextBox" refType="h"/></dgm:constrLst></dgm:else></dgm:choose><dgm:ruleLst/><dgm:layoutNode name="parentTextBox"><dgm:alg type="tx"/><dgm:choose name="Name7"><dgm:if name="Name8" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip="" zOrderOff="1" hideGeom="1"><dgm:adjLst/></dgm:shape></dgm:if><dgm:else name="Name9"><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape></dgm:else></dgm:choose><dgm:presOf axis="self"/><dgm:constrLst/><dgm:ruleLst><dgm:rule type="primFontSz" val="5" fact="NaN" max="NaN"/></dgm:ruleLst></dgm:layoutNode><dgm:choose name="Name10"><dgm:if name="Name11" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:layoutNode name="entireBox"><dgm:alg type="sp"/><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf axis="self"/><dgm:constrLst/><dgm:ruleLst/></dgm:layoutNode><dgm:layoutNode name="descendantBox" styleLbl="fgAccFollowNode1"><dgm:choose name="Name12"><dgm:if name="Name13" func="var" arg="dir" op="equ" val="norm"><dgm:alg type="lin"/></dgm:if><dgm:else name="Name14"><dgm:alg type="lin"><dgm:param type="linDir" val="fromR"/></dgm:alg></dgm:else></dgm:choose><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf/><dgm:constrLst><dgm:constr type="w" for="ch" forName="childTextBox" refType="w"/><dgm:constr type="h" for="ch" forName="childTextBox" refType="h"/></dgm:constrLst><dgm:ruleLst/><dgm:forEach name="Name15" axis="ch" ptType="node"><dgm:layoutNode name="childTextBox" styleLbl="fgAccFollowNode1"><dgm:varLst><dgm:bulletEnabled val="1"/></dgm:varLst><dgm:alg type="tx"/><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf axis="desOrSelf" ptType="node"/><dgm:constrLst><dgm:constr type="tMarg" refType="primFontSz" fact="0.1"/><dgm:constr type="bMarg" refType="primFontSz" fact="0.1"/></dgm:constrLst><dgm:ruleLst><dgm:rule type="primFontSz" val="5" fact="NaN" max="NaN"/></dgm:ruleLst></dgm:layoutNode></dgm:forEach></dgm:layoutNode></dgm:if><dgm:else name="Name16"/></dgm:choose></dgm:layoutNode></dgm:if><dgm:else name="Name17"><dgm:layoutNode name="arrowAndChildren"><dgm:alg type="composite"/><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf/><dgm:choose name="Name18"><dgm:if name="Name19" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:constrLst><dgm:constr type="w" for="ch" forName="parentTextArrow" refType="w"/><dgm:constr type="t" for="ch" forName="parentTextArrow"/><dgm:constr type="h" for="ch" forName="parentTextArrow" refType="h" fact="0.351"/><dgm:constr type="w" for="ch" forName="arrow" refType="w"/><dgm:constr type="h" for="ch" forName="arrow" refType="h"/><dgm:constr type="w" for="ch" forName="descendantArrow" refType="w"/><dgm:constr type="b" for="ch" forName="descendantArrow" refType="h" fact="0.65"/><dgm:constr type="h" for="ch" forName="descendantArrow" refType="h" fact="0.299"/></dgm:constrLst></dgm:if><dgm:else name="Name20"><dgm:constrLst><dgm:constr type="w" for="ch" forName="parentTextArrow" refType="w"/><dgm:constr type="h" for="ch" forName="parentTextArrow" refType="h"/></dgm:constrLst></dgm:else></dgm:choose><dgm:ruleLst/><dgm:layoutNode name="parentTextArrow"><dgm:alg type="tx"/><dgm:choose name="Name21"><dgm:if name="Name22" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip="" zOrderOff="1" hideGeom="1"><dgm:adjLst/></dgm:shape></dgm:if><dgm:else name="Name23"><dgm:shape rot="180" type="upArrowCallout" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape></dgm:else></dgm:choose><dgm:presOf axis="self"/><dgm:constrLst/><dgm:ruleLst><dgm:rule type="primFontSz" val="5" fact="NaN" max="NaN"/></dgm:ruleLst></dgm:layoutNode><dgm:choose name="Name24"><dgm:if name="Name25" axis="ch" ptType="node" func="cnt" op="gte" val="1"><dgm:layoutNode name="arrow"><dgm:alg type="sp"/><dgm:shape rot="180" type="upArrowCallout" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf axis="self"/><dgm:constrLst/><dgm:ruleLst/></dgm:layoutNode><dgm:layoutNode name="descendantArrow"><dgm:choose name="Name26"><dgm:if name="Name27" func="var" arg="dir" op="equ" val="norm"><dgm:alg type="lin"/></dgm:if><dgm:else name="Name28"><dgm:alg type="lin"><dgm:param type="linDir" val="fromR"/></dgm:alg></dgm:else></dgm:choose><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf/><dgm:constrLst><dgm:constr type="w" for="ch" forName="childTextArrow" refType="w"/><dgm:constr type="h" for="ch" forName="childTextArrow" refType="h"/></dgm:constrLst><dgm:ruleLst/><dgm:forEach name="Name29" axis="ch" ptType="node"><dgm:layoutNode name="childTextArrow" styleLbl="fgAccFollowNode1"><dgm:varLst><dgm:bulletEnabled val="1"/></dgm:varLst><dgm:alg type="tx"/><dgm:shape type="rect" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf axis="desOrSelf" ptType="node"/><dgm:constrLst><dgm:constr type="tMarg" refType="primFontSz" fact="0.1"/><dgm:constr type="bMarg" refType="primFontSz" fact="0.1"/></dgm:constrLst><dgm:ruleLst><dgm:rule type="primFontSz" val="5" fact="NaN" max="NaN"/></dgm:ruleLst></dgm:layoutNode></dgm:forEach></dgm:layoutNode></dgm:if><dgm:else name="Name30"/></dgm:choose></dgm:layoutNode></dgm:else></dgm:choose><dgm:forEach name="Name31" axis="precedSib" ptType="sibTrans" st="-1" cnt="1"><dgm:layoutNode name="sp"><dgm:alg type="sp"/><dgm:shape xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" r:blip=""><dgm:adjLst/></dgm:shape><dgm:presOf axis="self"/><dgm:constrLst/><dgm:ruleLst/></dgm:layoutNode></dgm:forEach></dgm:forEach></dgm:layoutNode></dgm:layoutDef>