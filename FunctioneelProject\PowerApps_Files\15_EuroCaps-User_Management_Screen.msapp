{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps User Management", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "UserManagementScreen", "Controls": [{"Name": "RoleFilterDropdown", "ControlType": "Dropdown", "Items": "[\"All Roles\", \"Operations Manager\", \"Purchasing Manager\", \"Production Employee\", \"Admin\"]"}, {"Name": "StatusFilterDropdown", "ControlType": "Dropdown", "Items": "[\"All Status\", \"Active\", \"Inactive\", \"Pending\"]"}, {"Name": "UsersGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "Filter(Users, (RoleFilter = \"All Roles\" || Role = RoleFilter) && (StatusFilter = \"All Status\" || Status = StatusFilter))"}, {"Name": "NewUserButton", "ControlType": "<PERSON><PERSON>", "Text": "➕ NEW USER", "Fill": "RGBA(16, 124, 16, 1)", "Visible": "User().Role = \"Admin\""}, {"Name": "ExportUsersButton", "ControlType": "<PERSON><PERSON>", "Text": "📤 Export Users", "Fill": "RGBA(0, 120, 212, 1)"}, {"Name": "UserStatsContainer", "ControlType": "Rectangle", "Fill": "RGBA(255, 255, 255, 1)"}]}], "DataSources": [{"Name": "Users", "Type": "Excel"}], "Variables": [{"Name": "<PERSON>Filter", "Type": "Text", "DefaultValue": "All Roles"}, {"Name": "StatusFilter", "Type": "Text", "DefaultValue": "All Status"}]}