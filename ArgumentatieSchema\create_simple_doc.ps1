# PowerShell script om een nieuw Word-document te maken met de argumenten

# Word COM object aanmaken
$word = New-Object -ComObject Word.Application
$word.Visible = $true

# Nieuw document maken
$document = $word.Documents.Add()

# Pad naar het argumenten bestand
$argumentsPath = Join-Path $PSScriptRoot "argumenten.txt"
$arguments = Get-Content -Path $argumentsPath -Raw

# Tekst toevoegen aan het document
$selection = $word.Selection
$selection.TypeText($arguments)

# Document opslaan
$outputPath = Join-Path $PSScriptRoot "Argumentatieschema_Bol_Data_Coaching_Nieuw.docx"
$document.SaveAs2($outputPath)

# Document sluiten
$document.Close()
$word.Quit()

# COM objecten vrijgeven
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($document) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Document is succesvol aangemaakt: $outputPath"
