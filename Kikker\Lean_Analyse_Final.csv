LEAN ANALYSE,VISUALISATIES
Verspilling in procestijd en voorraadniveaus,
,
Cyclustijd Statistieken:,Staafdiagram: Benuttingsgraad per Verpakkingsmachine
- Gemiddelde: 4.10 uur,
- Mediaan: 4.07 uur,Target (85%) |------------------------------------------->
- Standaarddeviatie: 0.75 uur,
,Packager 3   |███████████████████████████████████████| 75.33%
Benuttingsgraad per Verpakkingsmachine:,Onbekend     |██████████████████████████████████████▉| 75.15%
- Packager 1: 74.59%,Packager 2   |██████████████████████████████████████▉| 75.14%
- Packager 5: 74.59%,Packager 4   |██████████████████████████████████████▊| 74.92%
- Packager 4: 74.92%,Packager 1   |█████████████████████████████████████▊| 74.59%
- Packager 2: 75.14%,Packager 5   |█████████████████████████████████████▊| 74.59%
- Onbekend: 75.15%,
- Packager 3: 75.33%,
,
Voorraadniveaus Statistieken:,Value Stream Mapping:
- Gemiddelde: 311 eenheden,
- Mediaan: 300 eenheden,[Leverancier] --> [Voorraad] --> [Grinding] --> [Filling] --> [Packaging] --> [<PERSON><PERSON>]
,     |             ^              |            |             |
,     |             |              v            v             v
,     |         [Verspilling]  [Wachttijd]  [Defecten]    [Transport]
,     |             ^              ^            ^             ^
,     |             |              |            |             |
,     +-------------+--------------+------------+-------------+
,
Advies Lean:,7 Vormen van Verspilling (Muda):
Verminder verspilling door:,
1. Implementeer Value Stream Mapping om niet-waarde toevoegende activiteiten te identificeren,+-------------------+
2. Optimaliseer voorraadniveaus met Just-in-Time principes,| 1. Overproductie   |
3. Verbeter machines met lage benuttingsgraad,| 2. Wachttijd       |
4. Standaardiseer werkprocedures om cyclustijd te verkorten,| 3. Transport       |
5. Implementeer 5S methodologie op de werkvloer,| 4. Overbewerking   |
,| 5. Voorraad        |
,| 6. Beweging        |
,| 7. Defecten        |
,+-------------------+
