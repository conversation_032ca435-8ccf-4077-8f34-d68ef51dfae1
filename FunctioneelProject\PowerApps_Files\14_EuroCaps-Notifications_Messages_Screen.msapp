{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Notifications & Messages", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "NotificationsMessagesScreen", "Controls": [{"Name": "NotificationFilterDropdown", "ControlType": "Dropdown", "Items": "[\"All\", \"Unread\", \"Today\", \"This Week\"]"}, {"Name": "NotificationsGallery", "ControlType": "Gallery", "Layout": "Layout.Vertical", "Items": "SortByColumns(Filter(Notifications, FilterCondition), \"Timestamp\", Descending)"}, {"Name": "MarkAllReadButton", "ControlType": "<PERSON><PERSON>", "Text": "✅ <PERSON>", "Fill": "RGBA(16, 124, 16, 1)"}, {"Name": "ClearNotificationsButton", "ControlType": "<PERSON><PERSON>", "Text": "🗑️ Clear", "Fill": "RGBA(164, 38, 44, 1)"}, {"Name": "NotificationSettingsButton", "ControlType": "<PERSON><PERSON>", "Text": "⚙️ Settings", "Fill": "RGBA(96, 94, 92, 1)"}]}], "DataSources": [{"Name": "Notifications", "Type": "Collection"}, {"Name": "Orders", "Type": "Excel"}]}