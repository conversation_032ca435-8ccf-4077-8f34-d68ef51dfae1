{"FormatVersion": "0.24", "Properties": {"AppName": "EuroCaps Product Detail", "BackgroundColor": "RGBA(243, 242, 241, 1)"}, "Screens": [{"Name": "ProductDetailScreen", "Controls": [{"Name": "ProductImage", "ControlType": "Image", "Image": "ThisItem.ProductImage"}, {"Name": "ProductInfoContainer", "ControlType": "Rectangle", "Fill": "RGBA(255, 255, 255, 1)"}, {"Name": "QuantityDropdown", "ControlType": "Dropdown", "Items": "Sequence(Min(SelectedProduct.Stock, 10))"}, {"Name": "AddToCartButton", "ControlType": "<PERSON><PERSON>", "Text": "🛒 ADD TO CART", "Fill": "RGBA(0, 120, 212, 1)", "OnSelect": "Collect(CurrentOrder, {ProductID: SelectedProduct.ProductID, Quantity: QuantityDropdown.Selected.Value})"}, {"Name": "RelatedProductsGallery", "ControlType": "Gallery", "Layout": "Layout.FlexibleHeight", "Items": "Filter(Products, Category = SelectedProduct.Category && ProductID <> SelectedProduct.ProductID)", "WrapCount": 4}]}], "DataSources": [{"Name": "Products", "Type": "Excel"}, {"Name": "CurrentOrder", "Type": "Collection"}]}