// Fixed PowerApps Create Account Button Code
// OnSelect Property for Create_Account_button

// First, validate required fields
If(
    IsBlank(User_Name.Text) || 
    IsBlank(Email.Text) || 
    IsBlank(Hint_Password_2.Text) || 
    IsBlank(Hint__Confirm_Password.Text) || 
    IsBlank(First_name.Text) || 
    IsBlank(Last_name.Text) || 
    IsBlank(First_name_1.Text) || // Company Name field
    IsEmpty(Company_Type_dropdown.Selected),
    
    // Show error if required fields are missing
    Notify("Please fill in all required fields.", NotificationType.Error),
    
    // Check if passwords match
    If(
        Hint_Password_2.Text <> Hint__Confirm_Password.Text,
        Notify("Passwords do not match. Please try again.", NotificationType.Error),
        
        // All validations passed, proceed with saving
        Patch(
            Account_Creation_pages,
            Defaults(Account_Creation_pages),
            {
                Username: User_Name.Text,
                Email: Email.Text,
                Password: Hint_Password_2.Text,
                FirstName: First_name.Text,
                LastName: Last_name.Text,
                PhoneNumber: Phone_Number.Text,
                CompanyName: First_name_1.Text,  // This is the company name field
                CompanyPhoneNumber: Phone_Number_1.Text,
                CompanyType: Company_Type_dropdown.Selected.Value
            }
        );
        
        // Check for errors after patch operation
        If(
            CountRows(Errors(Account_Creation_pages)) > 0,
            Notify("An error occurred: " & First(Errors(Account_Creation_pages)).Message, NotificationType.Error),
            
            // Success - clear form and show success message
            Reset(User_Name);
            Reset(Email);
            Reset(Hint_Password_2);
            Reset(Hint__Confirm_Password);
            Reset(First_name);
            Reset(Last_name);
            Reset(Phone_Number);
            Reset(First_name_1);
            Reset(Phone_Number_1);
            Reset(Company_Type_dropdown);
            Notify("Account created successfully!", NotificationType.Success);
            
            // Optional: Navigate to login screen or dashboard
            // Navigate(Login_Screen, ScreenTransition.Fade)
        )
    )
);

// Alternative Simplified Version (if the above is too complex):
// ================================================================

// Simple version with basic validation
If(
    IsBlank(User_Name.Text) || IsBlank(Email.Text) || IsBlank(Hint_Password_2.Text),
    Notify("Please fill in required fields: Username, Email, and Password", NotificationType.Error),
    
    Patch(
        Account_Creation_pages,
        Defaults(Account_Creation_pages),
        {
            Username: User_Name.Text,
            Email: Email.Text,
            Password: Hint_Password_2.Text,
            FirstName: First_name.Text,
            LastName: Last_name.Text,
            PhoneNumber: Phone_Number.Text,
            CompanyName: First_name_1.Text,
            CompanyPhoneNumber: Phone_Number_1.Text,
            CompanyType: If(IsEmpty(Company_Type_dropdown.Selected), "", Company_Type_dropdown.Selected.Value)
        }
    );
    
    If(
        IsError(Last(Account_Creation_pages)),
        Notify("Error creating account. Please try again.", NotificationType.Error),
        Notify("Account created successfully!", NotificationType.Success)
    )
);

// Clear Form Button Fix:
// =====================
// For the Clear_form_button OnSelect property, replace 'false' with:

Reset(User_Name);
Reset(Email);
Reset(Hint_Password_2);
Reset(Hint__Confirm_Password);
Reset(First_name);
Reset(Last_name);
Reset(Phone_Number);
Reset(First_name_1);
Reset(Phone_Number_1);
Reset(Company_Type_dropdown);
Notify("Form cleared successfully.", NotificationType.Information);

// Data Source Setup:
// ==================
// Make sure your Account_Creation_pages data source has these columns:
// - Username (Single line of text)
// - Email (Single line of text)  
// - Password (Single line of text)
// - FirstName (Single line of text)
// - LastName (Single line of text)
// - PhoneNumber (Single line of text)
// - CompanyName (Single line of text)
// - CompanyPhoneNumber (Single line of text)
// - CompanyType (Single line of text or Choice)

// Additional Validation Functions:
// ================================

// Email validation function (can be added to Email field's OnChange):
If(
    !IsMatch(Email.Text, Match.Email),
    Notify("Please enter a valid email address.", NotificationType.Warning)
);

// Password strength validation (can be added to password field):
If(
    Len(Hint_Password_2.Text) < 8,
    Notify("Password must be at least 8 characters long.", NotificationType.Warning)
);

// Troubleshooting Tips:
// ====================
// 1. Check if Account_Creation_pages data source exists and is properly connected
// 2. Verify column names in your data source match the field names in Patch()
// 3. Make sure Company_Type_dropdown has proper Items and Value properties set
// 4. Test with simple Patch() first, then add validation
// 5. Use Monitor tool in PowerApps Studio to debug errors
// 6. Check if you have proper permissions to write to the data source
