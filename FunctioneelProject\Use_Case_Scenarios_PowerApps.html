<!DOCTYPE html>
<html>
<head>
    <title>Use <PERSON> Scenario's - EuroCaps Order Management App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
        }
        th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        td {
            padding: 10px;
            border: 1px solid #ddd;
            vertical-align: top;
            line-height: 1.4;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .use-case-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .actor {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Use Case Scenario's - EuroCaps Order Management App</h1>
    
    <table>
        <thead>
            <tr>
                <th style="width: 15%;">Naam Use <PERSON></th>
                <th style="width: 15%;">Actor</th>
                <th style="width: 15%;">Aannames</th>
                <th style="width: 25%;">Beschrijving</th>
                <th style="width: 15%;">Uitzonderingen</th>
                <th style="width: 15%;">Resultaat</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="use-case-name">Inloggen</td>
                <td class="actor">Gebruiker</td>
                <td>PowerApps is geïnstalleerd en systeem is operationeel</td>
                <td>1) Gebruiker opent de EuroCaps Order Management app<br>2) Gebruiker voert gebruikersnaam en wachtwoord in<br>3) Het systeem controleert authenticatie. Zo niet, dan treedt uitzondering [gebruiker niet bekend] op<br>4) Bij succesvolle login wordt het dashboard getoond</td>
                <td>[Gebruiker niet bekend] Foutmelding wordt getoond en gebruiker blijft op login screen<br>[App niet beschikbaar] Melding dat app niet kan worden geladen</td>
                <td>Gebruiker krijgt toegang tot het dashboard van de app</td>
            </tr>
            <tr>
                <td class="use-case-name">Dashboard bekijken</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Gebruiker is ingelogd in de app</td>
                <td>1) Gebruiker opent het dashboard screen<br>2) Het systeem toont KPI's en overzichten van orders<br>3) Gebruiker bekijkt recente orders en statistieken<br>4) Gebruiker kan navigeren naar andere modules via navigatiemenu</td>
                <td>[Geen data beschikbaar] Dashboard toont melding dat er geen data is<br>[Netwerk probleem] Dashboard kan niet worden geladen</td>
                <td>Dashboard wordt weergegeven met actuele bedrijfsinformatie en navigatiemogelijkheden</td>
            </tr>
            <tr>
                <td class="use-case-name">Klanten bekijken</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Gebruiker heeft toegang tot customer module</td>
                <td>1) Gebruiker navigeert naar Customer List screen<br>2) Het systeem toont lijst van alle klanten<br>3) Gebruiker kan klanten zoeken via zoekfunctie<br>4) Gebruiker selecteert klant om details te bekijken</td>
                <td>[Geen klanten gevonden] Melding dat er geen klanten in database staan<br>[Zoekresultaat leeg] Geen klanten gevonden met zoekterm</td>
                <td>Klantlijst wordt weergegeven en gebruiker kan klant selecteren</td>
            </tr>
            <tr>
                <td class="use-case-name">Producten bekijken</td>
                <td class="actor">Manager Inkoop</td>
                <td>Product catalog is gevuld met producten</td>
                <td>1) Gebruiker navigeert naar Product Catalog screen<br>2) Het systeem toont alle beschikbare producten<br>3) Gebruiker kan producten zoeken en filteren<br>4) Gebruiker bekijkt productdetails en prijzen</td>
                <td>[Geen producten beschikbaar] Melding dat product catalog leeg is<br>[Product niet gevonden] Zoekresultaat levert geen producten op</td>
                <td>Productcatalog wordt weergegeven met alle beschikbare producten</td>
            </tr>
            <tr>
                <td class="use-case-name">Nieuwe order aanmaken</td>
                <td class="actor">Manager Inkoop</td>
                <td>Klanten en producten zijn beschikbaar in het systeem</td>
                <td>1) Gebruiker navigeert naar New Order screen<br>2) Gebruiker selecteert klant uit customer list<br>3) Gebruiker voegt producten toe aan order<br>4) Het systeem controleert beschikbaarheid. Zo niet, dan treedt uitzondering [product niet beschikbaar] op<br>5) Gebruiker slaat order op</td>
                <td>[Product niet beschikbaar] Melding dat product niet op voorraad is<br>[Klant niet geselecteerd] Foutmelding dat klant moet worden gekozen</td>
                <td>Nieuwe order wordt aangemaakt en opgeslagen in het systeem</td>
            </tr>
            <tr>
                <td class="use-case-name">Order details bekijken</td>
                <td class="actor">Manager Logistiek</td>
                <td>Orders zijn aanwezig in het systeem</td>
                <td>1) Gebruiker navigeert naar Order Detail screen<br>2) Gebruiker selecteert een order uit de lijst<br>3) Het systeem toont alle orderdetails inclusief klant, producten en status<br>4) Gebruiker kan order wijzigen indien status dit toelaat</td>
                <td>[Order niet gevonden] Melding dat geselecteerde order niet bestaat<br>[Order niet wijzigbaar] Status van order staat wijzigingen niet toe</td>
                <td>Orderdetails worden volledig weergegeven met mogelijkheid tot wijziging</td>
            </tr>
            <tr>
                <td class="use-case-name">Order items beheren</td>
                <td class="actor">Productiemedewerker</td>
                <td>Order is geselecteerd en bevat items</td>
                <td>1) Gebruiker opent Order Item screen<br>2) Het systeem toont alle items in de geselecteerde order<br>3) Gebruiker kan hoeveelheden wijzigen van bestaande items<br>4) Gebruiker kan nieuwe items toevoegen of bestaande verwijderen</td>
                <td>[Minimum hoeveelheid] Hoeveelheid mag niet onder minimum komen<br>[Item niet beschikbaar] Geselecteerd product is niet meer beschikbaar</td>
                <td>Order items worden bijgewerkt volgens gebruikerswensen</td>
            </tr>
            <tr>
                <td class="use-case-name">Order geschiedenis bekijken</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Historische orders zijn opgeslagen in database</td>
                <td>1) Gebruiker navigeert naar Order History screen<br>2) Het systeem toont alle historische orders<br>3) Gebruiker kan orders filteren op datum, klant of status<br>4) Gebruiker selecteert order om details te bekijken</td>
                <td>[Geen historie beschikbaar] Melding dat er geen historische orders zijn<br>[Filter geeft geen resultaat] Geen orders voldoen aan filtercriteria</td>
                <td>Order geschiedenis wordt weergegeven met filtermogelijkheden</td>
            </tr>
            <tr>
                <td class="use-case-name">Order bevestigen</td>
                <td class="actor">Manager Logistiek</td>
                <td>Order is compleet en klaar voor bevestiging</td>
                <td>1) Gebruiker navigeert naar Order Confirmation screen<br>2) Het systeem toont order samenvatting voor bevestiging<br>3) Gebruiker controleert alle details en bevestigt order<br>4) Het systeem wijzigt orderstatus naar 'bevestigd' en verstuurt bevestiging</td>
                <td>[Order incompleet] Melding dat order niet alle vereiste gegevens bevat<br>[Bevestiging mislukt] Technische fout bij versturen bevestiging</td>
                <td>Order wordt bevestigd en bevestigingsmail wordt verzonden</td>
            </tr>
            <tr>
                <td class="use-case-name">App instellingen beheren</td>
                <td class="actor">Manager bedrijfsvoering</td>
                <td>Gebruiker heeft beheerrechten voor instellingen</td>
                <td>1) Gebruiker navigeert naar Settings screen<br>2) Het systeem toont alle beschikbare instellingen<br>3) Gebruiker wijzigt gewenste instellingen zoals profiel, voorkeuren<br>4) Gebruiker kan uitloggen via settings menu</td>
                <td>[Geen beheerrechten] Gebruiker kan bepaalde instellingen niet wijzigen<br>[Instellingen niet opgeslagen] Fout bij opslaan van wijzigingen</td>
                <td>App instellingen worden bijgewerkt en opgeslagen</td>
            </tr>
        </tbody>
    </table>
</body>
</html>
