# PowerShell script om HTML te converteren naar Word

# Word COM object aanmaken
$word = New-Object -ComObject Word.Application
$word.Visible = $true

# Paden definiëren
$htmlPath = Join-Path $PSScriptRoot "argumenten.html"
$outputPath = Join-Path $PSScriptRoot "Argumentatieschema_Bol_Data_Coaching_Nieuw.docx"

# HTML openen in Word
$document = $word.Documents.Open($htmlPath)

# Opslaan als Word-document
$document.SaveAs2($outputPath, 16) # 16 = wdFormatDocumentDefault

# Document sluiten
$document.Close()
$word.Quit()

# COM objecten vrijgeven
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($document) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "HTML is succesvol geconverteerd naar Word: $outputPath"
